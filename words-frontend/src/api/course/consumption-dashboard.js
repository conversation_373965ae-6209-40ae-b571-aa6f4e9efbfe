import request from '@/utils/request'

// 获取课消看板数据
export function getDashboardData(data) {
  return request({
    url: '/course-consumption/dashboard/data',
    method: 'post',
    data: data
  })
}

// 获取快捷时间范围选项
export function getTimeRanges() {
  return request({
    url: '/course-consumption/dashboard/time-ranges',
    method: 'get'
  })
}

// 导出课消数据
export function exportDashboardData(data) {
  return request({
    url: '/course-consumption/dashboard/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

<template>
  <div class="course-consumption-dashboard-index">
    <div class="loading-container" v-loading="loading">
      <div class="loading-content">
        <h2>正在跳转到课消看板...</h2>
        <p>根据您的角色权限自动跳转到对应的看板页面</p>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'pinia'
import useUserStore from '@/store/modules/user'

export default {
  name: 'CourseConsumptionDashboardIndex',
  data() {
    return {
      loading: true
    }
  },
  computed: {
    ...mapState(useUserStore, ['roles'])
  },
  mounted() {
    this.redirectToDashboard()
  },
  methods: {
    redirectToDashboard() {
      const roles = this.roles || []
      
      // 根据角色优先级进行跳转
      if (this.hasRole(roles, ['admin', 'hr'])) {
        // 管理员和HR跳转到管理看板
        this.$router.replace('/dashboard/course-consumption/admin')
      } else if (this.hasRole(roles, ['teaching_group_leader', 'teaching_group_admin'])) {
        // 教学组长跳转到组长看板
        this.$router.replace('/dashboard/course-consumption/group-leader')
      } else if (this.hasRole(roles, ['teacher'])) {
        // 老师跳转到老师看板
        this.$router.replace('/dashboard/course-consumption/teacher')
      } else {
        // 没有权限，显示提示信息
        this.loading = false
        this.$modal.msgError('您没有权限访问课消看板')
        this.$router.replace('/')
      }
    },
    
    hasRole(userRoles, targetRoles) {
      return targetRoles.some(role => userRoles.includes(role))
    }
  }
}
</script>

<style scoped>
.course-consumption-dashboard-index {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.loading-container {
  width: 400px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
}

.loading-content h2 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.loading-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}
</style>

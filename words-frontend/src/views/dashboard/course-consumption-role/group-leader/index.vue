<template>
  <div class="group-leader-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>组长课消看板</h2>
      <p class="page-description">查看本组老师和学生的课程消费统计数据</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <TimeRangeSelector 
          v-model="timeRange" 
          @change="handleTimeRangeChange"
        />
        
        <el-form-item label="查看老师">
          <el-select 
            v-model="queryParams.teacherId" 
            placeholder="选择老师"
            clearable
            style="width: 150px;"
            @change="handleTeacherChange"
          >
            <el-option 
              v-for="teacher in teacherOptions" 
              :key="teacher.value"
              :label="teacher.label" 
              :value="teacher.value" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="学科">
          <el-select 
            v-model="queryParams.subject" 
            placeholder="选择学科"
            clearable
            style="width: 120px;"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 统计卡片 -->
    <div class="stats-cards" v-loading="loading">
      <el-row :gutter="20">
        <el-col :span="6">
          <StatisticsCard
            :title="currentViewTitle + '老师数'"
            :value="currentStats?.totalTeachers || 0"
            unit="人"
            :icon="User"
            icon-color="#409EFF"
            :extra-info="[
              { label: '活跃老师', value: currentStats?.activeTeachers || 0, unit: '人' }
            ]"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            :title="currentViewTitle + '学生数'"
            :value="currentStats?.totalStudents || 0"
            unit="人"
            :icon="UserFilled"
            icon-color="#67C23A"
            :extra-info="[
              { label: '活跃学生', value: currentStats?.activeStudents || 0, unit: '人' }
            ]"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="总课消课时"
            :value="currentStats?.totalConsumption || 0"
            unit="课时"
            :icon="Clock"
            icon-color="#E6A23C"
            :extra-info="[
              { label: '师均周课消', value: currentStats?.avgWeeklyConsumptionPerTeacher || 0, unit: '课时' }
            ]"
          />
        </el-col>
        <el-col :span="6">
          <StatisticsCard
            title="生均周课消"
            :value="currentStats?.avgWeeklyConsumptionPerStudent || 0"
            unit="课时/周"
            :icon="TrendCharts"
            icon-color="#F56C6C"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 课消趋势图 -->
    <div class="chart-section">
      <ConsumptionTrendChart
        :title="trendChartTitle"
        :data="dashboardData?.weeklyTrends || []"
        :loading="loading"
        height="400px"
      />
    </div>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">{{ tableTitle }}</span>
          <div class="card-actions">
            <el-button 
              v-if="!queryParams.teacherId"
              type="success" 
              icon="View" 
              size="small" 
              @click="switchToTeacherView"
            >
              查看老师详情
            </el-button>
            <el-button 
              v-else
              type="info" 
              icon="Back" 
              size="small" 
              @click="backToGroupView"
            >
              返回组视图
            </el-button>
            <el-button type="primary" icon="Download" size="small" @click="exportData">
              导出数据
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 老师统计表格 -->
      <el-table 
        v-if="!queryParams.teacherId"
        :data="dashboardData?.teacherStats || []" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'totalConsumption', order: 'descending' }"
        @row-click="viewTeacherDetail"
      >
        <el-table-column prop="teacherName" label="老师姓名" width="120" fixed="left" />
        <el-table-column prop="studentCount" label="学生数" width="100" sortable />
        <el-table-column prop="activeStudents" label="活跃学生" width="100" sortable />
        <el-table-column prop="totalConsumption" label="总课消" width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消" width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消" width="120" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="consumptionRate" label="课消率" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.consumptionRate) }}%
          </template>
        </el-table-column>
        <el-table-column prop="lastConsumptionTime" label="最后课消时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="viewTeacherDetail(row)"
            >
              查看学生
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 学生统计表格 -->
      <el-table 
        v-else
        :data="dashboardData?.studentStats || []" 
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'totalConsumption', order: 'descending' }"
      >
        <el-table-column prop="studentName" label="学生姓名" width="120" fixed="left" />
        <el-table-column prop="studentPhone" label="手机号" width="130" />
        <el-table-column prop="totalConsumption" label="总课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.weeklyConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="monthlyConsumption" label="月课消" width="100" sortable>
          <template #default="{ row }">
            {{ formatNumber(row.monthlyConsumption) }}课时
          </template>
        </el-table-column>
        <el-table-column prop="consumptionCount" label="课消次数" width="100" sortable />
        <el-table-column prop="lastConsumptionTime" label="最后课消时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学科分布" min-width="200">
          <template #default="{ row }">
            <div class="subject-tags">
              <el-tag 
                v-for="subject in row.subjectConsumptions" 
                :key="subject.subject"
                size="small"
                class="subject-tag"
              >
                {{ subject.subject }}: {{ formatNumber(subject.consumption) }}课时
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewStudentDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 学生详情对话框 -->
    <StudentDetailDialog
      v-model="studentDetailVisible"
      :student-data="selectedStudent"
      @close="studentDetailVisible = false"
    />
  </div>
</template>

<script>
import { getGroupLeaderDashboardData, getTeacherSelectorOptions } from '@/api/dashboard/course-consumption-role'
import { formatDateTime } from '@/utils/date'
import { User, UserFilled, Clock, TrendCharts, Search, Refresh, Download, View, Back } from '@element-plus/icons-vue'

// 导入共享组件
import TimeRangeSelector from '../shared/TimeRangeSelector.vue'
import StatisticsCard from '../shared/StatisticsCard.vue'
import ConsumptionTrendChart from '../shared/ConsumptionTrendChart.vue'
import StudentDetailDialog from '../teacher/components/StudentDetailDialog.vue'

export default {
  name: 'GroupLeaderDashboard',
  components: {
    User, UserFilled, Clock, TrendCharts, Search, Refresh, Download, View, Back,
    TimeRangeSelector,
    StatisticsCard,
    ConsumptionTrendChart,
    StudentDetailDialog
  },
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        startDate: '',
        endDate: '',
        timeRangeType: 'thisWeek',
        subject: '',
        groupId: '',
        teacherId: ''
      },

      // 时间范围
      timeRange: {
        timeRangeType: 'thisWeek',
        startDate: '',
        endDate: ''
      },

      // 看板数据
      dashboardData: null,

      // 老师选项
      teacherOptions: [],

      // 学生详情对话框
      studentDetailVisible: false,
      selectedStudent: null
    }
  },

  computed: {
    currentViewTitle() {
      return this.queryParams.teacherId ? '该老师' : '本组'
    },
    
    currentStats() {
      if (this.queryParams.teacherId) {
        // 查看特定老师时，统计数据需要从学生数据计算
        const studentStats = this.dashboardData?.studentStats || []
        return {
          totalTeachers: 1,
          activeTeachers: 1,
          totalStudents: studentStats.length,
          activeStudents: studentStats.length,
          totalConsumption: studentStats.reduce((sum, student) => sum + (student.totalConsumption || 0), 0),
          avgWeeklyConsumptionPerTeacher: studentStats.reduce((sum, student) => sum + (student.weeklyConsumption || 0), 0),
          avgWeeklyConsumptionPerStudent: studentStats.length > 0 ? 
            studentStats.reduce((sum, student) => sum + (student.weeklyConsumption || 0), 0) / studentStats.length : 0
        }
      } else {
        return this.dashboardData?.groupStats
      }
    },
    
    trendChartTitle() {
      if (this.queryParams.teacherId) {
        const currentTeacher = this.dashboardData?.currentTeacher
        return `${currentTeacher?.teacherName || '老师'}的周课消趋势`
      }
      return '本组周课消趋势'
    },
    
    tableTitle() {
      if (this.queryParams.teacherId) {
        const currentTeacher = this.dashboardData?.currentTeacher
        return `${currentTeacher?.teacherName || '老师'}的学生课消详情`
      }
      return '本组老师课消统计'
    }
  },

  created() {
    this.loadTeacherOptions()
    this.getList()
  },

  methods: {
    /** 加载老师选项 */
    async loadTeacherOptions() {
      try {
        const response = await getTeacherSelectorOptions()
        if (response.code === 200) {
          this.teacherOptions = response.data.teachers || []
        }
      } catch (error) {
        console.error('获取老师选项失败:', error)
      }
    },

    /** 获取看板数据 */
    async getList() {
      this.loading = true
      try {
        const response = await getGroupLeaderDashboardData(this.queryParams)
        if (response.code === 200) {
          this.dashboardData = response.data
        } else {
          this.$modal.msgError(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取组长看板数据失败:', error)
        this.$modal.msgError('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    /** 时间范围变化处理 */
    handleTimeRangeChange(timeRange) {
      this.queryParams.startDate = timeRange.startDate
      this.queryParams.endDate = timeRange.endDate
      this.queryParams.timeRangeType = timeRange.timeRangeType
      this.getList()
    },

    /** 老师变化处理 */
    handleTeacherChange() {
      this.getList()
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.subject = ''
      this.queryParams.teacherId = ''
      this.timeRange = {
        timeRangeType: 'thisWeek',
        startDate: '',
        endDate: ''
      }
      this.handleQuery()
    },

    /** 查看老师详情 */
    viewTeacherDetail(teacher) {
      this.queryParams.teacherId = teacher.teacherId
      this.getList()
    },

    /** 切换到老师视图 */
    switchToTeacherView() {
      if (this.teacherOptions.length > 0) {
        this.queryParams.teacherId = this.teacherOptions[0].value
        this.getList()
      }
    },

    /** 返回组视图 */
    backToGroupView() {
      this.queryParams.teacherId = ''
      this.getList()
    },

    /** 查看学生详情 */
    viewStudentDetail(student) {
      this.selectedStudent = student
      this.studentDetailVisible = true
    },

    /** 导出数据 */
    exportData() {
      this.$modal.msgInfo('导出功能开发中...')
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined) return '0'
      const num = Number(value)
      if (isNaN(num)) return value
      return num % 1 === 0 ? num.toString() : num.toFixed(2)
    },

    /** 格式化日期时间 */
    formatDateTime(dateTime) {
      return formatDateTime(dateTime)
    }
  }
}
</script>

<style scoped>
.group-leader-dashboard {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-cards {
  margin-bottom: 20px;
}

.chart-section {
  margin-bottom: 20px;
}

.table-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.subject-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.subject-tag {
  margin: 0;
}

.el-table__row {
  cursor: pointer;
}

.el-table__row:hover {
  background-color: #f5f7fa;
}
</style>

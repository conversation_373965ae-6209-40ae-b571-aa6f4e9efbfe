# 学生课消可视化看板

## 📊 功能概述

学生课消可视化看板是一个基于 ECharts 的数据可视化平台，提供多维度的课程消费数据分析和展示。

## 🎯 核心特性

### 📈 可视化图表
- **课消趋势图** - 显示学生课消随时间的变化趋势
- **课消分布图** - 按课消范围统计学生分布情况
- **课消对比图** - 对比不同学生的课消数据
- **学科分布图** - 展示各学科的课消占比
- **课型分布图** - 展示各课型的课消统计

### 🏆 排行榜系统
- **学生排行榜** - 按总课消排序的学生榜单
- **教师排行榜** - 按生均课消排序的教师榜单  
- **销售排行榜** - 按课消率排序的销售榜单

### 📋 统计卡片
- **总学生数** - 显示总学生数和活跃学生数
- **总课消课时** - 显示总课消和课消率
- **平均周课消** - 显示平均周课消数据
- **平均月课消** - 显示平均月课消数据

### 🔍 详情对话框
- **学生详情** - 查看学生课消详细信息
- **课消详情** - 查看课消统计详细分析
- **趋势详情** - 查看课消趋势详细图表

## 🏗️ 技术架构

### 前端技术栈
- **Vue 3** - 响应式框架
- **Element Plus** - UI 组件库
- **ECharts** - 数据可视化图表库
- **SCSS** - CSS 预处理器

### 组件结构
```
course-consumption-dashboard/
├── index.vue                           # 主页面
├── components/
│   ├── ConsumptionTrendChart.vue       # 课消趋势图
│   ├── ConsumptionDistributionChart.vue # 课消分布图
│   ├── ConsumptionComparisonChart.vue   # 课消对比图
│   ├── SubjectDistributionChart.vue     # 学科分布图
│   ├── SpecificationDistributionChart.vue # 课型分布图
│   ├── StudentRankingList.vue          # 学生排行榜
│   ├── TeacherRankingList.vue          # 教师排行榜
│   ├── SalesRankingList.vue            # 销售排行榜
│   ├── StudentDetailDialog.vue         # 学生详情对话框
│   ├── ConsumptionDetailDialog.vue     # 课消详情对话框
│   └── TrendDetailDialog.vue           # 趋势详情对话框
└── README.md                           # 说明文档
```

## 🎨 界面设计

### 布局结构
1. **页面标题** - 功能说明和描述
2. **筛选条件** - 时间范围、学科、课型等过滤条件
3. **统计卡片** - 4个关键指标的可视化展示
4. **趋势图表** - 可切换的多种图表类型
5. **分布图表** - 学科和课型的分布统计
6. **排行榜** - 根据权限显示不同的排行榜

### 交互特性
- **响应式设计** - 适配PC和移动端
- **图表切换** - 支持趋势图、分布图、对比图切换
- **数据钻取** - 点击卡片查看详细信息
- **实时筛选** - 条件变更即时生效
- **排行榜展示** - 前三名特殊样式，奖牌图标

### 视觉效果
- **渐变背景** - 现代化的视觉效果
- **卡片阴影** - 悬浮效果和层次感
- **图表动画** - 平滑的数据加载动画
- **颜色主题** - 统一的配色方案

## 🔧 使用说明

### 基本操作
1. **选择时间范围** - 使用快捷按钮或日期选择器
2. **设置过滤条件** - 选择学科和课型进行筛选
3. **查看统计数据** - 观察统计卡片的关键指标
4. **切换图表类型** - 使用图表切换按钮查看不同视图
5. **查看排行榜** - 浏览学生、教师、销售排行榜
6. **查看详情** - 点击卡片或按钮查看详细信息

### 权限控制
- **管理员/HR** - 查看所有数据和图表
- **销售总监** - 查看所有销售相关数据
- **销售组长** - 查看本组销售数据
- **销售** - 查看自己的学生数据
- **教学组长** - 查看本组教师数据
- **教师** - 查看自己的学生数据

## 📊 数据说明

### 图表数据源
- **课消趋势** - 基于时间序列的课消累计数据
- **课消分布** - 按课消范围分组的学生统计
- **学科分布** - 各学科的课消总量统计
- **课型分布** - 各课型的课消总量统计

### 排行榜规则
- **学生排行** - 按总课消降序排列
- **教师排行** - 按生均周课消降序排列
- **销售排行** - 按总课消降序排列

### 统计指标
- **总课消** - 指定时间范围内的课消总和
- **周课消** - 总课消 ÷ 时间范围周数
- **月课消** - 总课消 ÷ 时间范围月数
- **课消率** - 活跃学生数 ÷ 总学生数

## 🚀 部署说明

### 依赖安装
```bash
# 安装 ECharts
pnpm add echarts

# 如果需要特定的图表类型，可以按需引入
```

### 路由配置
```javascript
// 在路由配置中添加
{
  path: '/dashboard/course-consumption',
  component: () => import('@/views/dashboard/course-consumption-dashboard/index.vue'),
  name: 'CourseConsumptionDashboard',
  meta: {
    title: '课消看板',
    icon: 'chart'
  }
}
```

### 权限配置
确保在菜单配置中设置正确的权限标识：
- 菜单权限：`course:consumption:dashboard:view`
- 导出权限：`course:consumption:dashboard:export`

## 🔧 自定义配置

### 图表主题
可以通过修改 ECharts 配置来自定义图表主题：

```javascript
// 在组件中自定义颜色
const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666']
```

### 响应式断点
可以通过修改 CSS 媒体查询来调整响应式断点：

```scss
@media (max-width: 1200px) {
  // 中等屏幕样式
}

@media (max-width: 768px) {
  // 小屏幕样式
}
```

## 🐛 常见问题

### 图表不显示
1. 检查 ECharts 是否正确安装
2. 确认容器元素有正确的宽高
3. 检查数据格式是否正确

### 权限问题
1. 确认用户角色配置正确
2. 检查菜单权限分配
3. 验证后端数据权限过滤

### 性能问题
1. 大数据量时考虑分页或虚拟滚动
2. 图表数据过多时考虑数据采样
3. 使用防抖处理频繁的筛选操作

## 📈 未来规划

### 短期优化
- 添加更多图表类型（雷达图、热力图等）
- 支持图表数据导出
- 增加数据刷新功能

### 长期规划
- 支持自定义看板配置
- 添加数据预警功能
- 集成实时数据推送

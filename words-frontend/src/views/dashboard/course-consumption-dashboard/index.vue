<template>
  <div class="course-consumption-dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学生课消看板</h2>
      <p class="page-description">查看学生课程消费统计数据，支持多维度分析和可视化展示</p>
    </div>

    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="queryForm" inline size="default">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="onDateRangeChange"
            style="width: 280px"
          />
        </el-form-item>

        <el-form-item label="快捷选择">
          <el-button-group>
            <el-button
              v-for="option in quickDateOptions"
              :key="option.value"
              :type="selectedQuickDate === option.value ? 'primary' : 'default'"
              size="default"
              @click="selectQuickDate(option.value)"
            >
              {{ option.label }}
            </el-button>
          </el-button-group>
        </el-form-item>

        <el-form-item label="学科">
          <el-select
            v-model="queryForm.subject"
            placeholder="全部学科"
            clearable
            style="width: 120px"
          >
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>

        <el-form-item label="课型">
          <el-select
            v-model="queryForm.specification"
            placeholder="全部课型"
            clearable
            style="width: 140px"
          >
            <el-option label="单词课" value="单词课" />
            <el-option label="音标拼读课" value="音标拼读课" />
            <el-option label="语法课" value="语法课" />
            <el-option label="题型课" value="题型课" />
            <el-option label="听说课" value="听说课" />
            <el-option label="通用课（非英语）" value="通用课（非英语）" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="loadDashboard" :loading="loading">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="resetQuery">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="exportData" :loading="exporting">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总体统计卡片 -->
    <div class="stats-cards" v-if="dashboardData">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card" @click="showStudentDetail">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.totalStudents || 0 }}</div>
              <div class="stat-label">总学生数</div>
              <div class="stat-trend">
                <span class="trend-text">活跃: {{ dashboardData.overallStats.activeStudents || 0 }}</span>
              </div>
            </div>
            <el-icon class="stat-icon student-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" @click="showConsumptionDetail">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.totalConsumption) }}</div>
              <div class="stat-label">总课消课时</div>
              <div class="stat-trend">
                <span class="trend-text">课消率: {{ formatNumber(dashboardData.overallStats.consumptionRate) }}%</span>
              </div>
            </div>
            <el-icon class="stat-icon consumption-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" @click="showWeeklyTrend">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.avgWeeklyConsumption) }}</div>
              <div class="stat-label">平均周课消</div>
              <div class="stat-trend">
                <span class="trend-text">{{ timeRangeText }}</span>
              </div>
            </div>
            <el-icon class="stat-icon weekly-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card" @click="showMonthlyTrend">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.avgMonthlyConsumption) }}</div>
              <div class="stat-label">平均月课消</div>
              <div class="stat-trend">
                <span class="trend-text">{{ dashboardData.timeRangeInfo.totalDays }} 天</span>
              </div>
            </div>
            <el-icon class="stat-icon monthly-icon"><DataAnalysis /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 课消趋势图表 -->
    <el-card class="chart-card" v-if="dashboardData">
      <template #header>
        <div class="card-header">
          <span>课消趋势分析</span>
          <div class="header-actions">
            <el-button-group>
              <el-button 
                :type="chartType === 'trend' ? 'primary' : 'default'" 
                size="small"
                @click="chartType = 'trend'"
              >
                趋势图
              </el-button>
              <el-button 
                :type="chartType === 'distribution' ? 'primary' : 'default'" 
                size="small"
                @click="chartType = 'distribution'"
              >
                分布图
              </el-button>
              <el-button 
                :type="chartType === 'comparison' ? 'primary' : 'default'" 
                size="small"
                @click="chartType = 'comparison'"
              >
                对比图
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>
      <div class="chart-container">
        <ConsumptionTrendChart 
          v-if="chartType === 'trend'"
          :data="dashboardData" 
          :loading="loading"
        />
        <ConsumptionDistributionChart 
          v-if="chartType === 'distribution'"
          :data="dashboardData" 
          :loading="loading"
        />
        <ConsumptionComparisonChart 
          v-if="chartType === 'comparison'"
          :data="dashboardData" 
          :loading="loading"
        />
      </div>
    </el-card>

    <!-- 学科课消分布 -->
    <el-row :gutter="20" v-if="dashboardData">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>学科课消分布</span>
              <el-button type="text" @click="showSubjectDetail">
                <el-icon><View /></el-icon>
                详情
              </el-button>
            </div>
          </template>
          <SubjectDistributionChart :data="dashboardData" :loading="loading" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>课型课消分布</span>
              <el-button type="text" @click="showSpecificationDetail">
                <el-icon><View /></el-icon>
                详情
              </el-button>
            </div>
          </template>
          <SpecificationDistributionChart :data="dashboardData" :loading="loading" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜 -->
    <el-row :gutter="20" v-if="dashboardData">
      <el-col :span="8">
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>学生课消排行榜</span>
              <el-button type="text" @click="showStudentRanking">
                <el-icon><Trophy /></el-icon>
                更多
              </el-button>
            </div>
          </template>
          <StudentRankingList :data="dashboardData.studentStats" :loading="loading" />
        </el-card>
      </el-col>
      <el-col :span="8" v-if="showTeacherStats">
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>教师课消排行榜</span>
              <el-button type="text" @click="showTeacherRanking">
                <el-icon><Trophy /></el-icon>
                更多
              </el-button>
            </div>
          </template>
          <TeacherRankingList :data="dashboardData.teacherStats" :loading="loading" />
        </el-card>
      </el-col>
      <el-col :span="8" v-if="showSalesStats">
        <el-card class="ranking-card">
          <template #header>
            <div class="card-header">
              <span>销售课消排行榜</span>
              <el-button type="text" @click="showSalesRanking">
                <el-icon><Trophy /></el-icon>
                更多
              </el-button>
            </div>
          </template>
          <SalesRankingList :data="dashboardData.salesStats" :loading="loading" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 详情对话框 -->
    <StudentDetailDialog 
      v-model="studentDetailVisible" 
      :data="dashboardData"
    />
    <ConsumptionDetailDialog 
      v-model="consumptionDetailVisible" 
      :data="dashboardData"
    />
    <TrendDetailDialog 
      v-model="trendDetailVisible" 
      :data="dashboardData"
      :type="trendType"
    />
  </div>
</template>

<script name="course-consumption">
import { getDashboardData, getTimeRanges, exportDashboardData } from '@/api/course/consumption-dashboard'
import {
  User, Clock, TrendCharts, DataAnalysis, Search, Refresh, Download,
  View, Trophy
} from '@element-plus/icons-vue'

// 导入图表组件
import ConsumptionTrendChart from './components/ConsumptionTrendChart.vue'
import ConsumptionDistributionChart from './components/ConsumptionDistributionChart.vue'
import ConsumptionComparisonChart from './components/ConsumptionComparisonChart.vue'
import SubjectDistributionChart from './components/SubjectDistributionChart.vue'
import SpecificationDistributionChart from './components/SpecificationDistributionChart.vue'
import StudentRankingList from './components/StudentRankingList.vue'
import TeacherRankingList from './components/TeacherRankingList.vue'
import SalesRankingList from './components/SalesRankingList.vue'

// 导入对话框组件
import StudentDetailDialog from './components/StudentDetailDialog.vue'
import ConsumptionDetailDialog from './components/ConsumptionDetailDialog.vue'
import TrendDetailDialog from './components/TrendDetailDialog.vue'

export default {
  name: 'CourseConsumptionDashboard',
  components: {
    User, Clock, TrendCharts, DataAnalysis, Search, Refresh, Download, View, Trophy,
    ConsumptionTrendChart,
    ConsumptionDistributionChart,
    ConsumptionComparisonChart,
    SubjectDistributionChart,
    SpecificationDistributionChart,
    StudentRankingList,
    TeacherRankingList,
    SalesRankingList,
    StudentDetailDialog,
    ConsumptionDetailDialog,
    TrendDetailDialog
  },
  data() {
    return {
      // 加载状态
      loading: false,
      exporting: false,

      // 查询参数
      queryForm: {
        startDate: '',
        endDate: '',
        timeRangeType: 'thisWeek',
        subject: '',
        specification: ''
      },

      // 日期范围
      dateRange: [],
      selectedQuickDate: 'thisWeek',

      // 快捷日期选项
      quickDateOptions: [
        { label: '本周', value: 'thisWeek' },
        { label: '上周', value: 'lastWeek' },
        { label: '本月', value: 'thisMonth' },
        { label: '上月', value: 'lastMonth' }
      ],

      // 看板数据
      dashboardData: null,

      // 图表类型
      chartType: 'trend',

      // 对话框显示状态
      studentDetailVisible: false,
      consumptionDetailVisible: false,
      trendDetailVisible: false,
      trendType: 'weekly',

      // 时间范围选项
      timeRanges: {}
    }
  },

  computed: {
    // 时间范围文本
    timeRangeText() {
      if (!this.dashboardData?.timeRangeInfo) return ''
      return this.dashboardData.timeRangeInfo.description
    },

    // 是否显示教师统计
    showTeacherStats() {
      return this.dashboardData?.teacherStats && this.dashboardData.teacherStats.length > 0
    },

    // 是否显示销售统计
    showSalesStats() {
      return this.dashboardData?.salesStats && this.dashboardData.salesStats.length > 0
    }
  },

  created() {
    this.initTimeRange()
    this.loadDashboard()
  },

  methods: {
    /** 初始化时间范围 */
    async initTimeRange() {
      try {
        const response = await getTimeRanges()
        if (response.code === 200) {
          this.timeRanges = response.data
          // 设置默认为本周
          if (this.timeRanges.thisWeek) {
            this.queryForm.startDate = this.timeRanges.thisWeek.startDate
            this.queryForm.endDate = this.timeRanges.thisWeek.endDate
            this.dateRange = [this.queryForm.startDate, this.queryForm.endDate]
          }
        } else {
          // 如果接口失败，使用本地计算的时间范围
          this.initLocalTimeRanges()
        }
      } catch (error) {
        console.error('获取时间范围失败:', error)
        // 接口失败时使用本地计算
        this.initLocalTimeRanges()
      }
    },

    /** 初始化本地时间范围 */
    initLocalTimeRanges() {
      const today = new Date()
      const formatter = (date) => date.toISOString().split('T')[0]

      // 本周（周一到周日）
      const thisWeekStart = new Date(today)
      thisWeekStart.setDate(today.getDate() - today.getDay() + 1)
      const thisWeekEnd = new Date(thisWeekStart)
      thisWeekEnd.setDate(thisWeekStart.getDate() + 6)

      // 上周
      const lastWeekStart = new Date(thisWeekStart)
      lastWeekStart.setDate(thisWeekStart.getDate() - 7)
      const lastWeekEnd = new Date(lastWeekStart)
      lastWeekEnd.setDate(lastWeekStart.getDate() + 6)

      // 本月
      const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)

      // 上月
      const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)

      this.timeRanges = {
        thisWeek: {
          startDate: formatter(thisWeekStart),
          endDate: formatter(thisWeekEnd),
          label: '本周'
        },
        lastWeek: {
          startDate: formatter(lastWeekStart),
          endDate: formatter(lastWeekEnd),
          label: '上周'
        },
        thisMonth: {
          startDate: formatter(thisMonthStart),
          endDate: formatter(thisMonthEnd),
          label: '本月'
        },
        lastMonth: {
          startDate: formatter(lastMonthStart),
          endDate: formatter(lastMonthEnd),
          label: '上月'
        }
      }

      // 设置默认为本周
      this.queryForm.startDate = this.timeRanges.thisWeek.startDate
      this.queryForm.endDate = this.timeRanges.thisWeek.endDate
      this.dateRange = [this.queryForm.startDate, this.queryForm.endDate]
    },

    /** 加载看板数据 */
    async loadDashboard() {
      // 检查时间范围是否过大
      if (!this.validateTimeRange()) {
        return
      }

      this.loading = true
      const startTime = Date.now()

      try {
        console.log('开始加载看板数据:', this.queryForm)

        // 设置超时时间为30秒
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), 30000)
        })

        const dataPromise = getDashboardData(this.queryForm)

        const response = await Promise.race([dataPromise, timeoutPromise])

        const endTime = Date.now()
        console.log(`数据加载完成，耗时: ${endTime - startTime}ms`)

        if (response.code === 200) {
          this.dashboardData = response.data
          if (endTime - startTime > 5000) {
            this.$message.warning(`数据加载完成，耗时较长 ${Math.round((endTime - startTime) / 1000)}秒，建议缩小查询范围`)
          } else {
            this.$message.success(`数据加载成功`)
          }
        } else {
          this.$modal.msgError(response.msg || '获取数据失败')
        }
      } catch (error) {
        const endTime = Date.now()
        console.error('获取看板数据失败:', error)

        if (error.message === '请求超时') {
          this.$modal.msgError(`请求超时（${Math.round((endTime - startTime) / 1000)}秒），请尝试缩小查询范围或联系管理员`)
        } else if (error.code === 'ECONNABORTED') {
          this.$modal.msgError('网络连接超时，请检查网络连接')
        } else {
          this.$modal.msgError('获取数据失败: ' + (error.message || '未知错误'))
        }
      } finally {
        this.loading = false
      }
    },

    /** 验证时间范围 */
    validateTimeRange() {
      if (!this.queryForm.startDate || !this.queryForm.endDate) {
        this.$modal.msgError('请选择查询时间范围')
        return false
      }

      const startDate = new Date(this.queryForm.startDate)
      const endDate = new Date(this.queryForm.endDate)
      const daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))

      if (daysDiff > 365) {
        this.$modal.msgError('查询时间范围不能超过365天，请缩小查询范围')
        return false
      }

      if (daysDiff > 90) {
        return new Promise((resolve) => {
          this.$modal.confirm(
            `查询时间范围为${daysDiff}天，数据量较大可能导致查询缓慢，是否继续？`,
            '提示',
            {
              confirmButtonText: '继续查询',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            resolve(true)
          }).catch(() => {
            resolve(false)
          })
        })
      }

      return true
    },

    /** 选择快捷日期 */
    selectQuickDate(value) {
      this.selectedQuickDate = value
      this.queryForm.timeRangeType = value

      // 如果没有预设的时间范围，重新计算
      if (!this.timeRanges[value]) {
        this.initLocalTimeRanges()
      }

      if (this.timeRanges[value]) {
        const range = this.timeRanges[value]
        this.queryForm.startDate = range.startDate
        this.queryForm.endDate = range.endDate
        this.dateRange = [range.startDate, range.endDate]

        console.log('快捷选择时间:', {
          type: value,
          startDate: range.startDate,
          endDate: range.endDate
        })

        this.loadDashboard()
      }
    },

    /** 日期范围改变 */
    onDateRangeChange(value) {
      if (value && value.length === 2) {
        this.queryForm.startDate = value[0]
        this.queryForm.endDate = value[1]
        this.queryForm.timeRangeType = 'custom'
        this.selectedQuickDate = ''
        this.loadDashboard()
      }
    },

    /** 重置查询 */
    resetQuery() {
      this.queryForm = {
        startDate: '',
        endDate: '',
        timeRangeType: 'thisWeek',
        subject: '',
        specification: ''
      }
      this.selectedQuickDate = 'thisWeek'
      this.initTimeRange()
      this.loadDashboard()
    },

    /** 导出数据 */
    async exportData() {
      try {
        this.exporting = true
        this.$modal.loading('正在导出数据，请稍候...')
        const response = await exportDashboardData(this.queryForm)
        this.$download.blob(response, '课消看板数据.xlsx')
      } catch (error) {
        console.error('导出失败:', error)
        this.$modal.msgError('导出失败')
      } finally {
        this.exporting = false
        this.$modal.closeLoading()
      }
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0'
      }
      return num.toFixed(2)
    },

    // 显示详情对话框的方法
    showStudentDetail() {
      this.studentDetailVisible = true
    },

    showConsumptionDetail() {
      this.consumptionDetailVisible = true
    },

    showWeeklyTrend() {
      this.trendType = 'weekly'
      this.trendDetailVisible = true
    },

    showMonthlyTrend() {
      this.trendType = 'monthly'
      this.trendDetailVisible = true
    },

    showSubjectDetail() {
      // 可以跳转到详细页面或显示对话框
      console.log('显示学科详情')
    },

    showSpecificationDetail() {
      // 可以跳转到详细页面或显示对话框
      console.log('显示课型详情')
    },

    showStudentRanking() {
      // 可以跳转到详细页面或显示对话框
      console.log('显示学生排行榜')
    },

    showTeacherRanking() {
      // 可以跳转到详细页面或显示对话框
      console.log('显示教师排行榜')
    },

    showSalesRanking() {
      // 可以跳转到详细页面或显示对话框
      console.log('显示销售排行榜')
    }
  }
}
</script>

<style lang="scss" scoped>
.course-consumption-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 8px;

  :deep(.el-card__body) {
    padding: 20px;
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    :deep(.el-card__body) {
      padding: 24px;
      height: 120px;
      display: flex;
      align-items: center;
    }

    .stat-content {
      position: relative;
      z-index: 2;
      flex: 1;

      .stat-value {
        font-size: 32px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .stat-trend {
        .trend-text {
          font-size: 12px;
          color: #67C23A;
          background: rgba(103, 194, 58, 0.1);
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }

    .stat-icon {
      position: absolute;
      right: 24px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 48px;
      opacity: 0.2;
      z-index: 1;

      &.student-icon {
        color: #409EFF;
      }

      &.consumption-icon {
        color: #67C23A;
      }

      &.weekly-icon {
        color: #E6A23C;
      }

      &.monthly-icon {
        color: #F56C6C;
      }
    }
  }
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;

    .header-actions {
      .el-button {
        margin-left: 8px;

        &[type="text"] {
          padding: 0;
          border: none;
          background: none;
          color: #409EFF;

          &:hover {
            color: #66b1ff;
          }
        }
      }
    }
  }

  .chart-container {
    height: 400px;
    padding: 20px 0;
  }
}

.ranking-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #303133;

    .el-button {
      padding: 0;
      border: none;
      background: none;
      color: #409EFF;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .chart-container {
    height: 300px !important;
  }
}

@media (max-width: 768px) {
  .course-consumption-dashboard {
    padding: 10px;
  }

  .filter-card {
    :deep(.el-form--inline .el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 15px;
    }
  }

  .stats-cards {
    .el-col {
      margin-bottom: 15px;
    }

    .stat-card {
      :deep(.el-card__body) {
        height: 100px;
        padding: 16px;
      }

      .stat-content {
        .stat-value {
          font-size: 24px;
        }
      }

      .stat-icon {
        font-size: 36px;
        right: 16px;
      }
    }
  }

  .chart-container {
    height: 250px !important;
  }
}
</style>

<template>
  <el-dialog
    v-model="visible"
    title="课消详情"
    width="600px"
    :before-close="handleClose"
  >
    <div class="consumption-detail-content" v-if="data">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="总课消课时">
          {{ formatNumber(data.overallStats?.totalConsumption) }} 课时
        </el-descriptions-item>
        <el-descriptions-item label="平均周课消">
          {{ formatNumber(data.overallStats?.avgWeeklyConsumption) }} 课时/周
        </el-descriptions-item>
        <el-descriptions-item label="平均月课消">
          {{ formatNumber(data.overallStats?.avgMonthlyConsumption) }} 课时/月
        </el-descriptions-item>
        <el-descriptions-item label="课消率">
          {{ formatNumber(data.overallStats?.consumptionRate) }}%
        </el-descriptions-item>
        <el-descriptions-item label="统计时间范围">
          {{ data.timeRangeInfo?.startDate }} 至 {{ data.timeRangeInfo?.endDate }}
          （共 {{ data.timeRangeInfo?.totalDays }} 天）
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="consumption-analysis" style="margin-top: 20px;">
        <h4>课消分析</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-statistic title="总周数" :value="data.timeRangeInfo?.totalWeeks" suffix="周" />
          </el-col>
          <el-col :span="12">
            <el-statistic title="总月数" :value="data.timeRangeInfo?.totalMonths" suffix="月" />
          </el-col>
        </el-row>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'ConsumptionDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }
      return num.toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-detail-content {
  .consumption-analysis {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style>

<template>
  <div class="subject-distribution-chart">
    <div ref="chartContainer" class="chart-container" v-loading="loading"></div>
    <div v-if="!hasData && !loading" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SubjectDistributionChart',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    hasData() {
      return this.data?.studentStats && this.data.studentStats.length > 0
    },
    chartData() {
      if (!this.hasData) return null
      
      // 统计各学科的课消总量
      const subjectMap = new Map()
      
      this.data.studentStats.forEach(student => {
        if (student.subjectConsumptions && student.subjectConsumptions.length > 0) {
          student.subjectConsumptions.forEach(subject => {
            const current = subjectMap.get(subject.subject) || 0
            subjectMap.set(subject.subject, current + parseFloat(subject.consumption || 0))
          })
        }
      })
      
      return Array.from(subjectMap.entries()).map(([subject, consumption]) => ({
        name: subject,
        value: consumption
      })).sort((a, b) => b.value - a.value)
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.chartData || this.chartData.length === 0) return
      
      const option = {
        title: {
          text: '学科课消分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>课消: ${params.value.toFixed(2)} 课时<br/>占比: ${params.percent}%`
          }
        },
        legend: {
          top: 25,
          left: 'center'
        },
        series: [
          {
            name: '学科分布',
            type: 'pie',
            radius: ['30%', '60%'],
            center: ['50%', '60%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'outside',
              formatter: function(params) {
                return `${params.name}\n${params.value.toFixed(1)}课时`
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: this.chartData.map((item, index) => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: this.getSubjectColor(item.name, index)
              }
            }))
          }
        ]
      }
      
      this.chart.setOption(option, true)
    },
    
    getSubjectColor(subject, index) {
      const subjectColors = {
        '英语': '#5470c6',
        '语文': '#91cc75',
        '数学': '#fac858',
        '物理': '#ee6666',
        '化学': '#73c0de'
      }
      
      return subjectColors[subject] || this.getDefaultColor(index)
    },
    
    getDefaultColor(index) {
      const colors = [
        '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc',
        '#5470c6', '#91cc75', '#fac858', '#ee6666'
      ]
      return colors[index % colors.length]
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.subject-distribution-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 250px;
  }
}
</style>

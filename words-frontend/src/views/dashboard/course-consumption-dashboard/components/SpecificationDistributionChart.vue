<template>
  <div class="specification-distribution-chart">
    <div ref="chartContainer" class="chart-container" v-loading="loading"></div>
    <div v-if="!hasData && !loading" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'SpecificationDistributionChart',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    hasData() {
      return this.data?.studentStats && this.data.studentStats.length > 0
    },
    chartData() {
      if (!this.hasData) return null
      
      // 模拟课型分布数据（实际应该从后端获取）
      const specificationMap = new Map()
      const specifications = ['单词课', '音标拼读课', '语法课', '题型课', '听说课', '通用课（非英语）']
      
      // 模拟数据分布
      specifications.forEach((spec, index) => {
        const value = Math.random() * 100 + 20 // 随机生成20-120之间的值
        specificationMap.set(spec, value)
      })
      
      return Array.from(specificationMap.entries()).map(([spec, consumption]) => ({
        name: spec,
        value: consumption
      })).sort((a, b) => b.value - a.value)
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.chartData || this.chartData.length === 0) return
      
      const option = {
        title: {
          text: '课型课消分布',
          left: 'center',
          textStyle: {
            fontSize: 14,
            fontWeight: 'normal',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            return `${params[0].name}<br/>课消: ${params[0].value.toFixed(2)} 课时`
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map(item => item.name),
          axisLabel: {
            color: '#666',
            interval: 0,
            rotate: 30,
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '课消课时',
          nameTextStyle: {
            color: '#666',
            fontSize: 10
          },
          axisLabel: {
            color: '#666',
            formatter: '{value}',
            fontSize: 10
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: '课型分布',
            type: 'bar',
            data: this.chartData.map((item, index) => ({
              value: item.value,
              itemStyle: {
                color: this.getSpecificationColor(item.name, index)
              }
            })),
            barWidth: '60%',
            itemStyle: {
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }
      
      this.chart.setOption(option, true)
    },
    
    getSpecificationColor(specification, index) {
      const specificationColors = {
        '单词课': '#5470c6',
        '音标拼读课': '#91cc75',
        '语法课': '#fac858',
        '题型课': '#ee6666',
        '听说课': '#73c0de',
        '通用课（非英语）': '#3ba272'
      }
      
      return specificationColors[specification] || this.getDefaultColor(index)
    },
    
    getDefaultColor(index) {
      const colors = [
        '#fc8452', '#9a60b4', '#ea7ccc', '#5470c6',
        '#91cc75', '#fac858', '#ee6666', '#73c0de'
      ]
      return colors[index % colors.length]
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.specification-distribution-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 250px;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 250px;
  }
}
</style>

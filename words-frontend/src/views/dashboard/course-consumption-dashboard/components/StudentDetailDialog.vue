<template>
  <el-dialog
    v-model="visible"
    title="学生详情"
    width="800px"
    :before-close="handleClose"
  >
    <div class="student-detail-content" v-if="data">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="总学生数">
          {{ data.overallStats?.totalStudents || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="活跃学生数">
          {{ data.overallStats?.activeStudents || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="课消率">
          {{ formatNumber(data.overallStats?.consumptionRate) }}%
        </el-descriptions-item>
        <el-descriptions-item label="统计时间">
          {{ data.timeRangeInfo?.description }}
        </el-descriptions-item>
      </el-descriptions>
      
      <div class="student-list" style="margin-top: 20px;">
        <h4>学生课消详情</h4>
        <el-table
          :data="data.studentStats?.slice(0, 20) || []"
          stripe
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column prop="studentName" label="学生姓名" width="120" />
          <el-table-column prop="salesName" label="销售" width="100" />
          <el-table-column prop="totalConsumption" label="总课消" width="100">
            <template #default="{ row }">
              {{ formatNumber(row.totalConsumption) }}
            </template>
          </el-table-column>
          <el-table-column prop="weeklyConsumption" label="周课消" width="100">
            <template #default="{ row }">
              {{ formatNumber(row.weeklyConsumption) }}
            </template>
          </el-table-column>
          <el-table-column prop="monthlyConsumption" label="月课消" width="100">
            <template #default="{ row }">
              {{ formatNumber(row.monthlyConsumption) }}
            </template>
          </el-table-column>
          <el-table-column prop="consumptionCount" label="课消次数" width="100" />
        </el-table>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
export default {
  name: 'StudentDetailDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:modelValue'],
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }
      return num.toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
.student-detail-content {
  .student-list {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style>

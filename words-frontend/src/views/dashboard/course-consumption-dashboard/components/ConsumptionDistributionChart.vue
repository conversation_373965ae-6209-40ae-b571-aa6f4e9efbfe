<template>
  <div class="consumption-distribution-chart">
    <div ref="chartContainer" class="chart-container" v-loading="loading"></div>
    <div v-if="!hasData && !loading" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ConsumptionDistributionChart',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    hasData() {
      return this.data?.studentStats && this.data.studentStats.length > 0
    },
    chartData() {
      if (!this.hasData) return null
      
      // 按课消范围分组统计
      const ranges = [
        { name: '0-5课时', min: 0, max: 5, count: 0 },
        { name: '5-10课时', min: 5, max: 10, count: 0 },
        { name: '10-20课时', min: 10, max: 20, count: 0 },
        { name: '20-30课时', min: 20, max: 30, count: 0 },
        { name: '30-50课时', min: 30, max: 50, count: 0 },
        { name: '50+课时', min: 50, max: Infinity, count: 0 }
      ]
      
      this.data.studentStats.forEach(student => {
        const consumption = parseFloat(student.totalConsumption || 0)
        const range = ranges.find(r => consumption >= r.min && consumption < r.max)
        if (range) {
          range.count++
        }
      })
      
      return ranges.filter(r => r.count > 0)
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.chartData) return
      
      const option = {
        title: {
          text: '课消分布统计',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>学生数: ${params.value}<br/>占比: ${params.percent}%`
          }
        },
        legend: {
          top: 30,
          left: 'center'
        },
        series: [
          {
            name: '课消分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '60%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.chartData.map((item, index) => ({
              value: item.count,
              name: item.name,
              itemStyle: {
                color: this.getColor(index)
              }
            }))
          }
        ]
      }
      
      this.chart.setOption(option, true)
    },
    
    getColor(index) {
      const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666', 
        '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
      ]
      return colors[index % colors.length]
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-distribution-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
  }
}
</style>

<template>
  <div class="teacher-ranking-list">
    <div v-if="hasData" class="ranking-content">
      <div 
        v-for="(teacher, index) in topTeachers" 
        :key="teacher.teacherId"
        class="ranking-item"
        :class="getRankingClass(index)"
      >
        <div class="ranking-number">
          <span v-if="index < 3" class="medal">
            <el-icon v-if="index === 0" class="gold"><Trophy /></el-icon>
            <el-icon v-else-if="index === 1" class="silver"><Trophy /></el-icon>
            <el-icon v-else class="bronze"><Trophy /></el-icon>
          </span>
          <span v-else class="number">{{ index + 1 }}</span>
        </div>
        <div class="teacher-info">
          <div class="teacher-name">{{ teacher.teacherName }}</div>
          <div class="teacher-meta">
            <span class="group-name">{{ teacher.teachingGroupName }}</span>
            <span class="student-count">{{ teacher.studentCount }}名学生</span>
          </div>
        </div>
        <div class="consumption-value">
          <div class="total-value">
            <span class="value">{{ formatNumber(teacher.totalConsumption) }}</span>
            <span class="unit">课时</span>
          </div>
          <div class="avg-value">
            <span class="label">生均:</span>
            <span class="value">{{ formatNumber(teacher.avgWeeklyConsumptionPerStudent) }}</span>
            <span class="unit">周</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else-if="!loading" class="no-data">
      <el-empty description="暂无数据" :image-size="60" />
    </div>
    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>
  </div>
</template>

<script>
import { Trophy } from '@element-plus/icons-vue'

export default {
  name: 'TeacherRankingList',
  components: {
    Trophy
  },
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    hasData() {
      return this.data && this.data.length > 0
    },
    topTeachers() {
      return this.data.slice(0, 10) // 显示前10名
    }
  },
  methods: {
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }
      return num.toFixed(2)
    },
    
    getRankingClass(index) {
      if (index === 0) return 'first'
      if (index === 1) return 'second'
      if (index === 2) return 'third'
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
.teacher-ranking-list {
  height: 100%;
  
  .ranking-content {
    .ranking-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      margin-bottom: 8px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: #67C23A;
        box-shadow: 0 2px 8px rgba(103, 194, 58, 0.1);
      }
      
      &.first {
        background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
        border-color: #ffd700;
        
        .ranking-number .medal .gold {
          color: #ffd700;
        }
      }
      
      &.second {
        background: linear-gradient(135deg, #c0c0c0 0%, #e8e8e8 100%);
        border-color: #c0c0c0;
        
        .ranking-number .medal .silver {
          color: #c0c0c0;
        }
      }
      
      &.third {
        background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
        border-color: #cd7f32;
        
        .ranking-number .medal .bronze {
          color: #cd7f32;
        }
      }
      
      .ranking-number {
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        .medal {
          font-size: 20px;
        }
        
        .number {
          font-size: 16px;
          font-weight: 600;
          color: #666;
        }
      }
      
      .teacher-info {
        flex: 1;
        margin-left: 12px;
        
        .teacher-name {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .teacher-meta {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #909399;
          
          .group-name {
            &:before {
              content: "组: ";
            }
          }
        }
      }
      
      .consumption-value {
        text-align: right;
        
        .total-value {
          margin-bottom: 2px;
          
          .value {
            font-size: 16px;
            font-weight: 700;
            color: #67C23A;
          }
          
          .unit {
            font-size: 12px;
            color: #909399;
            margin-left: 2px;
          }
        }
        
        .avg-value {
          font-size: 11px;
          color: #909399;
          
          .label {
            margin-right: 2px;
          }
          
          .value {
            font-weight: 600;
            color: #E6A23C;
          }
          
          .unit {
            margin-left: 1px;
          }
        }
      }
    }
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }
  
  .loading {
    padding: 16px;
  }
}
</style>

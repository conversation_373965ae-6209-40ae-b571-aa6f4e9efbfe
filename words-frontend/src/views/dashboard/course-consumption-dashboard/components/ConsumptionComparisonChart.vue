<template>
  <div class="consumption-comparison-chart">
    <div ref="chartContainer" class="chart-container" v-loading="loading"></div>
    <div v-if="!hasData && !loading" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ConsumptionComparisonChart',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    hasData() {
      return this.data?.studentStats && this.data.studentStats.length > 0
    },
    chartData() {
      if (!this.hasData) return null
      
      // 取前15名学生进行对比
      const topStudents = this.data.studentStats.slice(0, 15)
      
      return {
        students: topStudents.map(s => s.studentName),
        totalConsumption: topStudents.map(s => parseFloat(s.totalConsumption || 0)),
        weeklyConsumption: topStudents.map(s => parseFloat(s.weeklyConsumption || 0)),
        monthlyConsumption: topStudents.map(s => parseFloat(s.monthlyConsumption || 0))
      }
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.chartData) return
      
      const option = {
        title: {
          text: '学生课消对比',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params) {
            let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
            params.forEach(param => {
              result += `<div style="margin-bottom: 2px;">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
                ${param.seriesName}: ${param.value} 课时
              </div>`
            })
            return result
          }
        },
        legend: {
          top: 30,
          data: ['总课消', '周课消', '月课消']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.students,
          axisLabel: {
            color: '#666',
            interval: 0,
            rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '课消课时',
          nameTextStyle: {
            color: '#666'
          },
          axisLabel: {
            color: '#666',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: [
          {
            name: '总课消',
            type: 'bar',
            data: this.chartData.totalConsumption,
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '周课消',
            type: 'bar',
            data: this.chartData.weeklyConsumption,
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '月课消',
            type: 'bar',
            data: this.chartData.monthlyConsumption,
            itemStyle: {
              color: '#fac858'
            }
          }
        ]
      }
      
      this.chart.setOption(option, true)
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-comparison-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
  }
}
</style>

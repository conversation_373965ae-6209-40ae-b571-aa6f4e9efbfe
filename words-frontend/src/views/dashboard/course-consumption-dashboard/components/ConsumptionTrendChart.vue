<template>
  <div class="consumption-trend-chart">
    <div ref="chartContainer" class="chart-container" v-loading="loading"></div>
    <div v-if="!hasData && !loading" class="no-data">
      <el-empty description="暂无数据" />
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ConsumptionTrendChart',
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  computed: {
    hasData() {
      return this.data?.studentStats && this.data.studentStats.length > 0
    },
    chartData() {
      if (!this.hasData) return null
      
      // 按日期分组统计课消数据（模拟趋势数据）
      const students = this.data.studentStats.slice(0, 10) // 取前10名学生
      const dates = this.generateDateRange()
      
      const series = students.map(student => ({
        name: student.studentName,
        type: 'line',
        smooth: true,
        data: this.generateTrendData(student, dates)
      }))
      
      return {
        dates,
        series
      }
    }
  },
  watch: {
    data: {
      handler() {
        this.$nextTick(() => {
          this.renderChart()
        })
      },
      deep: true
    },
    loading(val) {
      if (!val) {
        this.$nextTick(() => {
          this.renderChart()
        })
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeUnmount() {
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      if (this.$refs.chartContainer) {
        this.chart = echarts.init(this.$refs.chartContainer)
        this.renderChart()
      }
    },
    
    renderChart() {
      if (!this.chart || !this.chartData) return
      
      const option = {
        title: {
          text: '学生课消趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#303133'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = `<div style="margin-bottom: 5px;">${params[0].axisValue}</div>`
            params.forEach(param => {
              result += `<div style="margin-bottom: 2px;">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
                ${param.seriesName}: ${param.value} 课时
              </div>`
            })
            return result
          }
        },
        legend: {
          top: 30,
          type: 'scroll',
          pageButtonItemGap: 5,
          pageButtonGap: 30,
          pageButtonPosition: 'end'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.chartData.dates,
          axisLabel: {
            color: '#666'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '课消课时',
          nameTextStyle: {
            color: '#666'
          },
          axisLabel: {
            color: '#666',
            formatter: '{value}'
          },
          axisLine: {
            lineStyle: {
              color: '#e0e0e0'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#f0f0f0'
            }
          }
        },
        series: this.chartData.series
      }
      
      this.chart.setOption(option, true)
    },
    
    generateDateRange() {
      const dates = []
      const timeRange = this.data?.timeRangeInfo
      if (!timeRange) return dates
      
      const start = new Date(timeRange.startDate)
      const end = new Date(timeRange.endDate)
      const current = new Date(start)
      
      while (current <= end) {
        dates.push(current.toISOString().split('T')[0])
        current.setDate(current.getDate() + 1)
      }
      
      return dates
    },
    
    generateTrendData(student, dates) {
      // 模拟趋势数据生成
      const totalConsumption = parseFloat(student.totalConsumption || 0)
      const data = []
      let accumulated = 0
      
      dates.forEach((date, index) => {
        // 模拟每日课消增长
        const dailyIncrease = (totalConsumption / dates.length) * (0.8 + Math.random() * 0.4)
        accumulated += dailyIncrease
        data.push(Math.min(accumulated, totalConsumption).toFixed(2))
      })
      
      return data
    },
    
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.consumption-trend-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
  }
  
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;
  }
}
</style>

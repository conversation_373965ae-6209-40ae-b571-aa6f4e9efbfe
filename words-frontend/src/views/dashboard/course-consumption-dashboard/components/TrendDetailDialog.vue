<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
  >
    <div class="trend-detail-content" v-if="data">
      <div class="trend-chart">
        <ConsumptionTrendChart :data="data" :loading="false" />
      </div>
      
      <div class="trend-analysis" style="margin-top: 20px;">
        <h4>{{ type === 'weekly' ? '周课消' : '月课消' }}趋势分析</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item :label="`平均${type === 'weekly' ? '周' : '月'}课消`">
            {{ formatNumber(type === 'weekly' ? data.overallStats?.avgWeeklyConsumption : data.overallStats?.avgMonthlyConsumption) }} 课时
          </el-descriptions-item>
          <el-descriptions-item label="活跃学生数">
            {{ data.overallStats?.activeStudents || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="总课消">
            {{ formatNumber(data.overallStats?.totalConsumption) }} 课时
          </el-descriptions-item>
          <el-descriptions-item label="课消率">
            {{ formatNumber(data.overallStats?.consumptionRate) }}%
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="top-students" style="margin-top: 20px;">
        <h4>{{ type === 'weekly' ? '周' : '月' }}课消排行榜</h4>
        <el-table
          :data="topStudentsByType"
          stripe
          border
          style="width: 100%"
          max-height="300"
        >
          <el-table-column type="index" label="排名" width="60" />
          <el-table-column prop="studentName" label="学生姓名" width="120" />
          <el-table-column prop="salesName" label="销售" width="100" />
          <el-table-column :label="`${type === 'weekly' ? '周' : '月'}课消`" width="100">
            <template #default="{ row }">
              {{ formatNumber(type === 'weekly' ? row.weeklyConsumption : row.monthlyConsumption) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalConsumption" label="总课消" width="100">
            <template #default="{ row }">
              {{ formatNumber(row.totalConsumption) }}
            </template>
          </el-table-column>
          <el-table-column prop="consumptionCount" label="课消次数" width="100" />
        </el-table>
      </div>
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import ConsumptionTrendChart from './ConsumptionTrendChart.vue'

export default {
  name: 'TrendDetailDialog',
  components: {
    ConsumptionTrendChart
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: 'weekly' // weekly 或 monthly
    }
  },
  emits: ['update:modelValue'],
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    },
    
    dialogTitle() {
      return this.type === 'weekly' ? '周课消趋势详情' : '月课消趋势详情'
    },
    
    topStudentsByType() {
      if (!this.data?.studentStats) return []
      
      const students = [...this.data.studentStats]
      const sortKey = this.type === 'weekly' ? 'weeklyConsumption' : 'monthlyConsumption'
      
      return students
        .sort((a, b) => parseFloat(b[sortKey] || 0) - parseFloat(a[sortKey] || 0))
        .slice(0, 15)
    }
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0.00'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0.00'
      }
      return num.toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
.trend-detail-content {
  .trend-chart {
    height: 300px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    padding: 10px;
  }
  
  .trend-analysis,
  .top-students {
    h4 {
      margin: 0 0 15px 0;
      color: #303133;
    }
  }
}
</style>

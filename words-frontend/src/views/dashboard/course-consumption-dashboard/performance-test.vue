<template>
  <div class="performance-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <span>课消看板性能测试</span>
          <el-button type="primary" @click="runAllTests" :loading="testing">
            <el-icon><Timer /></el-icon>
            运行所有测试
          </el-button>
        </div>
      </template>

      <!-- 测试结果概览 -->
      <div class="test-overview" v-if="testResults.length > 0">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-statistic title="总测试数" :value="testResults.length" />
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="通过测试" 
              :value="passedTests" 
              :value-style="{ color: '#67C23A' }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="失败测试" 
              :value="failedTests" 
              :value-style="{ color: '#F56C6C' }"
            />
          </el-col>
          <el-col :span="6">
            <el-statistic 
              title="平均响应时间" 
              :value="averageResponseTime" 
              suffix="ms"
              :value-style="averageResponseTime > 5000 ? { color: '#F56C6C' } : { color: '#67C23A' }"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 单项测试按钮 -->
      <div class="test-buttons">
        <el-space wrap>
          <el-button @click="testDatabaseConnection" :loading="testing">
            <el-icon><Connection /></el-icon>
            数据库连接测试
          </el-button>
          <el-button @click="testQuickQuery" :loading="testing">
            <el-icon><Search /></el-icon>
            快速查询测试
          </el-button>
          <el-button @click="testFullDashboard" :loading="testing">
            <el-icon><DataBoard /></el-icon>
            完整看板测试
          </el-button>
          <el-button @click="testTimeRanges" :loading="testing">
            <el-icon><Calendar /></el-icon>
            时间范围测试
          </el-button>
          <el-button @click="clearResults" type="danger" plain>
            <el-icon><Delete /></el-icon>
            清空结果
          </el-button>
        </el-space>
      </div>

      <!-- 测试结果列表 -->
      <div class="test-results" v-if="testResults.length > 0">
        <h3>测试结果</h3>
        <el-table :data="testResults" stripe border style="width: 100%">
          <el-table-column prop="testName" label="测试名称" width="200" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'success' ? 'success' : 'danger'">
                {{ row.status === 'success' ? '通过' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="responseTime" label="响应时间(ms)" width="120">
            <template #default="{ row }">
              <span :style="{ color: row.responseTime > 5000 ? '#F56C6C' : '#67C23A' }">
                {{ row.responseTime }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="dataCount" label="数据量" width="100" />
          <el-table-column prop="message" label="详细信息" />
          <el-table-column prop="timestamp" label="测试时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 性能建议 -->
      <div class="performance-suggestions" v-if="testResults.length > 0">
        <h3>性能建议</h3>
        <el-alert
          v-for="suggestion in performanceSuggestions"
          :key="suggestion.type"
          :title="suggestion.title"
          :description="suggestion.description"
          :type="suggestion.type"
          style="margin-bottom: 10px;"
          show-icon
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDashboardData, getTimeRanges } from '@/api/course/consumption-dashboard'
import { Timer, Connection, Search, DataBoard, Calendar, Delete } from '@element-plus/icons-vue'

export default {
  name: 'PerformanceTest',
  components: {
    Timer, Connection, Search, DataBoard, Calendar, Delete
  },
  data() {
    return {
      testing: false,
      testResults: [],
      testTimeouts: {
        quick: 5000,    // 快速测试5秒超时
        normal: 15000,  // 普通测试15秒超时
        full: 30000     // 完整测试30秒超时
      }
    }
  },
  computed: {
    passedTests() {
      return this.testResults.filter(r => r.status === 'success').length
    },
    failedTests() {
      return this.testResults.filter(r => r.status === 'error').length
    },
    averageResponseTime() {
      if (this.testResults.length === 0) return 0
      const total = this.testResults.reduce((sum, r) => sum + r.responseTime, 0)
      return Math.round(total / this.testResults.length)
    },
    performanceSuggestions() {
      const suggestions = []
      
      if (this.averageResponseTime > 10000) {
        suggestions.push({
          type: 'error',
          title: '严重性能问题',
          description: '平均响应时间超过10秒，建议立即检查数据库索引和查询逻辑'
        })
      } else if (this.averageResponseTime > 5000) {
        suggestions.push({
          type: 'warning',
          title: '性能需要优化',
          description: '平均响应时间超过5秒，建议优化数据库查询和添加索引'
        })
      } else if (this.averageResponseTime > 2000) {
        suggestions.push({
          type: 'info',
          title: '性能可以改善',
          description: '响应时间在可接受范围内，但仍有优化空间'
        })
      } else {
        suggestions.push({
          type: 'success',
          title: '性能良好',
          description: '响应时间在理想范围内，系统性能良好'
        })
      }
      
      if (this.failedTests > 0) {
        suggestions.push({
          type: 'error',
          title: '存在失败的测试',
          description: '请检查失败的测试项目，可能存在系统问题'
        })
      }
      
      return suggestions
    }
  },
  methods: {
    async runAllTests() {
      this.testing = true
      this.testResults = []
      
      try {
        await this.testDatabaseConnection()
        await this.testQuickQuery()
        await this.testTimeRanges()
        await this.testFullDashboard()
        
        this.$message.success('所有测试完成')
      } catch (error) {
        this.$message.error('测试过程中出现错误: ' + error.message)
      } finally {
        this.testing = false
      }
    },

    async testDatabaseConnection() {
      const testName = '数据库连接测试'
      const startTime = Date.now()
      
      try {
        // 这里应该调用一个简单的数据库连接测试接口
        const response = await this.request('/course-consumption/dashboard-test/db-test', 'GET', null, this.testTimeouts.quick)
        const responseTime = Date.now() - startTime
        
        this.addTestResult({
          testName,
          status: 'success',
          responseTime,
          dataCount: response.totalRecords || 0,
          message: response.status || '连接正常'
        })
      } catch (error) {
        const responseTime = Date.now() - startTime
        this.addTestResult({
          testName,
          status: 'error',
          responseTime,
          dataCount: 0,
          message: error.message
        })
      }
    },

    async testQuickQuery() {
      const testName = '快速查询测试'
      const startTime = Date.now()
      
      try {
        const queryData = {
          startDate: this.getDateString(-7),
          endDate: this.getDateString(0),
          timeRangeType: 'custom'
        }
        
        const response = await this.request('/course-consumption/dashboard-test/quick-test', 'POST', queryData, this.testTimeouts.quick)
        const responseTime = Date.now() - startTime
        
        this.addTestResult({
          testName,
          status: 'success',
          responseTime,
          dataCount: response.dataCount || 0,
          message: `查询成功，数据库查询耗时: ${response.queryTime}ms`
        })
      } catch (error) {
        const responseTime = Date.now() - startTime
        this.addTestResult({
          testName,
          status: 'error',
          responseTime,
          dataCount: 0,
          message: error.message
        })
      }
    },

    async testTimeRanges() {
      const testName = '时间范围测试'
      const startTime = Date.now()
      
      try {
        const response = await this.request('/course-consumption/dashboard/time-ranges', 'GET', null, this.testTimeouts.quick)
        const responseTime = Date.now() - startTime
        
        this.addTestResult({
          testName,
          status: 'success',
          responseTime,
          dataCount: Object.keys(response.data || {}).length,
          message: '时间范围获取成功'
        })
      } catch (error) {
        const responseTime = Date.now() - startTime
        this.addTestResult({
          testName,
          status: 'error',
          responseTime,
          dataCount: 0,
          message: error.message
        })
      }
    },

    async testFullDashboard() {
      const testName = '完整看板测试'
      const startTime = Date.now()
      
      try {
        const queryData = {
          startDate: this.getDateString(-7),
          endDate: this.getDateString(0),
          timeRangeType: 'custom'
        }
        
        const response = await getDashboardData(queryData)
        const responseTime = Date.now() - startTime
        
        this.addTestResult({
          testName,
          status: 'success',
          responseTime,
          dataCount: response.data?.studentStats?.length || 0,
          message: '完整看板数据获取成功'
        })
      } catch (error) {
        const responseTime = Date.now() - startTime
        this.addTestResult({
          testName,
          status: 'error',
          responseTime,
          dataCount: 0,
          message: error.message
        })
      }
    },

    async request(url, method, data, timeout) {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)
      
      try {
        const config = {
          method,
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + this.$store.getters.token
          }
        }
        
        if (data && method !== 'GET') {
          config.body = JSON.stringify(data)
        }
        
        const response = await fetch(process.env.VUE_APP_BASE_API + url, config)
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        return await response.json()
      } catch (error) {
        clearTimeout(timeoutId)
        if (error.name === 'AbortError') {
          throw new Error(`请求超时 (${timeout}ms)`)
        }
        throw error
      }
    },

    addTestResult(result) {
      this.testResults.push({
        ...result,
        timestamp: new Date()
      })
    },

    clearResults() {
      this.testResults = []
    },

    getDateString(daysOffset) {
      const date = new Date()
      date.setDate(date.getDate() + daysOffset)
      return date.toISOString().split('T')[0]
    },

    formatTime(timestamp) {
      return timestamp.toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.performance-test {
  padding: 20px;
}

.test-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.test-overview {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.test-buttons {
  margin-bottom: 20px;
}

.test-results {
  margin-bottom: 20px;
  
  h3 {
    margin-bottom: 15px;
    color: #303133;
  }
}

.performance-suggestions {
  h3 {
    margin-bottom: 15px;
    color: #303133;
  }
}
</style>

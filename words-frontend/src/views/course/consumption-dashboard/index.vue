<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>学生课消看板</h2>
      <p class="page-description">查看学生课程消费统计数据，支持多维度分析</p>
    </div>

    <!-- 查询条件 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
        <el-form-item label="时间范围">
          <el-select 
            v-model="queryParams.timeRangeType" 
            placeholder="选择时间范围"
            style="width: 120px; margin-right: 10px;"
            @change="handleTimeRangeChange"
          >
            <el-option label="本周" value="thisWeek" />
            <el-option label="上周" value="lastWeek" />
            <el-option label="本月" value="thisMonth" />
            <el-option label="上月" value="lastMonth" />
            <el-option label="自定义" value="custom" />
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px;"
            @change="handleDateRangeChange"
          />
        </el-form-item>

        <el-form-item label="学科">
          <el-select v-model="queryParams.subject" placeholder="选择学科" clearable style="width: 120px;">
            <el-option label="英语" value="英语" />
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
          </el-select>
        </el-form-item>

        <el-form-item label="课型">
          <el-select v-model="queryParams.specification" placeholder="选择课型" clearable style="width: 140px;">
            <el-option label="单词课" value="单词课" />
            <el-option label="音标拼读课" value="音标拼读课" />
            <el-option label="语法课" value="语法课" />
            <el-option label="题型课" value="题型课" />
            <el-option label="听说课" value="听说课" />
            <el-option label="通用课（非英语）" value="通用课（非英语）" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">查询</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <el-button type="success" icon="Download" @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 总体统计卡片 -->
    <div class="stats-cards" v-if="dashboardData">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ dashboardData.overallStats.totalStudents || 0 }}</div>
              <div class="stat-label">总学生数</div>
            </div>
            <el-icon class="stat-icon student-icon"><User /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.totalConsumption) }}</div>
              <div class="stat-label">总课消课时</div>
            </div>
            <el-icon class="stat-icon consumption-icon"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.avgWeeklyConsumption) }}</div>
              <div class="stat-label">平均周课消</div>
            </div>
            <el-icon class="stat-icon weekly-icon"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(dashboardData.overallStats.avgMonthlyConsumption) }}</div>
              <div class="stat-label">平均月课消</div>
            </div>
            <el-icon class="stat-icon monthly-icon"><DataAnalysis /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 时间范围信息 -->
    <el-card class="time-info-card" shadow="never" v-if="dashboardData">
      <div class="time-info">
        <span class="time-label">统计时间：</span>
        <span class="time-value">{{ dashboardData.timeRangeInfo.description }}</span>
        <span class="time-detail">
          （{{ dashboardData.timeRangeInfo.startDate }} 至 {{ dashboardData.timeRangeInfo.endDate }}，
          共 {{ dashboardData.timeRangeInfo.totalDays }} 天）
        </span>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>学生课消统计</span>
          <div class="header-actions">
            <el-button type="text" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        :data="dashboardData?.studentStats || []"
        stripe
        border
        style="width: 100%"
        v-loading="loading"
        :default-sort="{ prop: 'totalConsumption', order: 'descending' }"
      >
        <el-table-column prop="studentName" label="学生姓名" width="120" fixed="left" />
        <el-table-column prop="studentPhone" label="手机号" width="130" />
        <el-table-column prop="salesName" label="销售" width="100" />
        <el-table-column prop="teacherNames" label="授课教师" width="150">
          <template #default="{ row }">
            <el-tag 
              v-for="(teacher, index) in row.teacherNames" 
              :key="index"
              size="small"
              style="margin-right: 5px; margin-bottom: 2px;"
            >
              {{ teacher }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalConsumption" label="总课消" width="100" sortable>
          <template #default="{ row }">
            <span class="consumption-value">{{ formatNumber(row.totalConsumption) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="weeklyConsumption" label="周课消" width="100" sortable>
          <template #default="{ row }">
            <span class="consumption-value">{{ formatNumber(row.weeklyConsumption) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="monthlyConsumption" label="月课消" width="100" sortable>
          <template #default="{ row }">
            <span class="consumption-value">{{ formatNumber(row.monthlyConsumption) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="consumptionCount" label="课消次数" width="100" sortable />
        <el-table-column prop="lastConsumptionTime" label="最后课消时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.lastConsumptionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="学科分布" width="200">
          <template #default="{ row }">
            <div class="subject-distribution">
              <el-tag 
                v-for="(subject, index) in row.subjectConsumptions" 
                :key="index"
                size="small"
                :type="getSubjectTagType(subject.subject)"
                style="margin-right: 5px; margin-bottom: 2px;"
              >
                {{ subject.subject }}: {{ formatNumber(subject.consumption) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 教师统计（教学组长可见） -->
    <el-card 
      class="table-card" 
      v-if="dashboardData?.teacherStats && dashboardData.teacherStats.length > 0"
    >
      <template #header>
        <div class="card-header">
          <span>教师课消统计</span>
        </div>
      </template>

      <el-table
        :data="dashboardData.teacherStats"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="teacherName" label="教师姓名" width="120" />
        <el-table-column prop="teachingGroupName" label="教学组" width="120" />
        <el-table-column prop="studentCount" label="学生数" width="100" />
        <el-table-column prop="totalConsumption" label="总课消" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgMonthlyConsumptionPerStudent" label="生均月课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgMonthlyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="consumptionCount" label="课消次数" width="100" />
      </el-table>
    </el-card>

    <!-- 教学组统计（教学组长可见） -->
    <el-card 
      class="table-card" 
      v-if="dashboardData?.teachingGroupStats && dashboardData.teachingGroupStats.length > 0"
    >
      <template #header>
        <div class="card-header">
          <span>教学组课消统计</span>
        </div>
      </template>

      <el-table
        :data="dashboardData.teachingGroupStats"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="teachingGroupName" label="教学组" width="150" />
        <el-table-column prop="teacherCount" label="教师数" width="100" />
        <el-table-column prop="studentCount" label="学生数" width="100" />
        <el-table-column prop="totalConsumption" label="总课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgMonthlyConsumptionPerStudent" label="生均月课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgMonthlyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgConsumptionPerTeacher" label="师均课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgConsumptionPerTeacher) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 销售统计（销售组长和销售总监可见） -->
    <el-card 
      class="table-card" 
      v-if="dashboardData?.salesStats && dashboardData.salesStats.length > 0"
    >
      <template #header>
        <div class="card-header">
          <span>销售课消统计</span>
        </div>
      </template>

      <el-table
        :data="dashboardData.salesStats"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="salesName" label="销售姓名" width="120" />
        <el-table-column prop="salesGroupName" label="销售组" width="120" />
        <el-table-column prop="studentCount" label="学生数" width="100" />
        <el-table-column prop="activeStudents" label="活跃学生数" width="120" />
        <el-table-column prop="totalConsumption" label="总课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.totalConsumption) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgWeeklyConsumptionPerStudent" label="生均周课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgWeeklyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="avgMonthlyConsumptionPerStudent" label="生均月课消" width="120">
          <template #default="{ row }">
            {{ formatNumber(row.avgMonthlyConsumptionPerStudent) }}
          </template>
        </el-table-column>
        <el-table-column prop="consumptionRate" label="课消率" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.consumptionRate) }}%
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script name="consumption-dashboard">
import { getDashboardData, getTimeRanges, exportDashboardData } from '@/api/course/consumption-dashboard'
import { formatDateTime } from '@/utils/date'
import { User, Clock, TrendCharts, DataAnalysis, Search, Refresh, Download } from '@element-plus/icons-vue'

export default {
  name: 'CourseConsumptionDashboard',
  components: {
    User,
    Clock,
    TrendCharts,
    DataAnalysis,
    Search,
    Refresh,
    Download
  },
  data() {
    return {
      // 加载状态
      loading: false,

      // 查询参数
      queryParams: {
        startDate: '',
        endDate: '',
        timeRangeType: 'thisWeek',
        subject: '',
        specification: '',
        studentId: '',
        teacherId: '',
        salesId: '',
        teachingGroupId: '',
        salesGroupId: ''
      },

      // 日期范围
      dateRange: [],

      // 看板数据
      dashboardData: null,

      // 时间范围选项
      timeRanges: {}
    }
  },

  created() {
    this.initTimeRange()
    this.getList()
  },

  methods: {
    /** 初始化时间范围 */
    async initTimeRange() {
      try {
        const response = await getTimeRanges()
        if (response.code === 200) {
          this.timeRanges = response.data
          // 设置默认为本周
          if (this.timeRanges.thisWeek) {
            this.queryParams.startDate = this.timeRanges.thisWeek.startDate
            this.queryParams.endDate = this.timeRanges.thisWeek.endDate
            this.dateRange = [this.queryParams.startDate, this.queryParams.endDate]
          }
        }
      } catch (error) {
        console.error('获取时间范围失败:', error)
      }
    },

    /** 获取看板数据 */
    async getList() {
      this.loading = true
      try {
        const response = await getDashboardData(this.queryParams)
        if (response.code === 200) {
          this.dashboardData = response.data
        } else {
          this.$modal.msgError(response.msg || '获取数据失败')
        }
      } catch (error) {
        console.error('获取看板数据失败:', error)
        this.$modal.msgError('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        startDate: '',
        endDate: '',
        timeRangeType: 'thisWeek',
        subject: '',
        specification: '',
        studentId: '',
        teacherId: '',
        salesId: '',
        teachingGroupId: '',
        salesGroupId: ''
      }
      this.initTimeRange()
      this.handleQuery()
    },

    /** 刷新数据 */
    refreshData() {
      this.getList()
    },

    /** 时间范围类型改变 */
    handleTimeRangeChange(value) {
      if (value && value !== 'custom' && this.timeRanges[value]) {
        const range = this.timeRanges[value]
        this.queryParams.startDate = range.startDate
        this.queryParams.endDate = range.endDate
        this.dateRange = [range.startDate, range.endDate]
      }
    },

    /** 日期范围改变 */
    handleDateRangeChange(value) {
      if (value && value.length === 2) {
        this.queryParams.startDate = value[0]
        this.queryParams.endDate = value[1]
        this.queryParams.timeRangeType = 'custom'
      } else {
        this.queryParams.startDate = ''
        this.queryParams.endDate = ''
      }
    },

    /** 导出数据 */
    async handleExport() {
      try {
        this.$modal.loading('正在导出数据，请稍候...')
        const response = await exportDashboardData(this.queryParams)
        this.$download.blob(response, '课消看板数据.xlsx')
      } catch (error) {
        console.error('导出失败:', error)
        this.$modal.msgError('导出失败')
      } finally {
        this.$modal.closeLoading()
      }
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '0'
      }
      const num = parseFloat(value)
      if (isNaN(num)) {
        return '0'
      }
      return num.toFixed(2)
    },

    /** 格式化日期时间 */
    formatDateTime(value) {
      if (!value) return '-'
      return formatDateTime(value)
    },

    /** 获取学科标签类型 */
    getSubjectTagType(subject) {
      const typeMap = {
        '英语': 'primary',
        '语文': 'success',
        '数学': 'warning',
        '物理': 'info',
        '化学': 'danger'
      }
      return typeMap[subject] || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .page-description {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.search-card {
  margin-bottom: 20px;

  :deep(.el-card__body) {
    padding: 20px 20px 0 20px;
  }
}

.stats-cards {
  margin-bottom: 20px;

  .stat-card {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-card__body) {
      padding: 20px;
    }

    .stat-content {
      position: relative;
      z-index: 2;

      .stat-value {
        font-size: 28px;
        font-weight: 700;
        color: #303133;
        line-height: 1;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #909399;
        font-weight: 500;
      }
    }

    .stat-icon {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40px;
      opacity: 0.3;
      z-index: 1;

      &.student-icon {
        color: #409EFF;
      }

      &.consumption-icon {
        color: #67C23A;
      }

      &.weekly-icon {
        color: #E6A23C;
      }

      &.monthly-icon {
        color: #F56C6C;
      }
    }
  }
}

.time-info-card {
  margin-bottom: 20px;

  :deep(.el-card__body) {
    padding: 15px 20px;
  }

  .time-info {
    font-size: 14px;
    color: #606266;

    .time-label {
      font-weight: 500;
    }

    .time-value {
      color: #409EFF;
      font-weight: 600;
      margin: 0 8px;
    }

    .time-detail {
      color: #909399;
    }
  }
}

.table-card {
  margin-bottom: 20px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      .el-button {
        padding: 0;
        border: none;
        background: none;
        color: #409EFF;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }

  :deep(.el-table) {
    .consumption-value {
      font-weight: 600;
      color: #67C23A;
    }

    .subject-distribution {
      .el-tag {
        font-size: 12px;
      }
    }

    .el-tag {
      margin-right: 5px;
      margin-bottom: 2px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .stats-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .search-card {
    :deep(.el-form--inline .el-form-item) {
      display: block;
      margin-right: 0;
      margin-bottom: 15px;
    }
  }

  .stats-cards {
    .el-col {
      span: 12;
    }
  }

  .table-card {
    :deep(.el-table) {
      font-size: 12px;

      .el-table__cell {
        padding: 8px 0;
      }
    }
  }
}
</style>

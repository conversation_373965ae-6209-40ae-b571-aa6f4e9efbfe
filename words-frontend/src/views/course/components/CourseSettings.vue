<template>
  <el-drawer
    v-model="visible"
    title="课程设置"
    size="400px"
    :with-header="false"
    direction="rtl"
    class="review-basket-drawer"
  >
    <div class="review-basket-container">
      <!-- 抽屉头部 -->
      <div class="review-basket-header">
        <div class="header-title">
          <el-icon class="header-icon"><Setting /></el-icon>
          课程设置
        </div>
        <div class="header-subtitle">个性化学习体验设置</div>
      </div>

      <!-- 设置内容区域 -->
      <div class="settings-content">
        <div class="setting-item">
          <div class="setting-label">
            <div class="setting-title">默认显示选项</div>
            <div class="setting-description">
              控制所有课程中单词翻译和句子翻译环节的选项显示方式
            </div>
          </div>
          <div class="setting-control">
            <el-switch
              v-model="settings.showOptionsDefault"
              active-text="是"
              inactive-text="否"
              @change="handleSettingChange"
            />
          </div>
        </div>

        <div class="setting-help">
          <div class="help-item">
            <el-icon><Check /></el-icon>
            <span>开启：默认显示所有选项，传统学习模式</span>
          </div>
          <div class="help-item">
            <el-icon><Close /></el-icon>
            <span>关闭：默认隐藏选项，需要点击按钮显示，增加思考时间</span>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Check, Close } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  studentId: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 控制抽屉显示
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 设置数据
const settings = ref({
  showOptionsDefault: true // 默认显示选项
})

// 本地存储的key
const getStorageKey = () => `course_settings_${props.studentId}`

// 加载设置
const loadSettings = () => {
  try {
    const savedSettings = localStorage.getItem(getStorageKey())
    if (savedSettings) {
      const parsed = JSON.parse(savedSettings)
      settings.value = { ...settings.value, ...parsed }
    }
  } catch (error) {
    console.error('加载课程设置失败:', error)
  }
}

// 保存设置
const saveSettings = () => {
  try {
    localStorage.setItem(getStorageKey(), JSON.stringify(settings.value))
    ElMessage.success('设置已保存')
  } catch (error) {
    console.error('保存课程设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

// 设置变更处理
const handleSettingChange = () => {
  saveSettings()
}

// 关闭抽屉
const handleClose = () => {
  visible.value = false
}

// 监听studentId变化，重新加载设置
watch(() => props.studentId, () => {
  if (props.studentId) {
    loadSettings()
  }
}, { immediate: true })

// 暴露获取设置的方法
const getSettings = () => settings.value

defineExpose({
  getSettings
})
</script>

<style scoped>
/* 抽屉容器样式 */
.review-basket-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

/* 抽屉头部样式 */
.review-basket-header {
  padding: 30px 20px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 8px;
}

.header-icon {
  margin-right: 12px;
  font-size: 28px;
}

.header-subtitle {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 300;
}

/* 设置内容区域 */
.settings-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.setting-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.setting-label {
  margin-bottom: 16px;
}

.setting-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.setting-description {
  font-size: 13px;
  opacity: 0.8;
  line-height: 1.4;
}

.setting-control {
  display: flex;
  justify-content: center;
}

/* 帮助说明区域 */
.setting-help {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.help-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 13px;
  opacity: 0.9;
}

.help-item:last-child {
  margin-bottom: 0;
}

.help-item .el-icon {
  margin-right: 8px;
  font-size: 14px;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-switch) {
  --el-switch-on-color: #67c23a;
  --el-switch-off-color: #dcdfe6;
}

:deep(.el-switch__label) {
  color: white;
  font-weight: 500;
}

:deep(.el-switch__label.is-active) {
  color: #67c23a;
}
</style>

<template>
  <div class="h5-course-material">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <!-- 录音状态指示器 -->
      <div v-if="isRecording" class="recording-indicator">
        <div class="recording-dot"></div>
        <span>正在录音</span>
      </div>

      <!-- 音频播放状态 -->
      <div v-if="currentPlayingAudio" class="playing-indicator">
        <div class="playing-wave"></div>
        <span>播放中</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载课程资料...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">⚠️</div>
      <p>{{ error }}</p>
      <button @click="loadCourseData" class="retry-btn">重新加载</button>
    </div>

    <!-- 课程内容 -->
    <div v-else-if="courseInfo" class="h5-content">
      <!-- 顶部信息栏 -->
      <div class="top-info">
        <div class="course-info">
          <h1>课堂资料</h1>
          <div class="participants">
            <span class="teacher">👨‍🏫 {{ courseInfo.teacher?.name || '老师' }}</span>
            <span class="student">👨‍🎓 {{ courseInfo.student?.name || '同学' }}</span>
          </div>
        </div>

        <!-- 进度指示器 -->
        <div class="progress-indicator">
          <div class="progress-text">{{ currentWordIndex + 1 }} / {{ totalWords }}</div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
        </div>
      </div>

      <!-- 单词卡片容器 -->
      <div class="word-container" v-if="wordsList.length > 0">
        <!-- 当前单词卡片 -->
        <div class="word-card" v-if="currentWord">
          <!-- 单词标题 -->
          <div class="word-header">
            <h2 class="word-title">{{ currentWord.wordInfo.word }}</h2>
            <div class="difficulty-badge" :class="currentWord.wordInfo.difficulty">
              {{ getDifficultyText(currentWord.wordInfo.difficulty) }}
            </div>
          </div>

          <!-- 音标区域 -->
          <div class="phonetics-section">
            <div
              class="phonetic-item uk"
              @click="playWordAudio(currentWord.wordInfo, 'uk')"
              :class="{ 'playing': currentPlayingAudio === `word-${currentWord.wordInfo.id}-uk` }"
            >
              <div class="phonetic-label">英式</div>
              <div class="phonetic-text">{{ currentWord.wordInfo.phoneticUk }}</div>
              <div class="play-icon">🔊</div>
            </div>
            <div
              class="phonetic-item us"
              @click="playWordAudio(currentWord.wordInfo, 'us')"
              :class="{ 'playing': currentPlayingAudio === `word-${currentWord.wordInfo.id}-us` }"
            >
              <div class="phonetic-label">美式</div>
              <div class="phonetic-text">{{ currentWord.wordInfo.phoneticUs }}</div>
              <div class="play-icon">🔊</div>
            </div>
          </div>

          <!-- 释义区域 -->
          <div class="meanings-section" v-if="currentWord.wordInfo.meanings?.pos">
            <h3>释义</h3>
            <div class="meanings-list">
              <div
                v-for="(meaning, idx) in currentWord.wordInfo.meanings.pos"
                :key="idx"
                class="meaning-item"
              >
                <span class="pos-tag">{{ meaning.pos }}</span>
                <span class="definition">{{ meaning.def }}</span>
              </div>
            </div>
          </div>

          <!-- 例句区域 -->
          <div class="sentences-section" v-if="getSentencesList(currentWord.wordInfo.sentences).length > 0">
            <h3>例句</h3>
            <div class="sentences-list">
              <div
                v-for="(sentence, idx) in getSentencesList(currentWord.wordInfo.sentences)"
                :key="idx"
                class="sentence-item"
                @click="playSentenceAudio(sentence, 'uk')"
                :class="{ 'playing': currentPlayingAudio === `sentence-${idx}` }"
              >
                <div class="sentence-en">
                  <span class="text">{{ sentence.sentenceEn }}</span>
                  <span class="play-icon">🔊</span>
                </div>
                <div class="sentence-cn">{{ sentence.sentenceCn }}</div>
              </div>
            </div>
          </div>

          <!-- 跟读练习区域 -->
          <div class="practice-section">
            <h3>跟读练习</h3>
            <div class="practice-controls">
              <button
                @click="startFollowReading(currentWord.wordInfo, 'uk')"
                class="practice-btn"
                :disabled="isRecording"
                :class="{ 'recording': isRecording }"
              >
                <span class="icon">🎤</span>
                <span class="text">{{ isRecording ? '录音中...' : '开始跟读' }}</span>
              </button>

              <button
                v-if="isRecording"
                @click="stopRecording"
                class="stop-btn"
              >
                <span class="icon">⏹️</span>
                <span class="text">停止录音</span>
              </button>

              <button
                v-if="recordedAudio"
                @click="playRecordedAudio"
                class="playback-btn"
              >
                <span class="icon">▶️</span>
                <span class="text">播放录音</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部导航 -->
      <div class="bottom-navigation">
        <button
          @click="previousWord"
          :disabled="currentWordIndex <= 0"
          class="nav-btn prev"
          :class="{ 'disabled': currentWordIndex <= 0 }"
        >
          <span class="icon">◀️</span>
          <span class="text">上一个</span>
        </button>

        <div class="word-dots">
          <div
            v-for="(word, index) in wordsList"
            :key="word.id"
            class="dot"
            :class="{ 'active': index === currentWordIndex }"
            @click="goToWord(index)"
          ></div>
        </div>

        <button
          @click="nextWord"
          :disabled="currentWordIndex >= totalWords - 1"
          class="nav-btn next"
          :class="{ 'disabled': currentWordIndex >= totalWords - 1 }"
        >
          <span class="text">下一个</span>
          <span class="icon">▶️</span>
        </button>
      </div>
    </div>

    <!-- 音频播放器 -->
    <audio ref="audioPlayer" preload="none" @ended="onAudioEnded"></audio>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
// 简化的API调用函数
const fetchCourseInfoApi = async (courseId: string) => {
  try {
    const response = await fetch(`/dev-api/course/info/${courseId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  } catch (error) {
    console.error('API调用失败:', error)
    throw error
  }
}

// 简单的消息提示函数
const showMessage = (message: string, type: 'success' | 'error' | 'warning' = 'success') => {
  // 创建简单的toast提示
  const toast = document.createElement('div')
  toast.className = `h5-toast ${type}`
  toast.textContent = message
  document.body.appendChild(toast)

  setTimeout(() => {
    toast.classList.add('show')
  }, 100)

  setTimeout(() => {
    toast.classList.remove('show')
    setTimeout(() => {
      document.body.removeChild(toast)
    }, 300)
  }, 2000)
}

// 基础类型定义
interface CourseInfo {
  id: string
  type: string
  status: string
  teacher: {
    id: string
    name: string
    avatar: string
  }
  student: {
    id: string
    name: string
    avatar: string
  }
  scheduledStartTime: string
  scheduledEndTime: string
  actualStartTime: string
  actualEndTime: string
  durationMinutes: number
  content: CourseContent
}

interface CourseContent {
  currentSectionIndex: number
  sections: Section[]
}

interface Section {
  id: string
  title: string
  type: string
  status: string
  currentWordIndex: number
  startTime: string
  endTime: string
  words: CourseSectionWordDto[]
}

// 扩展类型定义以匹配后端数据结构
interface CourseSectionWordDto {
  id: string
  status: string
  result: string
  currentStepIndex: number
  wordInfo: CourseSectionWordInfo
  steps: any[]
}

interface CourseSectionWordInfo {
  id: string
  word: string
  syllables: string
  phoneticUk: string
  phoneticUs: string
  difficulty: string
  videoUrl: string
  audioUkUrl: string
  audioUsUrl: string
  meanings: WordMeanings
  sentences: WordSentences
}

interface WordMeanings {
  pos: Array<{pos: string, def: string}>
  practices: string[]
}

// 修正：后端返回的sentences是Map<String, List<Sentences>>结构
interface WordSentences {
  [stage: string]: Array<{
    sentenceEn: string
    sentenceCn: string
    audioUkUrl: string
    audioUsUrl: string
    structurePartsEn: string[]
    practices: string[]
    syllables: string
    stage: string
  }>
}

// 响应式数据
const loading = ref(true)
const error = ref('')
const courseInfo = ref<CourseInfo | null>(null)
const currentWordIndex = ref(0)
const audioPlayer = ref<HTMLAudioElement>()
const isRecording = ref(false)
const mediaRecorder = ref<MediaRecorder | null>(null)
const recordedAudio = ref<Blob | null>(null)
const currentPlayingAudio = ref<string>('')

// H5专用状态
const isPlaying = ref(false)
const touchStartX = ref(0)
const touchStartY = ref(0)
const isSwiping = ref(false)

// 路由参数
const route = useRoute()
const courseId = computed(() => route.params.courseId as string || route.query.courseId as string)

// 计算属性
const wordsList = computed(() => {
  if (!courseInfo.value?.content?.sections) return []

  const allWords: CourseSectionWordDto[] = []
  courseInfo.value.content.sections.forEach((section: any) => {
    if (section.words) {
      allWords.push(...section.words)
    }
  })
  return allWords
})

const totalWords = computed(() => wordsList.value.length)

const progressPercentage = computed(() => {
  if (totalWords.value === 0) return 0
  return Math.round(((currentWordIndex.value + 1) / totalWords.value) * 100)
})

// H5专用计算属性
const currentWord = computed(() => {
  return wordsList.value[currentWordIndex.value] || null
})

// 获取难度文本
const getDifficultyText = (difficulty: string) => {
  const difficultyMap: Record<string, string> = {
    'easy': '简单',
    'medium': '中等',
    'hard': '困难',
    'expert': '专家'
  }
  return difficultyMap[difficulty] || '未知'
}

// 方法
const loadCourseData = async () => {
  if (!courseId.value) {
    error.value = '缺少课程ID参数'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''
    
    const response = await fetchCourseInfoApi(courseId.value)
    if (response.code === 200) {
      courseInfo.value = response.data
      console.log('课程信息加载成功:', courseInfo.value)

      // 调试：检查单词数据结构
      if (courseInfo.value?.content?.sections) {
        courseInfo.value.content.sections.forEach((section: any, sectionIndex: number) => {
          console.log(`Section ${sectionIndex}:`, section)
          if (section.words) {
            section.words.forEach((word: any, wordIndex: number) => {
              console.log(`Word ${wordIndex}:`, word)
              if (word.wordInfo) {
                console.log(`WordInfo:`, word.wordInfo)
                console.log(`Sentences:`, word.wordInfo.sentences)
              }
            })
          }
        })
      }
    } else {
      throw new Error(response.msg || '获取课程信息失败')
    }
  } catch (err: any) {
    console.error('加载课程数据失败:', err)
    error.value = err.message || '加载课程数据失败'
    showMessage(error.value, 'error')
  } finally {
    loading.value = false
  }
}

const playWordAudio = (wordInfo: CourseSectionWordInfo, accent: 'uk' | 'us') => {
  const audioUrl = accent === 'uk' ? wordInfo.audioUkUrl : wordInfo.audioUsUrl
  if (audioUrl && audioPlayer.value) {
    currentPlayingAudio.value = `word-${wordInfo.id}-${accent}`
    audioPlayer.value.src = audioUrl
    audioPlayer.value.play().catch(err => {
      console.error('音频播放失败:', err)
      showMessage('音频播放失败', 'warning')
    })
  } else {
    showMessage('音频文件不可用', 'warning')
  }
}

const playSentenceAudio = (sentence: any, accent: 'uk' | 'us') => {
  const audioUrl = accent === 'uk' ? sentence.audioUkUrl : sentence.audioUsUrl
  if (audioUrl && audioPlayer.value) {
    audioPlayer.value.src = audioUrl
    audioPlayer.value.play().catch(err => {
      console.error('音频播放失败:', err)
      showMessage('音频播放失败', 'warning')
    })
  } else {
    showMessage('音频文件不可用', 'warning')
  }
}

const previousWord = () => {
  if (currentWordIndex.value > 0) {
    currentWordIndex.value--
  }
}

const nextWord = () => {
  if (currentWordIndex.value < totalWords.value - 1) {
    currentWordIndex.value++
  }
}

const onAudioEnded = () => {
  console.log('音频播放结束')
  currentPlayingAudio.value = ''
  isPlaying.value = false
}

// H5专用交互方法
const goToWord = (index: number) => {
  if (index >= 0 && index < totalWords.value) {
    currentWordIndex.value = index
  }
}

// 触摸滑动支持
const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  isSwiping.value = false
}

const handleTouchMove = (e: TouchEvent) => {
  if (!touchStartX.value || !touchStartY.value) return

  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY

  const diffX = touchStartX.value - currentX
  const diffY = touchStartY.value - currentY

  // 判断是否为水平滑动
  if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
    isSwiping.value = true
    e.preventDefault() // 阻止默认滚动行为
  }
}

const handleTouchEnd = (e: TouchEvent) => {
  if (!isSwiping.value || !touchStartX.value) return

  const endX = e.changedTouches[0].clientX
  const diffX = touchStartX.value - endX

  // 滑动阈值
  const threshold = 100

  if (Math.abs(diffX) > threshold) {
    if (diffX > 0) {
      // 向左滑动，下一个单词
      nextWord()
    } else {
      // 向右滑动，上一个单词
      previousWord()
    }
  }

  // 重置状态
  touchStartX.value = 0
  touchStartY.value = 0
  isSwiping.value = false
}

// 添加触摸事件监听
const addTouchListeners = () => {
  const container = document.querySelector('.word-container')
  if (container) {
    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd, { passive: false })
  }
}

// 移除触摸事件监听
const removeTouchListeners = () => {
  const container = document.querySelector('.word-container')
  if (container) {
    container.removeEventListener('touchstart', handleTouchStart)
    container.removeEventListener('touchmove', handleTouchMove)
    container.removeEventListener('touchend', handleTouchEnd)
  }
}

// 获取例句列表的辅助方法
const getSentencesList = (sentences: any) => {
  if (!sentences) return []

  // 检查sentences的类型
  console.log('sentences data:', sentences)

  // 如果是Map结构 {stage: [sentences]}
  if (typeof sentences === 'object' && !Array.isArray(sentences)) {
    const allSentences: any[] = []
    Object.keys(sentences).forEach(stage => {
      if (sentences[stage] && Array.isArray(sentences[stage])) {
        allSentences.push(...sentences[stage])
      }
    })
    return allSentences
  }

  // 如果直接是数组
  if (Array.isArray(sentences)) {
    return sentences
  }

  return []
}

// 跟读功能相关方法
const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    mediaRecorder.value = new MediaRecorder(stream)
    const chunks: BlobPart[] = []

    mediaRecorder.value.ondataavailable = (event) => {
      chunks.push(event.data)
    }

    mediaRecorder.value.onstop = () => {
      recordedAudio.value = new Blob(chunks, { type: 'audio/wav' })
      stream.getTracks().forEach(track => track.stop())
    }

    mediaRecorder.value.start()
    isRecording.value = true
    showMessage('开始录音，请跟读', 'success')
  } catch (error) {
    console.error('录音失败:', error)
    showMessage('录音功能不可用，请检查麦克风权限', 'error')
  }
}

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop()
    isRecording.value = false
    showMessage('录音完成', 'success')
  }
}

const playRecordedAudio = () => {
  if (recordedAudio.value && audioPlayer.value) {
    const url = URL.createObjectURL(recordedAudio.value)
    audioPlayer.value.src = url
    audioPlayer.value.play().catch(err => {
      console.error('播放录音失败:', err)
      showMessage('播放录音失败', 'warning')
    })
  } else {
    showMessage('没有录音可播放', 'warning')
  }
}

const startFollowReading = (wordInfo: CourseSectionWordInfo, accent: 'uk' | 'us') => {
  // 先播放原音
  playWordAudio(wordInfo, accent)

  // 等待音频播放完成后开始录音
  if (audioPlayer.value) {
    audioPlayer.value.onended = () => {
      setTimeout(() => {
        startRecording()
      }, 500) // 延迟500ms开始录音
    }
  }
}

// 生命周期
onMounted(() => {
  loadCourseData()

  // 添加触摸事件监听
  nextTick(() => {
    addTouchListeners()
  })
})

// 组件卸载时清理事件监听
import { onUnmounted } from 'vue'
onUnmounted(() => {
  removeTouchListeners()
})
</script>

<style lang="scss" scoped>
/* H5移动端专用样式 */
.h5-course-material {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  position: relative;
  overflow-x: hidden;

  /* 禁用选择和缩放 */
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* 状态栏 */
.status-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.recording-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff4757;

  .recording-dot {
    width: 8px;
    height: 8px;
    background: #ff4757;
    border-radius: 50%;
    animation: pulse 1s infinite;
  }
}

.playing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2ed573;

  .playing-wave {
    width: 16px;
    height: 8px;
    background: linear-gradient(45deg, #2ed573 25%, transparent 25%),
                linear-gradient(-45deg, #2ed573 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #2ed573 75%),
                linear-gradient(-45deg, transparent 75%, #2ed573 75%);
    background-size: 4px 4px;
    animation: wave 0.8s linear infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes wave {
  0% { background-position: 0 0, 0 0, 2px 2px, 2px 2px; }
  100% { background-position: 4px 4px, 4px 4px, 6px 6px, 6px 6px; }
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 44px);
  padding: 20px;
  color: white;
  text-align: center;
  margin-top: 44px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover, &:active {
    background: white;
    color: #667eea;
    transform: scale(0.95);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* H5内容区域 */
.h5-content {
  padding-top: 44px; /* 为状态栏留出空间 */
  padding-bottom: 80px; /* 为底部导航留出空间 */
  min-height: 100vh;
}

/* 顶部信息栏 */
.top-info {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px;
  margin: 0 12px 16px 12px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.course-info {
  h1 {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0 0 12px 0;
    text-align: center;
  }

  .participants {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #7f8c8d;

    .teacher, .student {
      padding: 6px 12px;
      background: #ecf0f1;
      border-radius: 15px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.progress-indicator {
  margin-top: 12px;

  .progress-text {
    font-size: 14px;
    font-weight: 500;
    color: #34495e;
    text-align: center;
    margin-bottom: 8px;
  }

  .progress-bar {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }
}

/* 单词容器 */
.word-container {
  padding: 0 12px;
  touch-action: pan-y; /* 允许垂直滚动，禁用水平滚动 */
}

.word-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

/* 单词标题区域 */
.word-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .word-title {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
  }

  .difficulty-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    &.easy {
      background: #d4edda;
      color: #155724;
    }

    &.medium {
      background: #fff3cd;
      color: #856404;
    }

    &.hard {
      background: #f8d7da;
      color: #721c24;
    }

    &.expert {
      background: #d1ecf1;
      color: #0c5460;
    }
  }
}

/* 音标区域 */
.phonetics-section {
  margin-bottom: 24px;

  .phonetic-item {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:active {
      transform: scale(0.98);
    }

    &.playing {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      .play-icon {
        animation: bounce 0.6s ease-in-out infinite alternate;
      }
    }

    &.uk {
      border-left: 4px solid #e74c3c;

      &:hover, &:active {
        background: #e74c3c;
        color: white;
      }
    }

    &.us {
      border-left: 4px solid #27ae60;

      &:hover, &:active {
        background: #27ae60;
        color: white;
      }
    }

    .phonetic-label {
      font-size: 14px;
      font-weight: 500;
      opacity: 0.8;
    }

    .phonetic-text {
      font-size: 18px;
      font-weight: bold;
      flex: 1;
      text-align: center;
    }

    .play-icon {
      font-size: 20px;
      opacity: 0.8;
    }
  }
}

@keyframes bounce {
  0% { transform: scale(1); }
  100% { transform: scale(1.2); }
}

/* 释义区域 */
.meanings-section {
  margin-bottom: 24px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &:before {
      content: "📖";
      font-size: 14px;
    }
  }

  .meanings-list {
    .meaning-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 12px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 12px;

      .pos-tag {
        background: #667eea;
        color: white;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        min-width: 32px;
        text-align: center;
        flex-shrink: 0;
      }

      .definition {
        flex: 1;
        color: #2c3e50;
        font-size: 15px;
        line-height: 1.4;
      }
    }
  }
}

/* 例句区域 */
.sentences-section {
  margin-bottom: 24px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &:before {
      content: "💬";
      font-size: 14px;
    }
  }

  .sentences-list {
    .sentence-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:active {
        transform: scale(0.98);
      }

      &.playing {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;

        .play-icon {
          animation: bounce 0.6s ease-in-out infinite alternate;
        }
      }

      .sentence-en {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .text {
          flex: 1;
          font-size: 15px;
          line-height: 1.4;
          font-weight: 500;
        }

        .play-icon {
          font-size: 18px;
          opacity: 0.7;
          margin-left: 8px;
        }
      }

      .sentence-cn {
        font-size: 14px;
        opacity: 0.8;
        line-height: 1.3;
      }
    }
  }
}

/* 跟读练习区域 */
.practice-section {
  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;

    &:before {
      content: "🎤";
      font-size: 14px;
    }
  }

  .practice-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;

    button {
      background: #f8f9fa;
      border: none;
      border-radius: 16px;
      padding: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }

      .icon {
        font-size: 20px;
      }

      .text {
        flex: 1;
        text-align: center;
      }
    }

    .practice-btn {
      background: linear-gradient(135deg, #2ed573, #17c0eb);
      color: white;

      &.recording {
        background: linear-gradient(135deg, #ff4757, #ff6b7a);
        animation: pulse 1s infinite;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .stop-btn {
      background: linear-gradient(135deg, #ff4757, #ff6b7a);
      color: white;
    }

    .playback-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }
  }
}

/* 底部导航 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .nav-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;

    &:active {
      transform: scale(0.95);
    }

    &.disabled {
      opacity: 0.4;
      cursor: not-allowed;
    }

    &.prev {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      &.disabled {
        background: #f8f9fa;
        color: #adb5bd;
      }
    }

    &.next {
      background: linear-gradient(135deg, #2ed573, #17c0eb);
      color: white;

      &.disabled {
        background: #f8f9fa;
        color: #adb5bd;
      }
    }

    .icon {
      font-size: 16px;
    }

    .text {
      font-size: 12px;
    }
  }

  .word-dots {
    display: flex;
    gap: 8px;
    align-items: center;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #dee2e6;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #667eea;
        transform: scale(1.5);
      }

      &:active {
        transform: scale(0.8);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .course-material-view {
    padding: 8px;
  }

  .course-header {
    padding: 16px;
    margin-bottom: 16px;

    .course-title {
      font-size: 20px;
      margin-bottom: 12px;
    }

    .course-meta {
      flex-direction: column;
      gap: 8px;
      text-align: center;

      span {
        font-size: 14px;
      }
    }
  }

  .word-card {
    padding: 16px;
    margin-bottom: 16px;

    .word-text {
      font-size: 24px;
      margin-bottom: 8px;
    }

    .phonetics {
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;

      .phonetic {
        font-size: 14px;
        padding: 4px 8px;
      }
    }

    .meanings {
      margin-bottom: 16px;

      .meaning-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        margin-bottom: 12px;

        .pos {
          font-size: 11px;
          padding: 2px 6px;
        }

        .definition {
          font-size: 14px;
          line-height: 1.4;
        }
      }
    }

    .sentences {
      .sentence-item {
        margin-bottom: 12px;

        .sentence-en {
          font-size: 14px;
          line-height: 1.4;
          margin-bottom: 4px;
        }

        .sentence-cn {
          font-size: 13px;
          padding-left: 0;
        }
      }
    }

    .word-actions {
      flex-wrap: wrap;
      gap: 8px;

      .el-button {
        flex: 1;
        min-width: calc(50% - 4px);
        font-size: 12px;
        padding: 8px 12px;
      }
    }
  }

  .navigation-buttons {
    position: fixed;
    bottom: 20px;
    left: 8px;
    right: 8px;
    display: flex;
    gap: 12px;
    background: rgba(255, 255, 255, 0.95);
    padding: 12px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);

    .el-button {
      flex: 1;
      font-size: 14px;
      padding: 12px;
    }
  }

  // 为固定导航按钮留出空间
  .words-container {
    padding-bottom: 100px;
  }
}

// 超小屏幕适配
@media (max-width: 480px) {
  .course-material-view {
    padding: 4px;
  }

  .course-header {
    padding: 12px;

    .course-title {
      font-size: 18px;
    }
  }

  .word-card {
    padding: 12px;

    .word-text {
      font-size: 20px;
    }

    .word-actions {
      .el-button {
        min-width: 100%;
        margin-bottom: 4px;
      }
    }
  }
}

// 录音状态指示器
.recording-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #e74c3c;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: bold;
  z-index: 1000;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

// 音频播放状态指示
.audio-playing {
  .audio-icon {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Toast提示样式 */
.h5-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  z-index: 9999;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &.show {
    opacity: 1;
  }

  &.success {
    background: rgba(46, 213, 115, 0.9);
  }

  &.error {
    background: rgba(255, 71, 87, 0.9);
  }

  &.warning {
    background: rgba(255, 165, 2, 0.9);
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .status-bar {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-top: env(safe-area-inset-top);
  }

  .bottom-navigation {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }
}
</style>

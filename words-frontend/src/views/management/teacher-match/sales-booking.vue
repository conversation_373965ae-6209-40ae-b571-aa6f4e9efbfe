<template>
  <div class="sales-booking-container">
    <div class="booking-layout">
      <!-- 左侧：筛选条件面板 -->
      <div class="filter-panel">
        <el-card class="filter-card">
          <template #header>
            <div class="filter-header">
              <span>筛选条件</span>
              <el-button
                type="text"
                size="small"
                @click="resetFilters"
                :disabled="!hasActiveFilters"
              >
                重置筛选
              </el-button>
            </div>
          </template>

          <!-- 学生信息 -->
          <div class="student-section">
            <!-- 锁定学生信息显示 -->
            <!-- <el-form-item v-if="isStudentLocked" label="学生信息">
              <div class="locked-student-info">
                <div class="student-name">{{ studentInfo.name }}</div>
                <div class="student-details">
                  <span>{{ studentInfo.phone }}</span>
                  <span v-if="studentInfo.grade">{{ getGradeText(studentInfo.grade) }}</span>
                  <span v-if="studentInfo.school">{{ studentInfo.school }}</span>
                </div>
              </div>
            </el-form-item> -->

            <!-- 学生选择器 -->
            <!-- <el-form-item v-else label="选择学生" required>
              <el-select
                v-model="searchForm.studentId"
                placeholder="请选择学生"
                filterable
                remote
                :remote-method="searchStudents"
                :loading="loadingStudents"
                style="width: 100%"
                @change="handleStudentChange"
              >
                <el-option
                  v-for="student in studentOptions"
                  :key="student.id"
                  :label="`${student.name} (${student.phone})`"
                  :value="student.id"
                />
              </el-select>
            </el-form-item> -->

            <!-- 选中学生信息展示 -->
            <div v-if="currentSelectedStudent" class="selected-student-info">
              <div class="student-name">{{ currentSelectedStudent.name }}</div>
              <div class="student-details">
                <span>{{ currentSelectedStudent.phone }}</span>
                <span>{{ getGradeText(currentSelectedStudent.grade) }}</span>
                <span>{{ currentSelectedStudent.school || "未填写学校" }}</span>
              </div>
            </div>
          </div>

          <!-- 时间需求 -->
          <div class="time-section">
            <el-form-item label="开始日期" required>
              <el-date-picker
                v-model="searchForm.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                size="small"
                :disabled-date="disabledDate"
              />
            </el-form-item>

            <!-- 试听课时间选择（必填） -->
            <el-form-item label="" required label-width="0">
              <div
                class="trial-time-section"
                style="padding: 15px 0; border-bottom: 1px solid #e4e7ed"
              >
                <div class="section-header" style="margin-bottom: 10px">
                  <span style="font-weight: 500">试听课时间</span>
                  <el-tag type="danger" size="small" style="margin-left: 8px"
                    >必填</el-tag
                  >
                </div>
                <div
                  class="trial-time-selector"
                  style="display: flex; align-items: center; flex-wrap: wrap; gap: 8px"
                >
                  <el-date-picker
                    v-model="searchForm.trialClassTime.date"
                    type="date"
                    placeholder="选择日期"
                    :disabled-date="disabledTrialDate"
                    style="width: 100px"
                    size="small"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                  />
                  <el-select
                    v-model="searchForm.trialClassTime.startTime"
                    placeholder="开始时间"
                    style="width: 90px"
                    size="small"
                    clearable
                  >
                    <el-option
                      v-for="time in trialTimeOptions"
                      :key="time.value"
                      :label="time.label"
                      :value="time.value"
                    />
                  </el-select>
                  <span style="margin: 0 5px">~</span>
                  <el-select
                    v-model="searchForm.trialClassTime.endTime"
                    placeholder="结束时间"
                    style="width: 90px"
                    size="small"
                    clearable
                    :disabled="!searchForm.trialClassTime.startTime"
                  >
                    <el-option
                      v-for="time in availableEndTimes"
                      :key="time.value"
                      :label="time.label"
                      :value="time.value"
                    />
                  </el-select>
                </div>
                <!-- <div class="trial-time-tips" style="margin-top: 8px">
                  <el-text type="info" size="small">
                    <el-icon><InfoFilled /></el-icon>
                    只能预约次日时间，14:00前可预约次日6:00后，14:00后可预约次日14:00后。至少需要1小时时间跨度。
                  </el-text>
                </div> -->
              </div>
            </el-form-item>

            <el-form-item label="" required label-width="0">
              <div class="time-slots-container">
                <div class="time-slots-header">
                  <span>正课时间段</span>
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Plus"
                    @click="addTimeSlot"
                    :disabled="searchForm.timeSlots.length >= 5"
                  >
                    添加时间段
                  </el-button>
                </div>

                <div v-if="searchForm.timeSlots.length > 0" class="time-slots-list">
                  <div
                    v-for="(slot, index) in searchForm.timeSlots"
                    :key="index"
                    class="time-slot-item"
                  >
                    <div class="slot-controls">
                      <el-select
                        v-model="slot.weekday"
                        placeholder="星期"
                        size="small"
                        style="width: 80px"
                      >
                        <el-option
                          v-for="day in weekDays"
                          :key="day.value"
                          :label="day.label"
                          :value="day.value"
                        />
                      </el-select>

                      <el-select
                        v-model="slot.duration"
                        placeholder="时长"
                        size="small"
                        style="width: 90px"
                        @change="updateEndTime(slot)"
                      >
                        <el-option
                          v-for="duration in durationOptions"
                          :key="duration.value"
                          :label="duration.label"
                          :value="duration.value"
                        />
                      </el-select>

                      <el-select
                        v-model="slot.startTime"
                        placeholder="开始时间"
                        size="small"
                        style="width: 100px"
                        @change="updateEndTime(slot)"
                      >
                        <el-option
                          v-for="time in getAvailableStartTimes(slot.duration)"
                          :key="time.value"
                          :label="time.label"
                          :value="time.value"
                        />
                      </el-select>

                      <span class="end-time">{{ slot.endTime || "--:--" }}</span>

                      <el-button
                        type="danger"
                        size="small"
                        :icon="Delete"
                        @click="removeTimeSlot(index)"
                        class="delete-btn"
                      />
                    </div>
                  </div>
                </div>

                <div v-else class="empty-time-slots">
                  <el-empty description="请添加可上课时间段" :image-size="60" />
                </div>
              </div>
            </el-form-item>
          </div>

          <div
            class="search-actions"
            style="
              padding-top: 5px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
              :loading="searching"
              :disabled="!canSearch"
            >
              搜索匹配老师
            </el-button>
            <!-- 搜索提示 -->
            <div v-if="!hasTrialClassTime" class="search-tip">
              <el-text type="warning" size="small">
                ⚠️ 请先设置试听课时间，再搜索匹配老师
              </el-text>
            </div>
            <div v-else-if="searchForm.timeSlots.length === 0" class="search-tip">
              <el-text type="info" size="small">
                💡
                提示：已设置试听课时间，可以直接搜索选择老师。也可以添加正课时间段进行更精确匹配
              </el-text>
            </div>
            <div
              v-else-if="searchForm.timeSlots.length > 0 && !hasValidTimeSlots"
              class="search-tip"
            >
              <el-text type="warning" size="small">
                ⚠️ 请完善正课时间段信息（星期、开始时间、时长）
              </el-text>
            </div>
          </div>

          <!-- 基础筛选 -->
          <div class="basic-filters">
            <el-form-item label="关键词搜索">
              <el-input
                v-model="searchForm.keyword"
                placeholder="教师姓名或手机号"
                clearable
                size="small"
              />
            </el-form-item>

            <el-form-item label="教学组">
              <el-select
                v-model="searchForm.groupIds"
                placeholder="全部教学组"
                multiple
                clearable
                style="width: 100%"
                size="small"
              >
                <el-option
                  v-for="group in teachingGroups"
                  :key="group.id"
                  :label="group.name"
                  :value="group.id"
                />
              </el-select>
            </el-form-item>

            <div class="filter-row">
              <el-form-item label="性别">
                <el-select
                  v-model="searchForm.gender"
                  placeholder="不限"
                  clearable
                  size="small"
                >
                  <el-option label="男" value="0" />
                  <el-option label="女" value="1" />
                </el-select>
              </el-form-item>

              <el-form-item label="工作性质">
                <el-select
                  v-model="searchForm.employmentType"
                  placeholder="不限"
                  clearable
                  size="small"
                >
                  <el-option label="全职" value="全职" />
                  <el-option label="意向全职" value="意向全职" />
                  <el-option label="兼职" value="兼职" />
                </el-select>
              </el-form-item>
            </div>

            <el-form-item label="年龄范围">
              <div class="age-range">
                <el-input-number
                  v-model="searchForm.minAge"
                  placeholder="最小"
                  :min="18"
                  :max="70"
                  size="small"
                  style="width: 90px"
                />
                <span>-</span>
                <el-input-number
                  v-model="searchForm.maxAge"
                  placeholder="最大"
                  :min="18"
                  :max="70"
                  size="small"
                  style="width: 90px"
                />
                <span>岁</span>
              </div>
            </el-form-item>
          </div>

          <!-- 高级筛选 -->
          <el-collapse v-model="activeCollapse" class="advanced-filters">
            <el-collapse-item title="教育背景" name="education">
              <div class="filter-grid">
                <el-form-item label="学历">
                  <el-select
                    v-model="searchForm.education"
                    placeholder="不限"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="本科" value="本科" />
                    <el-option label="硕士" value="硕士" />
                    <el-option label="博士" value="博士" />
                  </el-select>
                </el-form-item>

                <el-form-item label="大学属性">
                  <el-select
                    v-model="searchForm.universityType"
                    placeholder="全部类型"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="双一流" value="双一流" />
                    <el-option label="985" value="985" />
                    <el-option label="211" value="211" />
                    <el-option label="一本" value="一本" />
                    <el-option label="普通" value="普通" />
                  </el-select>
                </el-form-item>

                <el-form-item label="师范类">
                  <el-select
                    v-model="searchForm.isNormalUniversity"
                    placeholder="不限"
                    clearable
                    size="small"
                  >
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </el-form-item>

                <el-form-item label="留学经历">
                  <el-select
                    v-model="searchForm.studyAbroad"
                    placeholder="全部"
                    clearable
                    size="small"
                  >
                    <el-option label="有" :value="true" />
                    <el-option label="无" :value="false" />
                  </el-select>
                </el-form-item>

                <el-form-item label="暑期课时间">
                  <el-select
                    v-model="searchForm.summerScheduleType"
                    placeholder="全部档期"
                    clearable
                    size="small"
                  >
                    <el-option label="全满档" value="full" />
                    <el-option label="黄金档" value="golden" />
                    <el-option label="其他档" value="other" />
                  </el-select>
                </el-form-item>
              </div>
            </el-collapse-item>

            <el-collapse-item title="教学资质" name="qualification">
              <div class="filter-grid">
                <el-form-item label="教资级别">
                  <el-select
                    v-model="searchForm.teachingCertificateLevel"
                    placeholder="不限"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="小学" value="小学" />
                    <el-option label="初中" value="初中" />
                    <el-option label="高中" value="高中" />
                  </el-select>
                </el-form-item>

                <el-form-item label="教授学科">
                  <el-select
                    v-model="searchForm.subjects"
                    placeholder="全部学科"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="英语" value="英语" />
                    <el-option label="语文" value="语文" />
                    <el-option label="数学" value="数学" />
                    <el-option label="物理" value="物理" />
                    <el-option label="化学" value="化学" />
                  </el-select>
                </el-form-item>

                <el-form-item label="已通过培训科目">
                  <el-select
                    v-model="searchForm.trainingSubjects"
                    placeholder="全部培训科目"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="音标" value="音标" />
                    <el-option label="语法" value="语法" />
                    <el-option label="阅读" value="阅读" />
                    <el-option label="听说" value="听说" />
                    <el-option label="写作" value="写作" />
                    <el-option label="完型" value="完型" />
                  </el-select>
                </el-form-item>

                <el-form-item label="英语资质">
                  <el-select
                    v-model="searchForm.englishQualification"
                    placeholder="全部资质"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="四级" value="四级" />
                    <el-option label="六级" value="六级" />
                    <el-option label="专四" value="专四" />
                    <el-option label="专八" value="专八" />
                    <el-option label="雅思" value="雅思" />
                    <el-option label="托福" value="托福" />
                  </el-select>
                </el-form-item>

                <el-form-item label="普通话资质">
                  <el-select
                    v-model="searchForm.mandarinQualification"
                    placeholder="全部等级"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="一级甲等" value="一级甲等" />
                    <el-option label="一级乙等" value="一级乙等" />
                    <el-option label="二级甲等" value="二级甲等" />
                    <el-option label="二级乙等" value="二级乙等" />
                  </el-select>
                </el-form-item>

                <el-form-item label="教龄范围">
                  <div class="age-range">
                    <el-input-number
                      v-model="searchForm.minTeachingYears"
                      placeholder="最少"
                      :min="0"
                      :max="50"
                      size="small"
                      style="width: 90px"
                    />
                    <span>-</span>
                    <el-input-number
                      v-model="searchForm.maxTeachingYears"
                      placeholder="最多"
                      :min="0"
                      :max="50"
                      size="small"
                      style="width: 90px"
                    />
                    <span>年</span>
                  </div>
                </el-form-item>
              </div>
            </el-collapse-item>

            <el-collapse-item title="教学风格" name="style">
              <div class="filter-grid">
                <el-form-item label="上课风格">
                  <el-select
                    v-model="searchForm.teachingStyle"
                    placeholder="不限"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="严格" value="严格" />
                    <el-option label="温和" value="温和" />
                    <el-option label="幽默" value="幽默" />
                    <el-option label="活泼" value="活泼" />
                    <el-option label="耐心" value="耐心" />
                  </el-select>
                </el-form-item>

                <el-form-item label="适合年级">
                  <el-select
                    v-model="searchForm.suitableGrades"
                    placeholder="不限"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option
                      v-for="grade in gradeOptions"
                      :key="grade.value"
                      :label="grade.label"
                      :value="grade.value"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="适合程度">
                  <el-select
                    v-model="searchForm.suitableLevels"
                    placeholder="不限"
                    multiple
                    clearable
                    size="small"
                  >
                    <el-option label="基础" value="基础" />
                    <el-option label="中等" value="中等" />
                    <el-option label="优秀" value="优秀" />
                  </el-select>
                </el-form-item>

                <el-form-item label="适合性格">
                  <el-select
                    v-model="searchForm.suitablePersonality"
                    placeholder="不限"
                    clearable
                    size="small"
                  >
                    <el-option label="内向" value="内向" />
                    <el-option label="外向" value="外向" />
                    <el-option label="活泼" value="活泼" />
                    <el-option label="安静" value="安静" />
                  </el-select>
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>

          <!-- 搜索按钮 -->
          <div class="search-actions">
            <el-button
              type="primary"
              :icon="Search"
              @click="handleSearch"
              :loading="searching"
              :disabled="!canSearch"
            >
              搜索匹配老师
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 右侧：结果展示和申请表单 -->
      <div class="result-panel">
        <!-- 已选筛选条件展示 -->
        <div v-if="hasActiveFilters" class="active-filters-display">
          <el-card class="filters-card">
            <template #header>
              <div class="filters-header">
                <span>当前筛选条件</span>
                <el-button type="text" size="small" @click="resetFilters"
                  >清空所有</el-button
                >
              </div>
            </template>
            <div class="filters-tags">
              <el-tag
                v-for="filter in activeFiltersList"
                :key="filter.key"
                :type="filter.type"
                closable
                @close="removeFilter(filter.key)"
                class="filter-tag"
              >
                {{ filter.label }}
              </el-tag>
            </div>
          </el-card>
        </div>

        <!-- 搜索结果 -->
        <div v-if="matchResult" class="search-results">
          <el-card class="results-card">
            <template #header>
              <div class="results-header">
                <span>匹配结果 ({{ matchResult.totalCount }}位老师)</span>
                <div class="header-actions">
                  <el-button
                    type="text"
                    size="small"
                    :icon="Refresh"
                    @click="handleSearch"
                    :loading="searching"
                  >
                    刷新
                  </el-button>
                </div>
              </div>
            </template>

            <div v-if="matchResult.totalCount === 0" class="empty-result">
              <el-empty description="没有找到匹配的老师，请调整筛选条件" />
            </div>

            <div v-else class="teachers-list">
              <div
                v-for="teacher in matchResult.teachers"
                :key="teacher.teacherId"
                class="teacher-card"
                :class="{ selected: selectedTeachers.includes(teacher.teacherId) }"
              >
                <div class="teacher-header">
                  <div class="teacher-basic">
                    <div class="teacher-name">
                      <span>{{ teacher.teacherName }}</span>
                      <el-tag
                        v-if="teacher.gender !== undefined"
                        size="small"
                        :type="teacher.gender === '1' ? 'danger' : 'primary'"
                      >
                        {{ teacher.gender === "1" ? "女" : "男" }}
                      </el-tag>
                    </div>
                    <div class="teacher-contact">{{ teacher.teacherPhone }}</div>
                  </div>

                  <div class="teacher-actions">
                    <!-- 只有在有完整时间段配置时才显示选择按钮 -->
                    <template v-if="hasCompleteTimeSlots">
                      <el-button
                        v-if="!selectedTeachers.includes(teacher.teacherId)"
                        type="primary"
                        size="small"
                        @click="selectTeacher(teacher.teacherId)"
                        :disabled="selectedTeachers.length >= 3"
                      >
                        选择
                      </el-button>
                      <el-button
                        v-else
                        type="success"
                        size="small"
                        @click="unselectTeacher(teacher.teacherId)"
                      >
                        已选择
                      </el-button>
                    </template>
                    <!-- 没有完整时间段配置时显示提示 -->
                    <el-text v-else type="info" size="small">
                      请添加时间段后重新匹配
                    </el-text>
                    <el-button
                      type="text"
                      size="small"
                      @click="viewTeacherDetail(teacher)"
                    >
                      详情
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      @click="viewTeacherSchedule(teacher)"
                    >
                      时间表
                    </el-button>
                  </div>
                </div>

                <div class="teacher-info">
                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">教学组:</span>
                      <span>{{ teacher.groupName || "未分组" }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">经验:</span>
                      <span>{{ teacher.teachingYears || 0 }}年</span>
                    </div>
                    <div class="info-item">
                      <span class="label">年龄:</span>
                      <span>{{ teacher.age || "--" }}岁</span>
                    </div>
                  </div>

                  <div class="info-row">
                    <div class="info-item">
                      <span class="label">学生:</span>
                      <span>{{ teacher.currentStudents || 0 }}人</span>
                    </div>
                    <div class="info-item">
                      <span class="label">性质:</span>
                      <el-tag
                        size="small"
                        :type="getEmploymentTypeTagType(teacher.employmentType)"
                      >
                        {{ getEmploymentTypeText(teacher.employmentType) }}
                      </el-tag>
                    </div>
                  </div>

                  <div class="info-row">
                    <div class="info-item full-width">
                      <span class="label">学科:</span>
                      <div class="subjects-tags">
                        <el-tag
                          v-for="subject in teacher.subjects"
                          :key="subject"
                          size="small"
                          class="subject-tag"
                        >
                          {{ subject }}
                        </el-tag>
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="teacher.education || teacher.universityType?.length"
                    class="info-row"
                  >
                    <div class="info-item">
                      <span class="label">学历:</span>
                      <span>{{ teacher.education || "--" }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">院校:</span>
                      <div class="university-tags">
                        <el-tag size="small" :type="getUniversityTypeTagType(type)">
                          {{ teacher.universityType }}
                        </el-tag>
                      </div>
                    </div>
                  </div>

                  <div v-if="teacher.teachingStyle?.length" class="info-row">
                    <div class="info-item full-width">
                      <span class="label">风格:</span>
                      <div class="style-tags">
                        <el-tag
                          v-for="style in teacher.teachingStyle"
                          :key="style"
                          size="small"
                          type="info"
                        >
                          {{ style }}
                        </el-tag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 申请表单 -->
        <div v-if="selectedTeachers.length > 0" class="application-form">
          <el-card class="application-card">
            <template #header>
              <div class="application-header">
                <span>提交预约申请</span>
                <span>
                  <el-tag type="info" size="small"
                    >已选择 {{ selectedTeachers.length }}/3 位老师</el-tag
                  >
                  <el-button @click="clearSelection" size="small">清空选择</el-button>
                  <el-button
                    type="primary"
                    size="small"
                    @click="submitApplication"
                    :loading="submittingApplication"
                    :disabled="selectedTeachers.length === 0"
                  >
                    提交申请
                  </el-button>
                </span>
              </div>
            </template>

            <el-form
              ref="applicationFormRef"
              :model="applicationForm"
              :rules="applicationRules"
              label-width="100px"
            >
              <el-form-item label="选中老师">
                <div class="selected-teachers">
                  <el-tag
                    v-for="teacherId in selectedTeachers"
                    :key="teacherId"
                    type="success"
                    closable
                    @close="unselectTeacher(teacherId)"
                    class="teacher-tag"
                  >
                    {{ getTeacherName(teacherId) }}
                  </el-tag>
                </div>
              </el-form-item>

              <!-- 试听课时间显示（只读） -->
              <el-form-item label="试听课时间">
                <div class="trial-time-display">
                  <el-input
                    :value="formatTrialClassTime"
                    readonly
                    placeholder="请先在上方设置试听课时间"
                    style="background-color: #f5f7fa"
                  />
                  <div class="trial-time-note">
                    <el-text type="info" size="small">
                      试听课时间已设定，不可在此修改。如需修改请返回上方重新设置。
                    </el-text>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="申请说明" prop="applyReason">
                <el-input
                  v-model="applicationForm.applyReason"
                  type="textarea"
                  :rows="4"
                  placeholder="请说明选择这些老师的原因，以及学生的特殊需求等（5-200字）"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>

        <!-- 空状态 -->
        <div v-if="!matchResult && !searching" class="empty-state">
          <el-empty description="请设置筛选条件并搜索匹配的老师" />
        </div>
      </div>
    </div>

    <!-- 老师详情弹窗 -->
    <TeacherDetailDialog v-model="showDetailDialog" :teacher="selectedTeacherForDetail" />

    <!-- 老师时间表弹窗 -->
    <TeacherScheduleDialog
      v-model="showScheduleDialog"
      :teacher="selectedTeacherForSchedule"
      :start-date="searchForm.startDate"
    />
  </div>
</template>

<script setup name="SalesBookingTeacherMatch">
// Props
const props = defineProps({
  studentInfo: {
    type: Object,
    default: null,
  },
  isDialogMode: {
    type: Boolean,
    default: false,
  },
});

// Emits
const emit = defineEmits(["success"]);

import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Delete, Search, Refresh, InfoFilled } from "@element-plus/icons-vue";
import { matchTeachersApi } from "@/api/management/teacherMatch";
import { getStudentsApi } from "@/api/management/student";
import { getTeachingGroupsApi } from "@/api/management/teachingGroup";
import {
  submitCourseBookingApplicationApi,
  submitCourseBookingApplicationWithTrialTimeApi,
} from "@/api/management/courseBookingApplication";
import {
  GRADE_OPTIONS,
  getGradeText,
  getGradesByStage,
  getDefaultSuitableGrades,
} from "@/utils/gradeUtils";
import TeacherScheduleDialog from "./components/TeacherScheduleDialog.vue";
import TeacherDetailDialog from "@/views/management/teacher/components/TeacherDetailDialog.vue";
import useUserStore from "@/store/modules/user";
import { checkRole } from "@/utils/permission";
import { useRoute } from "vue-router";

// 路由和用户store
const route = useRoute();
const userStore = useUserStore();

// 角色判断
const isSalesRole = computed(() => {
  const roles = userStore.roles || [];
  return roles.some(
    (role) =>
      role === "sales" ||
      role === "sales_group_leader" ||
      role === "sales_director" ||
      role === "销售" ||
      role === "销售组长" ||
      role === "销售总监"
  );
});

// 获取学生信息（支持弹窗模式和URL参数模式）
const studentInfo = computed(() => {
  // 弹窗模式：使用props传入的学生信息
  if (props.isDialogMode && props.studentInfo) {
    return props.studentInfo;
  }

  // URL参数模式：从URL获取学生信息
  const query = route.query;
  if (query.studentId) {
    return {
      id: query.studentId,
      name: query.studentName || "",
      phone: query.studentPhone || "",
      grade: query.studentGrade || "",
      school: query.studentSchool || "",
    };
  }

  return null;
});

// 是否锁定学生选择（弹窗模式或URL传入学生时锁定）
const isStudentLocked = computed(() => !!studentInfo.value);

// 响应式数据
const searching = ref(false);
const loadingStudents = ref(false);
const submittingApplication = ref(false);
const showDetailDialog = ref(false);
const showScheduleDialog = ref(false);
const activeCollapse = ref(["education", "qualification", "style"]);

// 搜索表单
const searchForm = reactive({
  studentId: "",
  startDate: "",
  timeSlots: [],
  keyword: "",
  groupIds: [],

  // 试听课时间（必填）
  trialClassTime: {
    date: new Date().toISOString().split("T")[0], // 默认今天，格式化为YYYY-MM-DD
    startTime: null,
    endTime: null
  },

  // 基础信息筛选
  gender: "",
  minAge: null,
  maxAge: null,
  employmentType: "",
  currentStatus: "",

  // 教育背景筛选
  education: [],
  universityType: [],
  isNormalUniversity: null,
  studyAbroad: null,

  // 教学资质筛选
  teachingCertificateLevel: [],
  subjects: [],
  trainingSubjects: [],
  englishQualification: [],
  mandarinQualification: [],
  minTeachingYears: null,
  maxTeachingYears: null,

  // 教学风格筛选
  teachingStyle: [],
  suitableGrades: [],
  suitableLevels: [],
  suitablePersonality: "",

  // 暑期课上课时间筛选
  summerScheduleType: "",
});

// 申请表单
const applicationForm = reactive({
  applyReason: "",
});

// 申请表单验证规则
const applicationRules = computed(() => ({
  applyReason: [
    { required: true, message: "请填写申请说明", trigger: "blur" },
    { min: 5, max: 200, message: "申请说明长度在5到200个字符", trigger: "blur" },
  ],
}));

// 选项数据
const studentOptions = ref([]);
const teachingGroups = ref([]);
const matchResult = ref(null);
const selectedTeachers = ref([]);
const currentSelectedStudent = ref(null);
const selectedTeacherForDetail = ref(null);
const selectedTeacherForSchedule = ref(null);
const applicationFormRef = ref(null);

// 星期选项
const weekDays = [
  { label: "周一", value: 1 },
  { label: "周二", value: 2 },
  { label: "周三", value: 3 },
  { label: "周四", value: 4 },
  { label: "周五", value: 5 },
  { label: "周六", value: 6 },
  { label: "周日", value: 7 },
];

// 时长选项
const durationOptions = [
  { label: "60分钟", value: 60 },
  { label: "90分钟", value: 90 },
  { label: "120分钟", value: 120 },
];

// 年级选项
const gradeOptions = GRADE_OPTIONS;

// 生成时间选项
const generateTimeOptions = () => {
  const options = [];
  for (let hour = 6; hour <= 24; hour++) {
    const maxMinute = hour === 24 ? 0 : 60;
    for (let minute = 0; minute < maxMinute; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        label: timeStr,
        value: timeStr,
      });
    }
  }
  return options;
};

const timeOptions = generateTimeOptions();

// 试听课时间选项（每5分钟一个选项）
const trialTimeOptions = ref([]);

// 生成试听课时间选项
function generateTrialTimeOptions() {
  const options = [];
  for (let hour = 6; hour <= 23; hour++) {
    for (let minute = 0; minute < 60; minute += 5) {
      const timeStr = `${hour.toString().padStart(2, "0")}:${minute
        .toString()
        .padStart(2, "0")}`;
      options.push({
        value: timeStr,
        label: timeStr,
      });
    }
  }
  trialTimeOptions.value = options;
}

// 初始化试听课时间选项
generateTrialTimeOptions();

// 监听开始时间变化，重置不符合要求的结束时间
watch(
  () => searchForm.trialClassTime.startTime,
  (newStartTime, oldStartTime) => {
    if (newStartTime !== oldStartTime && searchForm.trialClassTime.endTime) {
      // 检查当前结束时间是否仍然有效（必须比开始时间晚超过1小时）
      if (newStartTime) {
        const [startHour, startMinute] = newStartTime.split(":").map(Number);
        const [endHour, endMinute] = searchForm.trialClassTime.endTime
          .split(":")
          .map(Number);

        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;

        // 如果结束时间不满足超过1小时的要求，重置结束时间
        if (endMinutes <= startMinutes + 60) {
          searchForm.trialClassTime.endTime = null;
        }
      } else {
        // 如果开始时间被清空，也清空结束时间
        searchForm.trialClassTime.endTime = null;
      }
    }
  }
);

// 计算属性
const hasTrialClassTime = computed(() => {
  const time = searchForm.trialClassTime;
  return time && time.date && time.startTime && time.endTime;
});

const formatTrialClassTime = computed(() => {
  if (!hasTrialClassTime.value) {
    return "";
  }

  const time = searchForm.trialClassTime;
  const date = new Date(time.date);
  const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`;
  const weekdayMap = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  const weekdayText = weekdayMap[date.getDay()];

  const startTime = time.startTime || "";
  const endTime = time.endTime || "";

  return `${dateStr}(${weekdayText}) ${startTime}-${endTime}`;
});

// 可用的结束时间选项（基于开始时间，必须超过1小时）
const availableEndTimes = computed(() => {
  if (!searchForm.trialClassTime.startTime) {
    return [];
  }

  const startTime = searchForm.trialClassTime.startTime;
  const [startHour, startMinute] = startTime.split(":").map(Number);
  const startMinutes = startHour * 60 + startMinute;

  // 结束时间必须比开始时间晚超过1小时（60分钟）
  const minEndMinutes = startMinutes + 60;

  const options = [];
  for (let hour = 6; hour <= 23; hour++) {
    for (let minute = 0; minute < 60; minute += 5) {
      const totalMinutes = hour * 60 + minute;
      if (totalMinutes >= minEndMinutes) {
        const timeStr = `${hour.toString().padStart(2, "0")}:${minute
          .toString()
          .padStart(2, "0")}`;
        options.push({
          value: timeStr,
          label: timeStr,
        });
      }
    }
  }

  return options;
});

const canSearch = computed(() => {
  // 基础条件：必须有学生ID、开始日期和试听课时间
  if (!searchForm.studentId || !searchForm.startDate || !hasTrialClassTime.value) {
    return false;
  }

  // 如果没有时间段记录，可以搜索
  if (searchForm.timeSlots.length === 0) {
    return true;
  }

  // 如果有时间段记录，必须每个时间段都填写完整
  return searchForm.timeSlots.every(
    (slot) => slot.weekday && slot.startTime && slot.duration
  );
});

// 是否有完整的正课时间段配置（用于提示信息）
const hasValidTimeSlots = computed(() => {
  return (
    searchForm.timeSlots.length > 0 &&
    searchForm.timeSlots.every((slot) => slot.weekday && slot.startTime && slot.duration)
  );
});

// 是否有完整的时间段配置（用于判断是否显示选择按钮）
// 修改逻辑：只要试听课时间完整就可以选择老师，正课时间段变为可选
const hasCompleteTimeSlots = computed(() => {
  // 只需要试听课时间完整即可
  return hasTrialClassTime.value;
});

const hasActiveFilters = computed(() => {
  return (
    searchForm.keyword ||
    searchForm.groupIds.length > 0 ||
    searchForm.gender ||
    searchForm.minAge ||
    searchForm.maxAge ||
    searchForm.employmentType ||
    searchForm.currentStatus ||
    searchForm.education.length > 0 ||
    searchForm.universityType.length > 0 ||
    searchForm.isNormalUniversity !== null ||
    searchForm.studyAbroad !== null ||
    searchForm.teachingCertificateLevel.length > 0 ||
    searchForm.subjects.length > 0 ||
    searchForm.englishQualification.length > 0 ||
    searchForm.mandarinQualification.length > 0 ||
    searchForm.minTeachingYears ||
    searchForm.maxTeachingYears ||
    searchForm.teachingStyle.length > 0 ||
    searchForm.suitableGrades.length > 0 ||
    searchForm.suitableLevels.length > 0 ||
    searchForm.suitablePersonality
  );
});

const activeFiltersList = computed(() => {
  const filters = [];

  // 基础筛选条件
  if (searchForm.keyword) {
    filters.push({
      key: "keyword",
      label: `关键词: ${searchForm.keyword}`,
      type: "info",
    });
  }

  if (searchForm.groupIds.length > 0) {
    const groupNames = searchForm.groupIds.map((id) => {
      const group = teachingGroups.value.find((g) => g.id === id);
      return group?.name || id;
    });
    filters.push({
      key: "groupIds",
      label: `教学组: ${groupNames.join(", ")}`,
      type: "info",
    });
  }

  if (searchForm.gender) {
    const genderText =
      searchForm.gender === "0" ? "男" : searchForm.gender === "1" ? "女" : "未知";
    filters.push({ key: "gender", label: `性别: ${genderText}`, type: "primary" });
  }

  if (searchForm.minAge || searchForm.maxAge) {
    const ageText = `${searchForm.minAge || ""}${
      searchForm.minAge && searchForm.maxAge ? "-" : ""
    }${searchForm.maxAge || ""}岁`;
    filters.push({ key: "age", label: `年龄: ${ageText}`, type: "primary" });
  }

  if (searchForm.employmentType) {
    filters.push({
      key: "employmentType",
      label: `性质: ${searchForm.employmentType}`,
      type: "success",
    });
  }

  if (searchForm.currentStatus) {
    filters.push({
      key: "currentStatus",
      label: `状态: ${searchForm.currentStatus}`,
      type: "success",
    });
  }

  // 教育背景筛选条件
  if (searchForm.education.length > 0) {
    filters.push({
      key: "education",
      label: `学历: ${searchForm.education.join(", ")}`,
      type: "warning",
    });
  }

  if (searchForm.universityType.length > 0) {
    filters.push({
      key: "universityType",
      label: `院校: ${searchForm.universityType.join(", ")}`,
      type: "warning",
    });
  }

  if (searchForm.isNormalUniversity !== null) {
    const normalText = searchForm.isNormalUniversity ? "是" : "否";
    filters.push({
      key: "isNormalUniversity",
      label: `师范类: ${normalText}`,
      type: "warning",
    });
  }

  if (searchForm.studyAbroad !== null) {
    const abroadText = searchForm.studyAbroad ? "有" : "无";
    filters.push({
      key: "studyAbroad",
      label: `留学经历: ${abroadText}`,
      type: "warning",
    });
  }

  // 教学资质筛选条件
  if (searchForm.teachingCertificateLevel.length > 0) {
    filters.push({
      key: "teachingCertificateLevel",
      label: `教资级别: ${searchForm.teachingCertificateLevel.join(", ")}`,
      type: "danger",
    });
  }

  if (searchForm.subjects.length > 0) {
    filters.push({
      key: "subjects",
      label: `教授学科: ${searchForm.subjects.join(", ")}`,
      type: "danger",
    });
  }

  if (searchForm.trainingSubjects.length > 0) {
    filters.push({
      key: "trainingSubjects",
      label: `培训科目: ${searchForm.trainingSubjects.join(", ")}`,
      type: "danger",
    });
  }

  if (searchForm.englishQualification.length > 0) {
    filters.push({
      key: "englishQualification",
      label: `英语资质: ${searchForm.englishQualification.join(", ")}`,
      type: "danger",
    });
  }

  if (searchForm.mandarinQualification.length > 0) {
    filters.push({
      key: "mandarinQualification",
      label: `普通话: ${searchForm.mandarinQualification.join(", ")}`,
      type: "danger",
    });
  }

  if (searchForm.minTeachingYears || searchForm.maxTeachingYears) {
    const yearsText = `${searchForm.minTeachingYears || ""}${
      searchForm.minTeachingYears && searchForm.maxTeachingYears ? "-" : ""
    }${searchForm.maxTeachingYears || ""}年`;
    filters.push({ key: "teachingYears", label: `教龄: ${yearsText}`, type: "danger" });
  }

  // 教学风格筛选条件
  if (searchForm.teachingStyle.length > 0) {
    filters.push({
      key: "teachingStyle",
      label: `上课风格: ${searchForm.teachingStyle.join(", ")}`,
      type: "info",
    });
  }

  if (searchForm.suitableGrades.length > 0) {
    const gradeTexts = searchForm.suitableGrades.map((grade) => getGradeText(grade));
    filters.push({
      key: "suitableGrades",
      label: `适合年级: ${gradeTexts.join(", ")}`,
      type: "info",
    });
  }

  if (searchForm.suitableLevels.length > 0) {
    filters.push({
      key: "suitableLevels",
      label: `适合程度: ${searchForm.suitableLevels.join(", ")}`,
      type: "info",
    });
  }

  if (searchForm.suitablePersonality) {
    filters.push({
      key: "suitablePersonality",
      label: `适合性格: ${searchForm.suitablePersonality}`,
      type: "info",
    });
  }

  // 暑期课时间筛选条件
  if (searchForm.summerScheduleType) {
    const scheduleText =
      searchForm.summerScheduleType === "full"
        ? "全满档"
        : searchForm.summerScheduleType === "golden"
        ? "黄金档"
        : "其他档";
    filters.push({
      key: "summerScheduleType",
      label: `暑期课时间: ${scheduleText}`,
      type: "warning",
    });
  }

  return filters;
});

// 禁用日期函数 - 开始日期（不能选择过去的日期）
const disabledDate = (time) => {
  // 禁用今天之前的日期
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
};

// 试听课日期禁用函数 - 不能选择过去时间
const disabledTrialDate = (time) => {
  // 获取今天的开始时间（00:00:00）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 禁用今天之前的日期
  return time.getTime() < today.getTime();
};

// 方法
async function searchStudents(query) {
  if (!query) return;

  loadingStudents.value = true;
  try {
    const { data } = await getStudentsApi({
      keyword: query,
      pageNum: 1,
      pageSize: 20,
    });
    studentOptions.value = data?.records || [];
  } catch (error) {
    console.error("搜索学生失败:", error);
  } finally {
    loadingStudents.value = false;
  }
}

function handleStudentChange() {
  if (searchForm.studentId && studentOptions.value.length > 0) {
    const student = studentOptions.value.find(
      (s) => String(s.id) === String(searchForm.studentId)
    );
    if (student) {
      currentSelectedStudent.value = student;

      // 根据学生年级设置默认的适合年级筛选条件
      if (student.grade) {
        searchForm.suitableGrades = getDefaultSuitableGrades(student.grade);
      }
    }
  }
}

function addTimeSlot() {
  if (searchForm.timeSlots.length >= 5) {
    ElMessage.warning("最多只能添加5个时间段");
    return;
  }

  searchForm.timeSlots.push({
    weekday: "",
    startTime: "",
    duration: 60,
    endTime: "",
  });
}

function removeTimeSlot(index) {
  searchForm.timeSlots.splice(index, 1);
}

function updateEndTime(slot) {
  if (!slot.startTime || !slot.duration) {
    slot.endTime = "";
    return;
  }

  const [startHour, startMinute] = slot.startTime.split(":").map(Number);
  const startTotalMinutes = startHour * 60 + startMinute;
  const endTotalMinutes = startTotalMinutes + slot.duration;

  const endHour = Math.floor(endTotalMinutes / 60);
  const endMinute = endTotalMinutes % 60;

  if (endHour >= 24) {
    slot.endTime =
      "次日 " +
      String(endHour - 24).padStart(2, "0") +
      ":" +
      String(endMinute).padStart(2, "0");
  } else {
    slot.endTime =
      String(endHour).padStart(2, "0") + ":" + String(endMinute).padStart(2, "0");
  }
}

function getAvailableStartTimes(duration) {
  if (!duration) {
    return timeOptions;
  }

  const availableOptions = [];

  timeOptions.forEach((timeOption) => {
    const startTime = timeOption.value;
    const [startHour, startMinute] = startTime.split(":").map(Number);
    const startTotalMinutes = startHour * 60 + startMinute;
    const endTotalMinutes = startTotalMinutes + duration;

    // 允许跨越午夜，但不超过次日6点
    if (endTotalMinutes <= 24 * 60 + 6 * 60) {
      availableOptions.push(timeOption);
    }
  });

  return availableOptions;
}

async function handleSearch() {
  if (!canSearch.value) {
    ElMessage.warning("请完善搜索条件");
    return;
  }

  searching.value = true;
  selectedTeachers.value = [];
  applicationForm.applyReason = "";

  try {
    // 构建请求参数
    const requestParams = {
      studentId: searchForm.studentId,
      startDate: searchForm.startDate,
      timeSlots: searchForm.timeSlots,
      keyword: searchForm.keyword,
      groupIds: searchForm.groupIds,
    };

    // 只有当试听课时间完整时才添加到请求参数中
    if (hasTrialClassTime.value) {
      requestParams.trialClassTime = {
        date: searchForm.trialClassTime.date,
        startTime: searchForm.trialClassTime.startTime,
        endTime: searchForm.trialClassTime.endTime,
      };

      // 调试信息：打印试听课时间参数
      console.log("发送的试听课时间参数:", requestParams.trialClassTime);
    }

    // 添加有值的筛选条件
    if (searchForm.gender) requestParams.gender = searchForm.gender;
    if (searchForm.minAge) requestParams.minAge = searchForm.minAge;
    if (searchForm.maxAge) requestParams.maxAge = searchForm.maxAge;
    if (searchForm.employmentType)
      requestParams.employmentType = searchForm.employmentType;
    if (searchForm.currentStatus) requestParams.currentStatus = searchForm.currentStatus;
    if (searchForm.education.length > 0) requestParams.education = searchForm.education;
    if (searchForm.universityType.length > 0)
      requestParams.universityType = searchForm.universityType;
    if (searchForm.isNormalUniversity !== null)
      requestParams.isNormalUniversity = searchForm.isNormalUniversity;
    if (searchForm.studyAbroad !== null)
      requestParams.studyAbroad = searchForm.studyAbroad;
    if (searchForm.teachingCertificateLevel.length > 0)
      requestParams.teachingCertificateLevel = searchForm.teachingCertificateLevel;
    if (searchForm.subjects.length > 0) requestParams.subjects = searchForm.subjects;
    if (searchForm.trainingSubjects.length > 0)
      requestParams.trainingSubjects = searchForm.trainingSubjects;
    if (searchForm.englishQualification.length > 0)
      requestParams.englishQualification = searchForm.englishQualification;
    if (searchForm.mandarinQualification.length > 0)
      requestParams.mandarinQualification = searchForm.mandarinQualification;
    if (searchForm.minTeachingYears)
      requestParams.minTeachingYears = searchForm.minTeachingYears;
    if (searchForm.maxTeachingYears)
      requestParams.maxTeachingYears = searchForm.maxTeachingYears;
    if (searchForm.teachingStyle.length > 0)
      requestParams.teachingStyle = searchForm.teachingStyle;
    if (searchForm.suitableGrades.length > 0)
      requestParams.suitableGrades = searchForm.suitableGrades;
    if (searchForm.suitableLevels.length > 0)
      requestParams.suitableLevels = searchForm.suitableLevels;
    if (searchForm.suitablePersonality)
      requestParams.suitablePersonality = searchForm.suitablePersonality;
    if (searchForm.summerScheduleType)
      requestParams.summerScheduleType = searchForm.summerScheduleType;

    const { data } = await matchTeachersApi(requestParams);
    matchResult.value = data;

    if (data.totalCount === 0) {
      ElMessage.info("没有找到匹配的教师，请调整搜索条件");
    } else {
      ElMessage.success(`找到 ${data.totalCount} 位匹配的教师`);
    }
  } catch (error) {
    console.error("匹配教师失败:", error);
  } finally {
    searching.value = false;
  }
}

function resetFilters() {
  // 保留学生、日期和时间段，重置其他筛选条件
  const preservedData = {
    studentId: searchForm.studentId,
    startDate: searchForm.startDate,
    timeSlots: [...searchForm.timeSlots],
  };

  Object.assign(searchForm, {
    studentId: preservedData.studentId,
    startDate: preservedData.startDate,
    timeSlots: preservedData.timeSlots,
    keyword: "",
    groupIds: [],
    gender: "",
    minAge: null,
    maxAge: null,
    employmentType: "",
    currentStatus: "",
    education: [],
    universityType: [],
    isNormalUniversity: null,
    studyAbroad: null,
    teachingCertificateLevel: [],
    subjects: [],
    trainingSubjects: [],
    englishQualification: [],
    mandarinQualification: [],
    minTeachingYears: null,
    maxTeachingYears: null,
    teachingStyle: [],
    suitableGrades: [],
    suitableLevels: [],
    suitablePersonality: "",
    summerScheduleType: "",
  });

  ElMessage.success("筛选条件已重置");
}

function removeFilter(filterKey) {
  switch (filterKey) {
    // 基础筛选条件
    case "keyword":
      searchForm.keyword = "";
      break;
    case "groupIds":
      searchForm.groupIds = [];
      break;
    case "gender":
      searchForm.gender = "";
      break;
    case "age":
      searchForm.minAge = null;
      searchForm.maxAge = null;
      break;
    case "employmentType":
      searchForm.employmentType = "";
      break;
    case "currentStatus":
      searchForm.currentStatus = "";
      break;

    // 教育背景筛选条件
    case "education":
      searchForm.education = [];
      break;
    case "universityType":
      searchForm.universityType = [];
      break;
    case "isNormalUniversity":
      searchForm.isNormalUniversity = null;
      break;
    case "studyAbroad":
      searchForm.studyAbroad = null;
      break;

    // 教学资质筛选条件
    case "teachingCertificateLevel":
      searchForm.teachingCertificateLevel = [];
      break;
    case "subjects":
      searchForm.subjects = [];
      break;
    case "trainingSubjects":
      searchForm.trainingSubjects = [];
      break;
    case "englishQualification":
      searchForm.englishQualification = [];
      break;
    case "mandarinQualification":
      searchForm.mandarinQualification = [];
      break;
    case "teachingYears":
      searchForm.minTeachingYears = null;
      searchForm.maxTeachingYears = null;
      break;

    // 教学风格筛选条件
    case "teachingStyle":
      searchForm.teachingStyle = [];
      break;
    case "suitableGrades":
      searchForm.suitableGrades = [];
      break;
    case "suitableLevels":
      searchForm.suitableLevels = [];
      break;
    case "suitablePersonality":
      searchForm.suitablePersonality = "";
      break;

    // 暑期课时间筛选条件
    case "summerScheduleType":
      searchForm.summerScheduleType = "";
      break;
  }
}

function selectTeacher(teacherId) {
  if (selectedTeachers.value.length >= 3) {
    ElMessage.warning("最多只能选择3位老师");
    return;
  }

  if (!selectedTeachers.value.includes(teacherId)) {
    selectedTeachers.value.push(teacherId);
  }
}

function unselectTeacher(teacherId) {
  const index = selectedTeachers.value.indexOf(teacherId);
  if (index > -1) {
    selectedTeachers.value.splice(index, 1);
  }
}

function clearSelection() {
  selectedTeachers.value = [];
  applicationForm.applyReason = "";
  // 注意：不重置试听课时间，因为它现在在搜索表单中
}

function getTeacherName(teacherId) {
  const teacher = matchResult.value?.teachers?.find((t) => t.teacherId === teacherId);
  return teacher?.teacherName || teacherId;
}

function viewTeacherDetail(teacher) {
  selectedTeacherForDetail.value = {
    id: teacher.teacherId,
    name: teacher.teacherName,
    phone: teacher.teacherPhone,
    ...teacher,
  };
  showDetailDialog.value = true;
}

function viewTeacherSchedule(teacher) {
  selectedTeacherForSchedule.value = {
    teacherId: teacher.teacherId,
    teacherName: teacher.teacherName,
    teacherPhone: teacher.teacherPhone,
    ...teacher,
  };
  showScheduleDialog.value = true;
}

async function submitApplication() {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning("请至少选择一位老师");
    return;
  }

  try {
    await applicationFormRef.value.validate();

    submittingApplication.value = true;

    const applicationData = {
      studentId: searchForm.studentId,
      subject: "英语",
      specification: "单词课",
      preferredTeachers: selectedTeachers.value,

      // 试听课时间（必填）
      trialClassTime: {
        date: searchForm.trialClassTime.date,
        startTime: searchForm.trialClassTime.startTime,
        endTime: searchForm.trialClassTime.endTime,
      },

      // 偏好时间段（可选）
      preferredTimeSlots: searchForm.timeSlots.map((slot) => ({
        weekday: slot.weekday,
        startTime: slot.startTime,
        endTime: slot.endTime,
        priority: 1,
      })),

      applicationReason: applicationForm.applyReason,
    };

    // 调用支持试听课时间的API
    await submitCourseBookingApplicationWithTrialTimeApi(applicationData);

    ElMessage.success("申请提交成功！");

    // 重置表单
    clearSelection();

    // 如果是弹窗模式，触发成功事件
    if (props.isDialogMode) {
      emit("success");
    }
  } catch (error) {
    console.error("提交申请失败:", error);
  } finally {
    submittingApplication.value = false;
  }
}

// 工具方法
function getMatchRateType(percentage) {
  if (percentage >= 80) return "success";
  if (percentage >= 60) return "warning";
  return "danger";
}

function getEmploymentTypeTagType(type) {
  const typeMap = {
    全职: "success",
    意向全职: "warning",
    兼职: "info",
  };
  return typeMap[type] || "info";
}

function getEmploymentTypeText(type) {
  const textMap = {
    full_time: "全职",
    intended_full_time: "意向全职",
    part_time: "兼职",
  };
  return textMap[type] || type;
}

function getUniversityTypeTagType(type) {
  const typeMap = {
    985: "danger",
    211: "warning",
    双一流: "primary",
    一本: "success",
    普通: "info",
  };
  return typeMap[type] || "info";
}

// 初始化
async function loadTeachingGroups() {
  try {
    const { data } = await getTeachingGroupsApi({
      pageNum: 1,
      pageSize: 100,
    });
    teachingGroups.value = data?.records || [];
  } catch (error) {
    console.error("加载教学组失败:", error);
  }
}

onMounted(() => {
  resetPageState();
  loadTeachingGroups();

  // 设置默认开始日期为今天
  searchForm.startDate = new Date().toISOString().split("T")[0];

  // 添加默认时间段
  // addTimeSlot();

  // 处理传入的学生信息（弹窗模式或URL参数模式）
  if (studentInfo.value) {
    searchForm.studentId = studentInfo.value.id;
    currentSelectedStudent.value = studentInfo.value;

    // 根据学生年级设置默认的适合年级筛选条件
    if (studentInfo.value.grade) {
      searchForm.suitableGrades = getDefaultSuitableGrades(studentInfo.value.grade);
    }
  }
});

// 监听学生信息变化，重置页面状态
watch(
  () => studentInfo.value,
  (newStudentInfo, oldStudentInfo) => {
    // 在弹窗模式下，当学生信息发生变化时重置页面状态
    if (props.isDialogMode) {
      // 情况1：从null变为有值（弹窗打开）
      // 情况2：从有值变为不同的值（切换学生）
      const shouldReset =
        (newStudentInfo && !oldStudentInfo) || // 从null变为有值
        (newStudentInfo && oldStudentInfo && newStudentInfo.id !== oldStudentInfo.id); // 学生ID变化

      if (shouldReset) {
        console.log("学生信息变化，重置页面状态", {
          old: oldStudentInfo?.name || "null",
          new: newStudentInfo?.name || "null",
        });

        // 重置页面状态
        resetPageState();

        // 重新设置学生信息
        if (newStudentInfo) {
          searchForm.studentId = newStudentInfo.id;
          currentSelectedStudent.value = newStudentInfo;

          // 根据新学生年级设置默认的适合年级筛选条件
          if (newStudentInfo.grade) {
            searchForm.suitableGrades = getDefaultSuitableGrades(newStudentInfo.grade);
          }
        }
      }
    }
  },
  { deep: true }
);

// 重置页面状态的函数
function resetPageState() {
  // 清空匹配结果
  matchResult.value = null;

  // 清空选中的教师
  selectedTeachers.value = [];

  // 重置搜索状态
  searching.value = false;
  submittingApplication.value = false;

  // 重置筛选条件（只保留学生信息）
  const preservedStudentId = searchForm.studentId;

  Object.assign(searchForm, {
    studentId: preservedStudentId,
    startDate: (() => {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    })(),
    timeSlots: [],
    // 重置试听课时间
    trialClassTime: {
      date: new Date().toISOString().split("T")[0], // 默认今天，格式化为YYYY-MM-DD
      startTime: null,
      endTime: null,
    },
    keyword: "",
    groupIds: [],
    gender: "",
    minAge: null,
    maxAge: null,
    employmentType: "",
    currentStatus: "",
    education: [],
    universityType: [],
    isNormalUniversity: null,
    studyAbroad: null,
    teachingCertificateLevel: [],
    subjects: [],
    englishQualification: [],
    mandarinQualification: [],
    minTeachingYears: null,
    maxTeachingYears: null,
    teachingStyle: [],
    suitableGrades: [],
    suitableLevels: [],
    suitablePersonality: "",
  });

  // 重置申请表单
  applicationForm.applyReason = "";

  // 关闭所有弹窗
  showDetailDialog.value = false;
  showScheduleDialog.value = false;
  selectedTeacherForDetail.value = null;
  selectedTeacherForSchedule.value = null;
}
</script>

<style lang="scss" scoped>
.sales-booking-container {
  padding: 5px;
  height: 100vh;
  overflow: hidden;
  .el-empty {
    --el-empty-padding: 5px, 0;
  }
  .page-header {
    margin-bottom: 20px;

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .booking-layout {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);
  }

  // 左侧筛选面板
  .filter-panel {
    width: 480px;
    flex-shrink: 0;

    .filter-card {
      height: 100%;
      overflow: hidden;

      .filter-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: 600;
      }

      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow-y: auto;
        padding: 16px;
      }
    }

    .student-section {
      margin-bottom: 20px;

      .locked-student-info {
        padding: 16px;
        background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
        border-radius: 8px;
        border: 2px solid #409eff;
        position: relative;

        &:before {
          content: "🔒";
          position: absolute;
          top: 8px;
          right: 12px;
          font-size: 16px;
        }

        .student-name {
          font-weight: 600;
          color: #303133;
          font-size: 16px;
          margin-bottom: 8px;
        }

        .student-details {
          display: flex;
          gap: 16px;
          font-size: 14px;
          color: #606266;

          span {
            display: flex;
            align-items: center;

            &:before {
              content: "•";
              margin-right: 4px;
              color: #909399;
            }

            &:first-child:before {
              display: none;
            }
          }
        }
      }

      .selected-student-info {
        margin-top: 12px;
        padding: 12px;
        background-color: #f0f9ff;
        border: 1px solid #bfdbfe;
        border-radius: 6px;

        .student-name {
          font-size: 16px;
          font-weight: 600;
          color: #1e40af;
          margin-bottom: 4px;
        }

        .student-details {
          display: flex;
          gap: 12px;
          font-size: 14px;
          color: #64748b;

          span {
            &:not(:last-child)::after {
              content: "|";
              margin-left: 12px;
              color: #cbd5e1;
            }
          }
        }
      }
    }

    .time-section {
      margin-bottom: 20px;

      .time-slots-container {
        width: 100%;
        .time-slots-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding: 8px 12px;
          background-color: #f5f7fa;
          border-radius: 4px;

          span {
            font-weight: 500;
            color: #303133;
            font-size: 14px;
          }
        }

        .time-slots-list {
          .time-slot-item {
            margin-bottom: 10px;
            padding: 12px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            background-color: #fafbfc;

            &:last-child {
              margin-bottom: 0;
            }

            .slot-controls {
              display: flex;
              align-items: center;
              gap: 6px;
              flex-wrap: wrap;

              .end-time {
                color: #606266;
                font-size: 13px;
                font-weight: 500;
                min-width: 60px;
                text-align: center;
                // background-color: #f5f7fa;
                // padding: 4px 8px;
                // border-radius: 3px;
                // border: 1px solid #e4e7ed;
              }

              .delete-btn {
                width: 28px;
                height: 28px;
                flex-shrink: 0;
              }
            }
          }
        }

        .empty-time-slots {
          text-align: center;
          //   padding: 30px 15px;
        }
      }
    }

    .basic-filters {
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        gap: 12px;

        .el-form-item {
          flex: 1;
        }
      }

      .age-range {
        display: flex;
        align-items: center;
        gap: 8px;

        span {
          color: #606266;
          font-size: 14px;
        }
      }
    }

    .advanced-filters {
      margin-bottom: 20px;

      .filter-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .el-form-item {
          margin-bottom: 8px;
        }
      }
    }

    .search-actions {
      text-align: center;
      padding-top: 16px;
      border-top: 1px solid #e4e7ed;

      .search-tip {
        margin-top: 8px;
        padding: 8px 12px;
        background-color: #f4f4f5;
        border-radius: 4px;
        border-left: 3px solid #409eff;
        text-align: left;
      }
    }
  }

  // 右侧结果面板
  .result-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: hidden;

    .active-filters-display {
      .filters-card {
        .filters-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
        }

        .filters-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .filter-tag {
            margin: 0;
          }
        }
      }
    }

    .search-results {
      flex: 1;
      overflow: hidden;

      .results-card {
        height: 100%;
        overflow: hidden;

        .results-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
        }

        :deep(.el-card__body) {
          height: calc(100% - 60px);
          overflow-y: auto;
          padding: 5px;
        }

        .empty-result {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .teachers-list {
          padding-bottom: 16px; // 添加底部内边距确保最后一条记录完整显示

          .teacher-card {
            margin-bottom: 16px;
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            background-color: #fff;
            transition: all 0.2s ease;

            &:last-child {
              margin-bottom: 16px; // 保持最后一个卡片的底部间距
            }

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
            }

            &.selected {
              border-color: #67c23a;
              background-color: #f0f9ff;
            }

            .teacher-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 12px;

              .teacher-basic {
                .teacher-name {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #303133;
                  margin-bottom: 4px;
                }

                .teacher-contact {
                  font-size: 14px;
                  color: #909399;
                }
              }

              .teacher-actions {
                display: flex;
                gap: 8px;
              }
            }

            .teacher-info {
              .info-row {
                display: flex;
                gap: 16px;
                margin-bottom: 8px;

                &:last-child {
                  margin-bottom: 0;
                }

                .info-item {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 14px;

                  &.full-width {
                    flex: 1;
                  }

                  .label {
                    color: #909399;
                    font-weight: 500;
                  }

                  .match-rate {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                  }

                  .subjects-tags,
                  .university-tags,
                  .style-tags {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 4px;
                  }

                  .subject-tag {
                    background-color: #e1f3d8;
                    border-color: #67c23a;
                    color: #67c23a;
                  }
                }
              }
            }
          }
        }
      }
    }

    .application-form {
      margin-top: 20px;
      margin-bottom: 60px;

      .application-card {
        // 确保卡片内容完整显示
        .el-card__body {
          padding: 20px;
        }
        .application-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-weight: 600;
          flex-wrap: wrap;
          gap: 12px;
          min-height: 20px;

          > span:last-child {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
          }
        }

        .selected-teachers {
          min-height: 32px;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          align-items: center;

          .teacher-tag {
            background-color: #e1f3d8;
            border-color: #67c23a;
            color: #67c23a;
          }
        }

        .trial-time-container {
          .trial-time-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 500;
          }

          .trial-time-selector {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 8px;
          }

          .trial-time-tips {
            font-size: 12px;
            color: #909399;
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }

        .trial-time-display {
          .trial-time-note {
            margin-top: 8px;
            font-size: 12px;
            color: #909399;
          }
        }

        .trial-time-section {
          .section-header {
            display: flex;
            align-items: center;
            font-weight: 500;
          }

          .trial-time-selector {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;
          }

          .trial-time-tips {
            font-size: 12px;
            color: #909399;
            display: flex;
            align-items: center;
            gap: 4px;
          }
        }
      }
    }

    .empty-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .sales-booking-container {
    .booking-layout {
      flex-direction: column;
      height: auto;
    }

    .filter-panel {
      width: 100%;

      .filter-card {
        height: auto;

        :deep(.el-card__body) {
          height: auto;
          max-height: 400px;
        }
      }
    }

    .result-panel {
      .search-results {
        .results-card {
          height: 600px;
        }
      }
    }
  }
}
</style>

<template>
  <el-dialog
    v-model="visible"
    title="申请详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="application-detail">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 申请信息标签页 -->
        <el-tab-pane label="申请信息" name="application">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="学科">
              <span>{{ applicationDetail.subject }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="课型">
              <span>{{ applicationDetail.courseType }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="申请状态">
              <el-tag :type="getStatusType(applicationDetail.status)">
                {{ getStatusText(applicationDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="申请时间">
              <span>{{ formatDateTime(applicationDetail.createTime) }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="处理时间">
              <span>{{ formatDateTime(applicationDetail.approvalTime || applicationDetail.confirmTime) || '-' }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="试听课时间" :span="2">
              <div v-if="applicationDetail.trialClassTime" class="trial-time-info">
                <div class="trial-date">{{ formatTrialDate(applicationDetail.trialClassTime.date) }}</div>
                <div class="trial-time">{{ formatTimeOnly(applicationDetail.trialClassTime.startTime) }} - {{ formatTimeOnly(applicationDetail.trialClassTime.endTime) }}</div>
              </div>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="申请说明" :span="2">
              <div class="application-reason">
                <el-input
                  v-model="applicationDetail.applyReason"
                  type="textarea"
                  :rows="3"
                  readonly
                  resize="none"
                  placeholder="无申请说明"
                  class="readonly-textarea"
                />
              </div>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 预约教师 -->
          <div class="section-title">预约教师</div>
          <div class="preferred-teachers-horizontal">
            <el-tag
              v-for="teacher in applicationDetail.preferredTeachers"
              :key="teacher.teacherId"
              :type="teacher.teacherId === applicationDetail.approvedTeacherId ? 'primary' : 'info'"
              size="default"
              class="teacher-tag-horizontal"
            >
              <!-- 如果是已通过审核且是被选择的老师，显示绿色勾 -->
              <span v-if="applicationDetail.status === '已通过' && teacher.teacherId === applicationDetail.approvedTeacherId" class="approved-icon">✅</span>
              {{ teacher.teacherName }}
              <span class="teacher-phone">({{ teacher.phone }})</span>
            </el-tag>
          </div>

          <!-- 首选时间 -->
          <div class="section-title">正课时间</div>
          <div class="preferred-times">
            <el-tag
              v-for="(timeSlot, index) in applicationDetail.preferredTimeSlots"
              :key="index"
              class="time-tag"
              type="info"
            >
              {{ getWeekdayText(timeSlot.weekday) }} {{ formatTimeOnly(timeSlot.startTime) }}-{{ formatTimeOnly(timeSlot.endTime) }}
            </el-tag>
          </div>
        </el-tab-pane>

        <!-- 学生信息标签页 -->
        <el-tab-pane label="学生信息" name="student">
          <div v-loading="studentLoading" class="student-info">
            <el-descriptions :column="2" border v-if="studentDetail">
              <el-descriptions-item label="学生姓名">
                <span>{{ studentDetail.name }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="手机号码">
                <span>{{ studentDetail.phone }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="性别">
                <span>{{ getGenderText(studentDetail.gender) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="年级">
                <span>{{ getGradeText(studentDetail.grade) }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="学校">
                <span>{{ studentDetail.school || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="班级">
                <span>{{ studentDetail.className || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="家长姓名">
                <span>{{ studentDetail.parentName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="家长手机">
                <span>{{ studentDetail.parentPhone || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="学习目标" :span="2">
                <div class="learning-goals">
                  <el-input
                    v-model="studentDetail.learningGoals"
                    type="textarea"
                    :rows="2"
                    readonly
                    resize="none"
                    placeholder="暂无学习目标"
                    class="readonly-textarea"
                  />
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">
                <div class="student-remarks">
                  <el-input
                    v-model="studentDetail.remarks"
                    type="textarea"
                    :rows="2"
                    readonly
                    resize="none"
                    placeholder="暂无备注"
                    class="readonly-textarea"
                  />
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <div v-else class="no-student-data">
              <el-empty description="暂无学生信息" />
            </div>
          </div>
        </el-tab-pane>
        <!-- 审核情况标签页 -->
        <el-tab-pane label="审核情况" name="review" v-if="applicationDetail.reviewStatusList && applicationDetail.reviewStatusList.length > 0">
          <div class="review-status-section">
            <el-table
              :data="applicationDetail.reviewStatusList"
              border
              size="small"
              class="review-status-table"
            >
              <el-table-column prop="teachingGroupName" label="教学组" width="120" />
              <el-table-column prop="reviewStatus" label="审核状态" width="120">
                <template #default="{ row }">
                  <el-tag
                    :type="getReviewStatusType(row.reviewStatus)"
                    size="small"
                  >
                    {{ row.reviewStatus }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="reviewerName" label="审核人" width="100">
                <template #default="{ row }">
                  <span>{{ row.reviewerName || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="reviewTime" label="审核时间" width="150">
                <template #default="{ row }">
                  <span>{{ formatDateTime(row.reviewTime) || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="reviewComment" label="审核备注" min-width="150">
                <template #default="{ row }">
                  <span>{{ row.reviewComment || '-' }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 处理结果标签页 -->
        <el-tab-pane label="处理结果" name="result" v-if="applicationDetail.status !== '待审核'">
          <!-- 作废信息 -->
          <div v-if="applicationDetail.status === '已作废'" class="void-info-section">
            <div class="section-title">作废信息</div>
            <el-descriptions :column="2" border size="small">
              <el-descriptions-item label="作废操作人员">
                <span>{{ applicationDetail.voidOperatorName || '--' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="作废时间">
                <span>{{ formatDateTime(applicationDetail.voidTime) || '--' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="作废原因" :span="2">
                <span>{{ applicationDetail.voidReason || '--' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 通过结果 -->
          <div v-if="applicationDetail.status === '已通过'" class="approval-result">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="确认教师">
                <span>{{ applicationDetail.assignedTeacherName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="确认时间">
                <div v-if="applicationDetail.confirmedTimeSlot" class="confirmed-time-info">
                  <div class="confirmed-date">{{ formatTrialDate(applicationDetail.confirmedTimeSlot.date) }} {{ formatTimeOnly(applicationDetail.confirmedTimeSlot.startTime) }}-{{ formatTimeOnly(applicationDetail.confirmedTimeSlot.endTime) }}</div>
                </div>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="处理人">
                <span>{{ applicationDetail.reviewerName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="处理时间">
                <span>{{ formatDateTime(applicationDetail.reviewTime) || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="备注" :span="2">
                <span>{{ applicationDetail.reviewComment || '-' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <!-- 拒绝结果 -->
          <div v-else-if="applicationDetail.status === '已拒绝'" class="rejection-result">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="拒绝原因" :span="2">
                <span style="color: #f56c6c">{{ applicationDetail.rejectionReason || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="处理人">
                <span>{{ applicationDetail.reviewerName || '-' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="处理时间">
                <span>{{ formatDateTime(applicationDetail.reviewTime) || '-' }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getCourseBookingReviewDetailApi } from '@/api/teaching-group-leader/courseReview'
import { getStudentDetailApi } from '@/api/management/student'
import { getGradeText } from '@/utils/gradeUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  applicationId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

const visible = ref(false)
const loading = ref(false)
const studentLoading = ref(false)
const applicationDetail = ref({})
const studentDetail = ref(null)
const activeTab = ref('application')

// 工作日选项
const weekdays = [
  { label: '周一', value: 1 },
  { label: '周二', value: 2 },
  { label: '周三', value: 3 },
  { label: '周四', value: 4 },
  { label: '周五', value: 5 },
  { label: '周六', value: 6 },
  { label: '周日', value: 7 }
]

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.applicationId) {
    getApplicationDetail()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取申请详情
const getApplicationDetail = async () => {
  if (!props.applicationId) return

  loading.value = true
  try {
    const response = await getCourseBookingReviewDetailApi(props.applicationId)
    applicationDetail.value = response.data || {}

    // 获取学生详情
    if (applicationDetail.value.studentId) {
      await getStudentDetail(applicationDetail.value.studentId)
    }
  } catch (error) {
    ElMessage.error('获取申请详情失败')
  } finally {
    loading.value = false
  }
}

// 获取学生详情
const getStudentDetail = async (studentId) => {
  if (!studentId) return

  studentLoading.value = true
  try {
    const response = await getStudentDetailApi(studentId)
    studentDetail.value = response.data || null
  } catch (error) {
    console.error('获取学生详情失败:', error)
    studentDetail.value = null
  } finally {
    studentLoading.value = false
  }
}

// 工具方法
const getStatusType = (status) => {
  const statusMap = {
    // 英文状态（兼容）
    pending: 'warning',
    approved: 'success',
    rejected: 'danger',
    cancelled: 'info',
    withdrawn: 'info',
    voided: 'warning',
    // 中文状态（新）
    '待审核': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger',
    '已取消': 'info',
    '已撤回': 'info',
    '已作废': 'warning'
  }
  return statusMap[status] || 'info'
}

const getReviewStatusType = (status) => {
  const statusMap = {
    '待审核': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    // 英文状态转中文（兼容）
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    cancelled: '已取消',
    withdrawn: '已撤回',
    // 中文状态直接返回
    '待审核': '待审核',
    '已通过': '已通过',
    '已拒绝': '已拒绝',
    '已取消': '已取消',
    '已撤回': '已撤回'
  }
  return statusMap[status] || status
}

const getWeekdayText = (weekday) => {
  const day = weekdays.find(d => d.value === weekday)
  return day ? day.label : `周${weekday}`
}



const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

const formatTrialDate = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]
  return `${date.getMonth() + 1}月${date.getDate()}日 ${weekday}`
}

// 格式化时间为 HH:mm 格式（去掉秒）
const formatTimeOnly = (timeStr) => {
  if (!timeStr) return '--'
  // 如果是 HH:mm:ss 格式，截取前5位
  if (timeStr.includes(':') && timeStr.length > 5) {
    return timeStr.substring(0, 5)
  }
  return timeStr
}

// 获取性别文本
const getGenderText = (gender) => {
  const genderMap = {
    '0': '男',
    '1': '女',
    '2': '未知'
  }
  return genderMap[gender] || '未知'
}

// 事件处理
const handleClose = () => {
  visible.value = false
  // 重置数据
  applicationDetail.value = {}
  studentDetail.value = null
  activeTab.value = 'application'
}
</script>

<style scoped>
.application-detail {
  max-height: 600px;
  overflow-y: auto;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 20px 0 10px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.preferred-teachers {
  margin-bottom: 20px;
}

.teacher-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.teacher-details {
  font-size: 12px;
  color: #909399;
}

.teacher-details span {
  margin-right: 12px;
}

.teacher-status {
  text-align: right;
}

.unavailable-reason {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 4px;
}

.preferred-times {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.time-tag {
  margin: 0;
}

.approval-result,
.rejection-result {
  margin-top: 10px;
}

.application-reason {
  width: 100%;
}

.readonly-textarea {
  width: 100%;
}

.readonly-textarea .el-textarea__inner {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  color: #606266;
  cursor: default;
}

.readonly-textarea .el-textarea__inner:focus {
  border-color: #e4e7ed;
  box-shadow: none;
}

.dialog-footer {
  text-align: right;
}

/* 试听课时间样式 */
.trial-time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trial-date {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.trial-time {
  color: #606266;
  font-size: 13px;
}

/* 确认时间样式 */
.confirmed-time-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.confirmed-date {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.confirmed-time {
  color: #606266;
  font-size: 13px;
}

/* 横向老师列表样式 */
.preferred-teachers-horizontal {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 20px;
}

.teacher-tag-horizontal {
  padding: 8px 12px;
  font-size: 13px;
  border-radius: 4px;
}

.teacher-phone {
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.8;
}

.approved-icon {
  margin-right: 4px;
}

/* 审核情况样式 */
.review-status-section {
  margin-bottom: 20px;
}

.review-status-table {
  width: 100%;
}

.review-status-table .el-table__cell {
  padding: 8px 0;
}

.review-status-table .el-tag {
  font-size: 12px;
}

/* 作废信息样式 */
.void-info-section {
  margin-bottom: 20px;
}

.void-info-section .el-descriptions {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
}

.void-info-section .el-descriptions__label {
  background-color: #fde2e2;
}
</style>

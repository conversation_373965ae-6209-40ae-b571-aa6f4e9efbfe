<template>
  <el-dialog
    v-model="visible"
    title="批量审核"
    width="800px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="batch-review-content">
      <!-- 选中的申请列表 -->
      <div class="selected-applications">
        <h4>选中的申请 ({{ selectedApplications.length }}个)</h4>
        <el-table :data="selectedApplications" max-height="200">
          <el-table-column prop="studentName" label="学生姓名" width="120" />
          <el-table-column prop="subject" label="学科" width="80" />
          <el-table-column prop="specification" label="课型" width="100" />
          <el-table-column prop="createTime" label="申请时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 批量审核表单 -->
      <div class="batch-review-form">
        <h4>批量审核设置</h4>
        <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
          <el-form-item label="审核结果" prop="reviewResult">
            <el-radio-group v-model="form.reviewResult" @change="handleReviewResultChange">
              <el-radio label="已通过">批量通过</el-radio>
              <el-radio label="已拒绝">批量拒绝</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 批量通过时的设置 -->
          <template v-if="form.reviewResult === '已通过'">
            <el-form-item label="分配方式">
              <el-radio-group v-model="assignmentMode">
                <el-radio label="auto">自动分配</el-radio>
                <el-radio label="manual">手动分配</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 手动分配 -->
            <template v-if="assignmentMode === 'manual'">
              <div class="manual-assignments">
                <h5>手动分配教师和时间</h5>
                <div 
                  v-for="(application, index) in selectedApplications" 
                  :key="application.id"
                  class="assignment-item"
                >
                  <div class="application-info">
                    <span class="student-name">{{ application.studentName }}</span>
                    <span class="subject">{{ application.subject }} - {{ application.specification }}</span>
                  </div>
                  <div class="assignment-controls">
                    <el-select
                      v-model="assignments[index].teacherId"
                      placeholder="选择教师"
                      style="width: 150px; margin-right: 10px"
                      @change="handleTeacherChange(index, $event)"
                    >
                      <el-option
                        v-for="teacher in availableTeachers"
                        :key="teacher.teacherId"
                        :label="teacher.teacherName"
                        :value="teacher.teacherId"
                      />
                    </el-select>
                    <el-select
                      v-model="assignments[index].trialTimeSlot"
                      placeholder="选择试听课时间"
                      style="width: 250px"
                      :disabled="!assignments[index].teacherId"
                    >
                      <el-option
                        v-for="timeSlot in getTeacherTrialTimeSlots(index)"
                        :key="`${timeSlot.date}-${timeSlot.startTime}`"
                        :label="getTrialTimeSlotText(timeSlot)"
                        :value="timeSlot"
                        :disabled="timeSlot.disabled"
                        :class="{ 'disabled-option': timeSlot.disabled }"
                      />
                    </el-select>
                  </div>
                </div>
              </div>
            </template>
          </template>

          <!-- 批量拒绝时的设置 -->
          <template v-if="form.reviewResult === '已拒绝'">
            <el-form-item label="拒绝原因" prop="rejectionReason">
              <el-select
                v-model="form.rejectionReason"
                placeholder="请选择拒绝原因"
                style="width: 100%"
                allow-create
                filterable
              >
                <el-option label="教师时间冲突" value="教师时间冲突" />
                <el-option label="课程安排已满" value="课程安排已满" />
                <el-option label="学生信息不完整" value="学生信息不完整" />
                <el-option label="课时余额不足" value="课时余额不足" />
                <el-option label="不符合课程要求" value="不符合课程要求" />
                <el-option label="其他原因" value="其他原因" />
              </el-select>
            </el-form-item>
          </template>

          <el-form-item label="审核意见" prop="reviewComment">
            <el-input
              v-model="form.reviewComment"
              type="textarea"
              :rows="3"
              :placeholder="form.reviewResult === '已通过' ? '请输入批量通过的意见' : '请详细说明批量拒绝的原因'"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          :type="form.reviewResult === '已通过' ? 'primary' : 'danger'"
          @click="handleConfirm"
          :loading="submitting"
        >
          {{ form.reviewResult === '已通过' ? '确认批量通过' : '确认批量拒绝' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  batchReviewApplicationsApi,
  getAppliedTeachersApi,
  getTeacherAvailableSlotsApi
} from '@/api/teaching-group-leader/courseReview'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  selectedApplications: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const visible = ref(false)
const loading = ref(false)
const submitting = ref(false)
const formRef = ref()
const availableTeachers = ref([])
const teacherTimeSlots = ref({}) // 存储每个教师的可用时间段
const assignmentMode = ref('auto')

const form = reactive({
  reviewResult: '已通过',
  reviewComment: '',
  rejectionReason: ''
})

const assignments = ref([])

const rules = computed(() => {
  const baseRules = {
    reviewResult: [
      { required: true, message: '请选择审核结果', trigger: 'change' }
    ],
    reviewComment: [
      { required: true, message: '请输入审核意见', trigger: 'blur' }
    ]
  }

  if (form.reviewResult === '已拒绝') {
    baseRules.rejectionReason = [
      { required: true, message: '请选择拒绝原因', trigger: 'change' }
    ]
  }

  return baseRules
})

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    initializeAssignments()
    loadAvailableTeachers()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  form.reviewResult = '已通过'
  form.reviewComment = ''
  form.rejectionReason = ''
  assignmentMode.value = 'auto'
  assignments.value = []
  teacherTimeSlots.value = {}
  
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 初始化分配数组
const initializeAssignments = () => {
  assignments.value = props.selectedApplications.map(app => ({
    applicationId: app.id,
    teacherId: '',
    trialTimeSlot: null
  }))
}

// 加载可用教师（批量审核：获取所有选中申请的候选老师并集）
const loadAvailableTeachers = async () => {
  try {
    if (!props.selectedApplications || props.selectedApplications.length === 0) {
      availableTeachers.value = []
      return
    }

    // 获取所有选中申请的候选老师并集
    const allTeachers = new Map() // 使用Map去重

    for (const application of props.selectedApplications) {
      try {
        const response = await getAppliedTeachersApi(application.id)
        const teachers = response.data || []

        teachers.forEach(teacher => {
          allTeachers.set(teacher.teacherId, teacher)
        })
      } catch (error) {
        console.error(`获取申请 ${application.id} 的候选老师失败:`, error)
      }
    }

    availableTeachers.value = Array.from(allTeachers.values())
  } catch (error) {
    console.error('加载可用教师失败:', error)
    availableTeachers.value = []
  }
}

// 处理审核结果变化
const handleReviewResultChange = (value) => {
  if (value === '已拒绝') {
    assignmentMode.value = 'auto'
  }
}

// 处理教师选择变化
const handleTeacherChange = async (index, teacherId) => {
  if (!teacherId) {
    assignments.value[index].trialTimeSlot = null
    return
  }

  // 重新加载该申请的试听课时间段，并检查老师可用性
  const application = props.selectedApplications[index]
  if (application && application.trialClassTime) {
    await loadTrialTimeSlotsForApplication(index, teacherId, application.trialClassTime)
  }
}

// 为指定申请加载试听课时间段并检查老师可用性
const loadTrialTimeSlotsForApplication = async (index, teacherId, trialClassTime) => {
  const timeSlots = generateTrialTimeSlots(trialClassTime)

  // 获取对应的申请ID
  const applicationId = assignments.value[index].applicationId

  if (teacherId) {
    try {
      // 调用API获取该申请和老师的可选时间段
      const response = await getTeacherAvailableSlotsApi(teacherId, applicationId)

      const availableTimeSlots = response.data || []

      console.log('批量审核 - 后端返回的可选时间段:', availableTimeSlots)

      // 直接使用后端返回的时间段数据，因为后端已经计算好了可用性
      const processedTimeSlots = availableTimeSlots.map(slot => ({
        ...slot,
        disabled: !slot.available // 根据后端返回的available字段设置disabled
      }))

      // 存储到teacherTimeSlots中，使用applicationId作为key
      teacherTimeSlots.value[`${applicationId}-${teacherId}`] = processedTimeSlots

      return // 直接返回，不需要继续处理
    } catch (error) {
      console.error('检查老师可用性失败:', error)
      // 如果检查失败，使用前端生成的时间段，但都设为可选
      timeSlots.forEach(slot => {
        slot.disabled = false
      })

      // 存储到teacherTimeSlots中
      teacherTimeSlots.value[`${applicationId}-${teacherId}`] = timeSlots
    }
  } else {
    // 如果没有teacherId，存储空的时间段
    teacherTimeSlots.value[`${applicationId}-${teacherId}`] = []
  }
}

// 生成试听课时间段选项
const generateTrialTimeSlots = (trialTime) => {
  const timeSlots = []
  const startTime = new Date(`2000-01-01 ${trialTime.startTime}`)
  const endTime = new Date(`2000-01-01 ${trialTime.endTime}`)

  // 每15分钟生成一个时间段，每个时间段持续1小时
  const current = new Date(startTime)
  while (current.getTime() + 60 * 60 * 1000 <= endTime.getTime()) {
    const slotStart = current.toTimeString().slice(0, 5)
    const slotEnd = new Date(current.getTime() + 60 * 60 * 1000).toTimeString().slice(0, 5)

    timeSlots.push({
      date: trialTime.date,
      startTime: slotStart,
      endTime: slotEnd,
      disabled: false // 初始状态不禁用，后续根据老师可用性设置
    })

    // 增加15分钟
    current.setTime(current.getTime() + 15 * 60 * 1000)
  }

  return timeSlots
}

// 获取指定分配项的试听课时间段
const getTeacherTrialTimeSlots = (index) => {
  const teacherId = assignments.value[index]?.teacherId
  const applicationId = assignments.value[index]?.applicationId
  return teacherId ? (teacherTimeSlots.value[`${applicationId}-${teacherId}`] || []) : []
}

// 获取试听课时间段文本
const getTrialTimeSlotText = (timeSlot) => {
  const date = new Date(timeSlot.date)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.getDay()]
  const dateStr = `${date.getMonth() + 1}月${date.getDate()}日`

  return `${dateStr} ${weekday} ${timeSlot.startTime}-${timeSlot.endTime}`
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN')
}

// 确认批量审核
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    // 如果是手动分配，检查是否所有申请都已分配
    if (form.reviewResult === '已通过' && assignmentMode.value === 'manual') {
      const unassigned = assignments.value.filter(a => !a.teacherId || !a.trialTimeSlot)
      if (unassigned.length > 0) {
        ElMessage.warning('请为所有申请分配教师和试听课时间段')
        return
      }
    }
    
    submitting.value = true
    
    const data = {
      applicationIds: props.selectedApplications.map(app => app.id),
      reviewResult: form.reviewResult,
      reviewComment: form.reviewComment
    }

    if (form.reviewResult === '已通过' && assignmentMode.value === 'manual') {
      data.assignments = assignments.value
    } else if (form.reviewResult === '已拒绝') {
      data.rejectionReason = form.rejectionReason
    }
    
    const response = await batchReviewApplicationsApi(data)
    
    if (response.data.failedCount > 0) {
      ElMessage.warning(`批量审核完成，成功 ${response.data.successCount} 个，失败 ${response.data.failedCount} 个`)
    } else {
      ElMessage.success('批量审核成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('批量审核失败: ' + (error.message || '未知错误'))
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.batch-review-content {
  max-height: 600px;
  overflow-y: auto;
}

.selected-applications {
  margin-bottom: 20px;
}

.selected-applications h4,
.batch-review-form h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.manual-assignments {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 16px;
  margin-top: 12px;
}

.manual-assignments h5 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.assignment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
}

.application-info {
  flex: 1;
}

.student-name {
  font-weight: 500;
  color: #303133;
  margin-right: 12px;
}

.subject {
  font-size: 12px;
  color: #909399;
}

.assignment-controls {
  display: flex;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

/* 禁用的时间段选项样式 */
:deep(.disabled-option) {
  color: #C0C4CC !important;
  background-color: #F5F7FA !important;
  cursor: not-allowed !important;
}

:deep(.disabled-option:hover) {
  background-color: #F5F7FA !important;
}
</style>

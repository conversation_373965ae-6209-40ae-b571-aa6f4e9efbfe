<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="教材名称" prop="textbookName">
        <el-input
          v-model="queryParams.textbookName"
          placeholder="请输入教材名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="测试时间" prop="testDateRange">
        <el-date-picker
          v-model="queryParams.testDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          size="small"
          @click="handleExport"
          v-hasPermi="['word-test:query:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="testList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="教材名称" align="center" prop="textbookName" />
      <el-table-column label="测试模式" align="center" prop="testMode">
        <template #default="scope">
          <el-tag v-if="scope.row.testMode === 'random'" type="primary">随机测试</el-tag>
          <el-tag v-else-if="scope.row.testMode === 'range'" type="success">范围测试</el-tag>
          <el-tag v-else>{{ scope.row.testMode }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="测试单词数" align="center" prop="testedWordNum" />
      <el-table-column label="答对单词数" align="center" prop="successWordNum" />
      <el-table-column label="正确率" align="center" prop="successRate">
        <template #default="scope">
          <el-tag 
            :type="getSuccessRateType(scope.row.successRate)"
            effect="dark"
          >
            {{ scope.row.successRate }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="耗时" align="center" prop="consumTime" />
      <el-table-column label="测试时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="错误单词数" align="center" prop="wrongWordCount">
        <template #default="scope">
          <el-tag v-if="scope.row.wrongWordCount > 0" type="danger">
            {{ scope.row.wrongWordCount }}
          </el-tag>
          <el-tag v-else type="success">0</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="300">
        <template #default="scope">
          <el-button
            type="primary"
            icon="View"
            @click="handleView(scope.row)"
            v-hasPermi="['word-test:query:view']"
          >查看详情</el-button>
          
          <!-- 错误内容下载下拉菜单 -->
          <el-dropdown
            v-if="scope.row.wrongWordCount > 0"
            trigger="click"
            @command="(command) => handleDownload(scope.row, command)"
            :loading="getLoading(scope.row.courseId, 'download')"
          >
            <el-button
              type="success"
              size="small"
              :loading="getLoading(scope.row.courseId, 'download')"
            >
              错误内容
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="error_handout">
                  <el-icon><Document /></el-icon>
                  错词讲义
                </el-dropdown-item>
                <el-dropdown-item command="error_exercise">
                  <el-icon><EditPen /></el-icon>
                  错题练习
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Document, EditPen } from '@element-plus/icons-vue'
import { getWordTestListApi, downloadWordTestErrorMaterialApi } from '@/api/course'
import { parseTime } from '@/utils/ruoyi'

const { proxy } = getCurrentInstance() as any

// 响应式数据
const testList = ref([])
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)

// 下载加载状态管理
const downloadLoadingMap = ref(new Map())

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  studentName: undefined,
  textbookName: undefined,
  testDateRange: undefined
})

/** 查询单词测验列表 */
function getList() {
  loading.value = true
  
  // 构建查询参数
  const params = {
    ...queryParams,
    startTime: queryParams.testDateRange?.[0],
    endTime: queryParams.testDateRange?.[1]
  }
  delete params.testDateRange

  // 这里需要调用一个新的API来获取所有学生的测验记录
  // 暂时使用现有API的逻辑，后续需要后端提供新的接口
  getWordTestListApi('all').then(response => {
    if (response.code === 200) {
      testList.value = response.data || []
      total.value = testList.value.length
    } else {
      ElMessage.error(response.msg || '获取测验列表失败')
    }
  }).catch(error => {
    console.error('获取测验列表失败:', error)
    ElMessage.error('获取测验列表失败')
  }).finally(() => {
    loading.value = false
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef')
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection: any[]) {
  ids.value = selection.map(item => item.courseId)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

/** 查看详情 */
function handleView(row: any) {
  // 跳转到测验结果详情页面
  proxy.$router.push({
    path: '/word-test-detail',
    query: { courseId: row.courseId }
  })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('word-test/export', {
    ...queryParams
  }, `word_test_${new Date().getTime()}.xlsx`)
}

/** 获取正确率标签类型 */
function getSuccessRateType(successRate: string) {
  const rate = parseFloat(successRate.replace('%', ''))
  if (rate >= 90) return 'success'
  if (rate >= 70) return 'warning'
  return 'danger'
}

/** 获取下载加载状态 */
function getLoading(courseId: string, type: string) {
  return downloadLoadingMap.value.get(`${courseId}_${type}`) || false
}

/** 设置下载加载状态 */
function setLoading(courseId: string, type: string, loading: boolean) {
  downloadLoadingMap.value.set(`${courseId}_${type}`, loading)
}

/** 处理错误内容下载 */
async function handleDownload(row: any, materialType: string) {
  const courseId = row.courseId
  setLoading(courseId, 'download', true)
  
  try {
    const response = await downloadWordTestErrorMaterialApi(courseId, materialType)
    if (response.code === 200) {
      // 打开下载链接
      window.open(response.data.downloadUrl, '_blank')
      const materialName = materialType === 'error_handout' ? '错词讲义' : '错题练习'
      ElMessage.success(`${materialName}下载成功`)
    } else {
      ElMessage.error(response.msg || '下载失败')
    }
  } catch (error) {
    console.error('下载错误内容失败:', error)
    ElMessage.error('下载失败')
  } finally {
    setLoading(courseId, 'download', false)
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>

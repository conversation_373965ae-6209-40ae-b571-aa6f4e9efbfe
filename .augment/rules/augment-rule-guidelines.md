---
type: "always_apply"
---

## 准则

1.  所有计划的任务、文档、思考过程、记忆存储、日志、输出，都保存到augment文件夹下合适的位置
2. 所有生成的非代码文件，文件名按以`年月日时分_`开头，方便我后续查找；
3. 为每一次任务创建独立的文件夹，存储相关信息；
4. 每次开始涉及代码相关的任务时，务必先调用**Augment Context Engine**获取现有实现。
5. 在 augment文件夹下合适的子文件夹，生成过程文件和报告，以日期_任务ID_xxx.md文件格式

---

## 技术及体验要求

### words-frontend是前端代码

1. vue3+typescript+elementui；
2. PC端用户基本是笔记本电脑，高度可视空间比较少，设计布局都时候需要考虑。

### words-service是Java后端代码
1. orm框架使用了mybatis-plus、mybatis-plus-join，尽可能用service，不得已才使用mapper，能不再xml中编写sql就不写，需要表关联查询可以使用MPJLambdaWrapper关联查询。
2. 尽可能使用xxxService.lambdaQuery()或者xxxService.lambdaUpdate()或者MPJLambdaWrapper关联查询，除非sql过于复杂才允许在mapper.xml中编写sql。
3. sys开头的表和service，不得已的情况不实用。要查询老师信息可以通过teacher_profile表，学生信息可以通过user_student_ext表。
4. sysUser的service服务统一试一试IUserService接口及对应的实现类
5. 禁止使用xxxMapper，禁止在xml中写sql，除非统计类需求。
6. 禁止生成Java测试类和前端测试页面。

---

## 编码规范要求（必须遵循）



1. 禁止在xxxMapper.xml中编写sql；
2. 禁止在xxxMapper类中实现常规查询方法；
3. xxxService定位为数据层服务，不在这边做业务耦合；
4. xxxFacade实现业务聚合；
5. 禁止使用`javax.validation`包，应该用`jakarta.validation`取代


---

## 最高准则
- 你的每次回答，都必须调用 *Augment Context Engine* 工具，详细思考后再使用中文回复 ** 。
- 大模型你仅被授权使用 ** claude sonnet 4 ** ，禁止使用其他模型；
- `augment` 文件夹是你的存储库，用于存储你的执行任务的记录、日志、文档、和记忆
- `augment/requirements/` 存储用户需要需要实现的需求，一批需求会写在一个文件中，你需要一次性完成一个需求文件中的需求
- `augment/memories/` 存储你关于本项目的所有记忆，每次执行任务需要及时更新记忆
- `augment/tasks/` 存储你每次执行任务的记录，每次执行任务需要创建一个以任务ID命名的文件夹，用于存储任务相关的所有文件
- `augment/logs/` 存储你每次执行任务的日志，每次执行任务需要创建一个以任务ID命名的文件夹，用于存储任务相关的所有日志
- `augment/docs/` 存储你每次执行任务的文档，每次执行任务需要创建一个以任务ID命名的文件夹，用于存储任务相关的所有文档
- 除了代码和脚本外，所有生成的文件都用中文命名

---

{"name": "words-wechat", "type": "commonjs", "version": "2.11.0", "description": "xxxxxxxx", "update-time": "2025-05-28", "author": {"name": "words"}, "license": "MIT", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"preinstall": "npx only-allow pnpm", "uvm": "npx @dcloudio/uvm@latest", "uvm-rm": "node ./scripts/postupgrade.js", "postuvm": "echo upgrade uni-app success!", "dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp": "uni -p mp-weixin", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:mp-xhs": "uni -p mp-xhs", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp": "uni build -p mp-weixin", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:mp-xhs": "uni build -p mp-xhs", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "openapi-ts-request": "openapi-ts", "prepare": "git init && husky"}, "lint-staged": {"**/*.{vue,html,cjs,json,md,scss,css,txt}": ["prettier --write --cache"], "**/*.{js,ts}": ["oxlint --fix", "prettier --write --cache"], "!**/{node_modules,dist}/**": []}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060620250520001", "@dcloudio/uni-app-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-app-plus": "3.0.0-4060620250520001", "@dcloudio/uni-components": "3.0.0-4060620250520001", "@dcloudio/uni-h5": "3.0.0-4060620250520001", "@dcloudio/uni-mp-alipay": "3.0.0-4060620250520001", "@dcloudio/uni-mp-baidu": "3.0.0-4060620250520001", "@dcloudio/uni-mp-harmony": "3.0.0-4060620250520001", "@dcloudio/uni-mp-jd": "3.0.0-4060620250520001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060620250520001", "@dcloudio/uni-mp-lark": "3.0.0-4060620250520001", "@dcloudio/uni-mp-qq": "3.0.0-4060620250520001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060620250520001", "@dcloudio/uni-mp-weixin": "3.0.0-4060620250520001", "@dcloudio/uni-mp-xhs": "3.0.0-4060620250520001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060620250520001", "@tanstack/vue-query": "^5.62.16", "abortcontroller-polyfill": "^1.7.8", "dayjs": "1.11.10", "js-cookie": "^3.0.5", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "vue": "^3.5.15", "wot-design-uni": "^1.9.1", "z-paging": "^2.8.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4060620250520001", "@dcloudio/uni-cli-shared": "3.0.0-4060620250520001", "@dcloudio/uni-stacktracey": "3.0.0-4060620250520001", "@dcloudio/vite-plugin-uni": "3.0.0-4060620250520001", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@iconify-json/carbon": "^1.2.4", "@rollup/rollup-darwin-x64": "^4.28.0", "@types/node": "^20.17.9", "@types/wechat-miniprogram": "^3.4.8", "@uni-helper/uni-types": "1.0.0-alpha.3", "@uni-helper/unocss-preset-uni": "^0.2.11", "@uni-helper/vite-plugin-uni-components": "^0.2.0", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.8", "@uni-helper/vite-plugin-uni-pages": "0.2.20", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@uni-ku/bundle-optimizer": "^1.3.3", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "husky": "^9.1.7", "lint-staged": "^15.2.10", "openapi-ts-request": "^1.1.2", "oxlint": "^0.1.0", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.77.8", "terser": "^5.36.0", "typescript": "^5.7.2", "unocss": "65.4.2", "unplugin-auto-import": "^0.17.8", "vite": "6.3.5", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^2.2.10"}}
<route lang="json5" type="page">
{
  style: {
    navigationStyle: 'default',
    navigationBarTitleText: '分包页面 标题',
  },
}
</route>

<template>
  <view class="text-center">
    <view class="m-8">http://localhost:9000/#/pages-sub/demo/index</view>
    <view class="text-green-500">分包页面demo</view>
  </view>
</template>

<script lang="ts" setup>
// code here
</script>

<style lang="scss" scoped>
//
</style>

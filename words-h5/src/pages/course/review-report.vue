<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '课程报告',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="report-container">
    <!-- 背景装饰元素 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>

    <view class="report-content">
      <!-- 标题区域 -->
      <view class="report-header">
        <view class="congratulation-icon">
          <wd-icon name="success" size="80px" color="#07c160"></wd-icon>
        </view>
        <view class="report-title">本次陪练报告</view>
        <view class="congratulation-text">
          {{ reportDetailDto.userName }}，恭喜你顺利完成本次复习！
        </view>
      </view>

      <!-- 抗遗忘复习列表 -->
      <view class="training-card" v-if="reviewListDisplay">
        <view
          class="training-item"
          v-for="item in reportDetailDto.reportData.reviewWordDataList"
          :key="item.title"
        >
          <view class="training-icon">
            <wd-icon name="discount" size="24px" color="#e2a03b"></wd-icon>
          </view>
          <view class="training-content">
            <view class="training-label">{{ item.title }}</view>
            <view class="training-value">复习{{ item.count }}词，正确率{{ item.correctRate }}</view>
          </view>
        </view>
      </view>

      <!-- 错误单词列表 -->
      <view class="error-words-card" v-if="errorWordsDisplay">
        <view class="card-header">
          <view class="card-title">本次课程错误单词</view>
          <view class="error-count">共{{ (reportDetailDto.reportData.errorWordList || []).length }}个</view>
        </view>
        <view class="error-words-list">
          <view
            class="error-word-item"
            v-for="(word, index) in (reportDetailDto.reportData.errorWordList || [])"
            :key="index"
          >
            {{ word }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useToast } from '@/uni_modules/wot-design-uni'
import { getReportInfo } from '@/api/course/report'
import { ReportDetailDto } from '@/api/course/report.typings'

const toast = useToast()
const reportDetailDto = ref<ReportDetailDto>()

const reviewListDisplay = computed(() => {
  return reportDetailDto.value?.reportData?.reviewWordDataList?.length > 0
})
const errorWordsDisplay = computed(() => {
  try {
    return reportDetailDto.value?.reportData?.errorWordList?.length > 0
  } catch (error) {
    console.warn('计算错误单词显示状态时出错:', error)
    return false
  }
})

const formatMinutes = (minutes) => {
  if (minutes < 60) {
    return `${minutes} 分钟`
  } else {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return `${hours} 小时 ${remainingMinutes} 分钟`
  }
}

// 页面加载时获取数据
onMounted((option) => {
  // 从 URL 中获取 code
  let id: string | null = null

  // 1. 尝试从 onLoad(option) 获取 (这是 uni-app 推荐的方式)
  if (option && option.id) {
    id = option.id as string
  } else {
    // 2. 如果 onLoad 没拿到，尝试从 window.location.search (hash 前的参数)
    const searchParams = new URLSearchParams(window.location.search)
    if (searchParams.has('id')) {
      id = searchParams.get('id')
    }
    // 3. 如果还未拿到，尝试从 window.location.hash (hash 后面的参数，如果你的微信回调格式是这样)
    // 这种情况下，你需要解析 hash 里面的路径和参数
    // 例如： #/pages/bind/index?code=xxx&state=xxx
    if (!id && window.location.hash.includes('id=')) {
      const hashParts = window.location.hash.split('?')
      if (hashParts.length > 1) {
        const hashQueryParams = new URLSearchParams(hashParts[1])
        if (hashQueryParams.has('id')) {
          id = hashQueryParams.get('id')
        }
      }
    }
  }
  if (!id) {
    toast.error('参数错误')
    return
  }
  getReportInfo(id)
    .then((res) => {
      reportDetailDto.value = res.data
      // 确保errorWordList字段存在
      if (reportDetailDto.value?.reportData && !reportDetailDto.value.reportData.errorWordList) {
        reportDetailDto.value.reportData.errorWordList = []
      }
    })
    .catch((err) => {
      toast.error(err.msg || '获取报告信息失败')
    })
    .finally(() => {
      // 隐藏加载动画
      try {
        uni.hideLoading()
      } catch (e) {
        console.warn('隐藏加载动画失败:', e)
      }
    })
})
</script>

<style lang="scss" scoped>
.report-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 40rpx;
  background-color: #ffffff;
  background-image: linear-gradient(
    135deg,
    rgba(25, 137, 250, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}

.bg-circle-1 {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.05), rgba(7, 193, 96, 0.1));
}

.report-content {
  flex: 1;
  padding: 60rpx 0;
  position: relative;
  z-index: 1;
}

.report-header {
  text-align: center;
  margin-bottom: 60rpx;
  animation: fadeInDown 0.8s ease-out;

  .congratulation-icon {
    margin-bottom: 20rpx;
    animation: scaleIn 0.5s ease-out;
  }

  .report-title {
    font-size: 44rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 16rpx;
    letter-spacing: 2rpx;
  }

  .congratulation-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.achievement-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.8s ease-out 0.2s both;

  .card-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 30rpx;
    text-align: center;
  }

  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
    margin: 30rpx 0 20rpx 0;
    padding-left: 10rpx;
    border-left: 4rpx solid #1989fa;

    &:first-of-type {
      margin-top: 0;
    }
  }

  .achievement-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30rpx;

    .achievement-item {
      text-align: center;
      padding: 20rpx;
      background: rgba(25, 137, 250, 0.05);
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(25, 137, 250, 0.15);
      }

      .achievement-number {
        font-size: 36rpx;
        font-weight: bold;
        color: #1989fa;
        margin-bottom: 8rpx;
      }

      .achievement-label {
        font-size: 24rpx;
        color: #666666;
      }
    }
  }
}

.training-card,
.error-words-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.8s ease-out 0.4s both;

  .training-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }

    .training-icon {
      width: 60rpx;
      height: 60rpx;
      background: rgba(25, 137, 250, 0.1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
    }

    .training-content {
      flex: 1;

      .training-label {
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 8rpx;
      }

      .training-value {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }
    }
  }
}

.error-words-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #f5f5f5;

    .card-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }

    .error-count {
      font-size: 24rpx;
      color: #ff4757;
      background: rgba(255, 71, 87, 0.1);
      padding: 8rpx 16rpx;
      border-radius: 12rpx;
    }
  }

  .error-words-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    min-height: 60rpx; /* 确保即使没有内容也有最小高度 */

    .error-word-item {
      background: rgba(255, 71, 87, 0.1);
      color: #ff4757;
      padding: 12rpx 20rpx;
      border-radius: 20rpx;
      font-size: 28rpx;
      font-weight: 500;
      border: 1px solid rgba(255, 71, 87, 0.2);
      transition: all 0.3s ease;
      word-break: break-word; /* 防止长单词破坏布局 */
      max-width: 100%; /* 防止单词过长 */

      &:hover {
        background: rgba(255, 71, 87, 0.15);
        transform: translateY(-2rpx);
      }
    }
  }
}

.progress-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  animation: fadeInUp 0.8s ease-out 0.6s both;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .progress-title {
      font-size: 28rpx;
      color: #666666;
    }

    .progress-percentage {
      font-size: 36rpx;
      font-weight: bold;
      color: #07c160;
    }
  }

  .progress-bar {
    height: 12rpx;
    background-color: #f5f5f5;
    border-radius: 6rpx;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #07c160, #39d87a);
      border-radius: 6rpx;
      transition: width 1s ease-out;
    }
  }
}

/* 动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>

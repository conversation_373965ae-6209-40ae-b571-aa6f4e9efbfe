<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '绑定成功',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="success-container">
    <!-- 背景装饰元素 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>

    <view class="success-content">
      <view class="success-icon">
        <wd-icon name="check" size="40px" color="#1989fa"></wd-icon>
      </view>
      <view class="success-title">账号绑定成功</view>
      <view class="success-desc">您的账号已成功绑定，现在可以开始使用了</view>
      
      <view class="success-buttons">
        <wd-button type="primary" size="large" block @click="goToBindList" class="list-btn">
          <wd-icon name="view" size="18px"></wd-icon>
          查看绑定列表
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 跳转到绑定列表页面
const goToBindList = () => {
  uni.navigateTo({ url: '/pages/bind/list' })
}
</script>

<style lang="scss" scoped>
.success-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 70rpx;
  background-color: #ffffff;
  background-image: linear-gradient(
    135deg,
    rgba(25, 137, 250, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}

.bg-circle-1 {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
}

.success-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.8s ease-out;

  .success-icon {
    margin-bottom: 40rpx;
    animation: scaleIn 0.5s ease-out;
  }

  .success-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
    letter-spacing: 2rpx;
  }

  .success-desc {
    font-size: 28rpx;
    color: #888888;
    margin-bottom: 80rpx;
    text-align: center;
  }

  .success-buttons {
    width: 100%;
    margin-top: 40rpx;

    .list-btn {
      height: 96rpx;
      font-size: 32rpx;
      font-weight: 500;
      letter-spacing: 2rpx;
      border-radius: 48rpx;
      box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.25);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        transform: scale(0.98);
        box-shadow: 0 5rpx 10rpx rgba(25, 137, 250, 0.2);
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>

<route lang="json5" type="home">
{
  style: {
    navigationBarTitleText: '绑定账号列表',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bind-list-container">
    <!-- 背景装饰元素 -->
    <view class="bg-decoration bg-circle-1"></view>
    <view class="bg-decoration bg-circle-2"></view>
    <view class="bg-decoration bg-circle-3"></view>

    <view class="bind-list-header">
      <view class="header-title">账号绑定列表</view>
      <view class="header-desc">管理您绑定的账号</view>
    </view>

    <view class="bind-list-content">
      <!-- 加载状态 -->
      <view class="bind-loading" v-if="isLoading">
        <wd-loading color="#1989fa" size="36px"></wd-loading>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 绑定账号列表 -->
      <scroll-view class="bind-list-scroll" scroll-y v-else-if="bindList.length > 0">
        <view class="bind-list" @click.stop="closeOutside">
          <wd-swipe-action v-for="(item, index) in bindList" :key="index">
            <view class="bind-item">
              <view class="bind-item-left">
                <view class="bind-item-avatar">
                  <wd-icon name="user" size="24px" color="#1989fa"></wd-icon>
                </view>
              </view>
              <view class="bind-item-content">
                <view class="bind-item-name">{{ item.userName }}</view>
                <view class="bind-item-phone">{{ item.phonenumber }}</view>
                <view class="bind-item-time">绑定时间：{{ item.createTime }}</view>
              </view>
              <view class="bind-item-right">
                <wd-icon name="move" size="16px" color="#999"></wd-icon>
              </view>
            </view>
            <template #right>
              <view class="action">
                <view class="button" style="background: #dd524d" @click.stop="confirmUnbind(item)">
                  解绑
                </view>
              </view>
            </template>
          </wd-swipe-action>
        </view>
      </scroll-view>

      <!-- 空状态 -->
      <view class="bind-empty" v-else>
        <view class="empty-icon">
          <wd-icon name="warn" size="48px" color="#cccccc"></wd-icon>
        </view>
        <view class="empty-text">暂无绑定账号</view>
      </view>
    </view>

    <!-- 添加绑定按钮 - 根据数据状态调整位置 -->
    <view :class="bindList.length > 0 ? 'add-bind-btn-fixed' : 'add-bind-btn-center'">
      <wd-button type="primary" size="large" block @click="goToBindPage">
        <wd-icon name="add" size="18px"></wd-icon>
        添加绑定账号
      </wd-button>
    </view>

    <!-- 确认解绑弹窗 -->
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import { useToast, useQueue, useMessage } from '@/uni_modules/wot-design-uni'
import { getWeixinCodeApi, getOpenidApi, listApi, unbindApi } from '@/api/bind/bind'
import { BindList } from '@/api/bind/bind.typings'
import { useUserStore, WeixinInfo } from '@/store'
const { closeOutside } = useQueue()
const mesage = useMessage()
const toast = useToast()
// 绑定列表数据
const bindList = ref<BindList[]>([])
// 加载状态
const isLoading = ref(true)
// 下拉刷新状态
const isRefreshing = ref(false)

// 获取绑定列表
const getBindList = async () => {
  isLoading.value = true

  try {
    let res = await listApi()
    if (res && Array.isArray(res.data)) {
      bindList.value = [...res.data]
    }
  } catch (error) {
    toast.error('获取绑定列表失败，请重试')
  } finally {
    isLoading.value = false
    if (isRefreshing.value) {
      isRefreshing.value = false
    }
  }
}

// 确认解绑
const confirmUnbind = async (item: any) => {
  if (!item) return
  mesage
    .confirm({
      msg: '您确定要进行解绑操作吗？',
      title: '解绑账号',
    })
    .then(async () => {
      try {
        // 调用解绑API
        // 注意：实际环境中需要获取code和密码
        const res = await unbindApi(item.id)

        if (res.code === 200) {
          // 解绑成功，从列表中移除
          // bindList.value = bindList.value.filter((bind) => bind.id !== item.id)
          toast.success('解绑成功')
          nextTick(() => getBindList())
        } else {
          toast.error(res.msg || '解绑失败，请重试')
        }
      } catch (error) {
        toast.error('解绑失败，请重试')
      }
    })
}

// 跳转到绑定页面
const goToBindPage = () => {
  uni.navigateTo({ url: '/pages/bind/add' })
}

// 页面加载完毕时触发
onLoad(async (option) => {
  // 从 store 或本地存储中获取 openid
  const userStore = useUserStore()
  const openid = userStore.weixinInfo?.openid
  // 如果有openid，直接获取绑定列表
  if (openid) {
    nextTick(() => getBindList())
    return
  }

  // 如果没有 openid，则获取微信 code
  // 从 URL 中获取 code
  let code: string | null = null

  // 1. 尝试从 onLoad(option) 获取 (这是 uni-app 推荐的方式)
  if (option && option.code) {
    code = option.code as string
  } else {
    // 2. 如果 onLoad 没拿到，尝试从 window.location.search (hash 前的参数)
    const searchParams = new URLSearchParams(window.location.search)
    if (searchParams.has('code')) {
      code = searchParams.get('code')
    }
    // 3. 如果还未拿到，尝试从 window.location.hash (hash 后面的参数，如果你的微信回调格式是这样)
    // 这种情况下，你需要解析 hash 里面的路径和参数
    // 例如： #/pages/bind/index?code=xxx&state=xxx
    if (!code && window.location.hash.includes('code=')) {
      const hashParts = window.location.hash.split('?')
      if (hashParts.length > 1) {
        const hashQueryParams = new URLSearchParams(hashParts[1])
        if (hashQueryParams.has('code')) {
          code = hashQueryParams.get('code')
        }
      }
    }
  }

  if (code == null || code === '') {
    try {
      const res = await getWeixinCodeApi() // 使用 await 等待 Promise 完成
      window.location.replace(res.data as string)
      return
    } catch (error: any) {
      toast.error(error.msg || '获取授权链接失败')
      return
    }
  }

  try {
    let res = await getOpenidApi(code as string) // 确保 code 不为空
    userStore.weixinInfo = res.data
    // 获取到 openid 后，通常也要获取绑定列表
    await getBindList()
  } catch (error: any) {
    toast.error(error.msg || '获取 OpenID 失败')
  }
})
</script>

<style lang="scss" scoped>
.bind-list-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 30rpx;
  background-color: #ffffff;
  background-image: linear-gradient(
    135deg,
    rgba(25, 137, 250, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}

.bg-circle-1 {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.05), rgba(7, 193, 96, 0.1));
}

.bind-list-header {
  margin-top: 120rpx;
  margin-bottom: 40rpx;
  animation: fadeInDown 0.8s ease-out;

  .header-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-bottom: 16rpx;
  }

  .header-desc {
    font-size: 28rpx;
    color: #888888;
    text-align: center;
  }
}

.bind-list-content {
  flex: 1;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.8s ease-out 0.2s both;
}

.bind-list {
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.bind-item {
  display: flex;
  align-items: center;
  padding: 30rpx 24rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.3s;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.bind-item-left {
  margin-right: 24rpx;
}

.bind-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(25, 137, 250, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.bind-item-content {
  flex: 1;
}

.bind-item-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.bind-item-phone {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.bind-item-time {
  font-size: 24rpx;
  color: #999999;
}

.bind-item-right {
  margin-left: 16rpx;
}

.bind-loading,
.bind-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.bind-loading {
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999999;
  }
}

.bind-empty {
  .empty-icon {
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

.add-bind-btn {
  margin-top: 40rpx;
  padding: 0 20rpx;

  :deep(.wd-button) {
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
    box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.25);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 5rpx 10rpx rgba(25, 137, 250, 0.2);
    }
  }
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.bind-list-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 0 30rpx;
  background-color: #ffffff;
  background-image: linear-gradient(
    135deg,
    rgba(25, 137, 250, 0.05) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: relative;
  overflow: hidden;
}

/* 背景装饰元素 */
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(25, 137, 250, 0.05), rgba(25, 137, 250, 0.1));
  z-index: 0;
  pointer-events: none;
}

.bg-circle-1 {
  width: 500rpx;
  height: 500rpx;
  top: -200rpx;
  right: -200rpx;
  opacity: 0.6;
}

.bg-circle-2 {
  width: 400rpx;
  height: 400rpx;
  bottom: 10%;
  left: -200rpx;
  opacity: 0.4;
}

.bg-circle-3 {
  width: 300rpx;
  height: 300rpx;
  bottom: -100rpx;
  right: 10%;
  opacity: 0.3;
  background: linear-gradient(135deg, rgba(7, 193, 96, 0.05), rgba(7, 193, 96, 0.1));
}

.bind-list-header {
  margin-top: 120rpx;
  margin-bottom: 40rpx;
  animation: fadeInDown 0.8s ease-out;

  .header-title {
    font-size: 48rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
    margin-bottom: 16rpx;
  }

  .header-desc {
    font-size: 28rpx;
    color: #888888;
    text-align: center;
  }
}

.bind-list-content {
  flex: 1;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.8s ease-out 0.2s both;
}

.bind-list-scroll {
  height: calc(100vh - 300rpx); /* 减去头部和底部按钮的高度 */
  overflow: hidden;
}

.bind-list {
  margin-bottom: 40rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.bind-item {
  display: flex;
  align-items: center;
  padding: 30rpx 24rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.3s;

  &:active {
    background-color: #f9f9f9;
  }

  &:last-child {
    border-bottom: none;
  }
}

.bind-item-left {
  margin-right: 24rpx;
}

.bind-item-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(25, 137, 250, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.bind-item-content {
  flex: 1;
}

.bind-item-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 8rpx;
}

.bind-item-phone {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.bind-item-time {
  font-size: 24rpx;
  color: #999999;
}

.bind-item-right {
  margin-left: 16rpx;
}

.bind-loading,
.bind-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.bind-loading {
  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #999999;
  }
}

.bind-empty {
  .empty-icon {
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}

.add-bind-btn-fixed {
  position: fixed;
  bottom: 20px;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
}

.add-bind-btn-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 40rpx);
  z-index: 10;
}

.add-bind-btn-fixed,
.add-bind-btn-center {
  :deep(.wd-button) {
    height: 96rpx;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 500;
    letter-spacing: 2rpx;
    box-shadow: 0 10rpx 20rpx rgba(25, 137, 250, 0.25);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 5rpx 10rpx rgba(25, 137, 250, 0.2);
    }
  }
}

.action {
  display: flex;
  height: 100%;
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  color: white;
  font-size: 14px;
}
</style>

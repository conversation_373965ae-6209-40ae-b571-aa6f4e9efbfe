import { defineStore } from 'pinia'
import { ref } from 'vue'

export type WeixinInfo = {
  openid: string
  unionid: string
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 用户的 openid
    const weixinInfo = ref<WeixinInfo>({
      openid: '',
      unionid: '',
    })

    /**
     * 账号绑定
     */
    const bind = async (credentials: { account: string; password: string }) => {
      console.log(credentials)
      // 获取微信小程序登录的code
      // const data = await getWxCode()

      // const res = await _wxLogin(data)
      // return res
    }

    return {
      weixinInfo,
      bind,
    }
  },
  {
    persist: true,
  },
)

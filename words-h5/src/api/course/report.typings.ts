export interface ReportDetailDto {
  userName: string
  reportData: ReportData
}

// 定义 ReportData 内部的 CountData 接口
export interface CountData {
  type: string
  count: number // Java 的 Long 对应 TypeScript 的 number
}

// 定义 ReviewWordData 复习列表
export interface ReviewWordData {
  title: string
  count: number
  correctRate: string
}

// 定义 ReportData 接口
export interface ReportData {
  courseType?: string
  // 词汇总结信息
  wordCountMap?: { [key: string]: CountData[] } // Map<String, List<CountData>> 对应 TS 的 Record<string, CountData[]> 或 { [key: string]: CountData[] }
  // 听说训练时长，分钟
  listenerTrainingTime?: number // Java Long 对应 TS number
  courseTotalTime?: number
  // 实现目标进度
  goalsRate?: number // Java BigDecimal 通常对应 TS number
  // 总单词数量
  wordTotalCnt?: number
  // 句型数量
  sentencesCnt?: number
  // 总阅读量，例句的单词数量
  sentencesWordCnt?: number
  // 未学习前：知识掌握率XX% （第1、第3环节的总正确率，单词和句子的英翻中题目正确率，
  beforeCorrectRateStr?: string
  // 学习后：知识掌握率XX%（下课前复习的正确率）
  reviewCorrectRateStr?: string
  reviewWordDataList?: ReviewWordData[]
  // 本次课程中所有出现错误的单词列表
  errorWordList?: string[]
}

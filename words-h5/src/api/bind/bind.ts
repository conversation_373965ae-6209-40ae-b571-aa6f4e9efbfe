import { http } from '@/utils/http'
import { BindList } from '@/api/bind/bind.typings'
import { WeixinInfo, useUserStore } from '@/store'
const userStore = useUserStore()

/**
 * 获取微信授权地址
 * @returns 返回微信重定向uri
 */
export const getWeixinCodeApi = async () => {
  return http.get<String>('/weixin/mp/code', null)
}

/**
 * 根据code获取openid
 * @returns
 */
export const getOpenidApi = async (code: string) => {
  return http.get<WeixinInfo>('/weixin/mp/getOpenid/' + code, null)
}

/**
 * 获取绑定列表
 * @param data
 * @returns
 */
export const listApi = async () => {
  return http.post<BindList>('/weixin/mp/list', userStore.weixinInfo)
}

/**
 * 账号绑定
 */
export const bindApi = (account: string, password: string) => {
  return http.post<String>('/weixin/mp/bind', {
    account: account,
    password: password,
    openid: userStore.weixinInfo.openid,
    unionid: userStore.weixinInfo.unionid,
  })
}

/**
 * 账号解绑
 */
export const unbindApi = async (id: string) => {
  return http.post<String>('/weixin/mp/unbind', {
    id: id,
    openid: userStore.weixinInfo.openid,
  })
}

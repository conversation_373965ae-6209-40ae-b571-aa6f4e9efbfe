# Words WeChat 项目使用指南

## 项目简介

这是一个基于 UniApp3 + Vue3 + TypeScript + Vite6 + wot-design-uni 构建的微信小程序项目，支持H5页面访问。项目具有完整的登录功能和现代化的UI设计。

## 技术栈

- **框架**: UniApp 3.x
- **前端**: Vue 3 + TypeScript
- **构建工具**: Vite 6.x
- **UI组件库**: wot-design-uni
- **样式**: UnoCSS + SCSS
- **状态管理**: Pinia
- **包管理器**: pnpm

## 项目结构

```
words-wechat/
├── src/
│   ├── api/                 # API接口
│   ├── components/          # 公共组件
│   ├── pages/              # 页面
│   │   ├── index/          # 首页
│   │   ├── login/          # 登录页
│   │   ├── about/          # 关于页
│   │   └── mine/           # 我的页面
│   ├── store/              # 状态管理
│   ├── utils/              # 工具函数
│   ├── style/              # 全局样式
│   └── static/             # 静态资源
├── env/                    # 环境配置
└── ...
```

## 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动开发服务器

#### H5开发
```bash
pnpm dev:h5
```
访问: http://localhost:9000

#### 微信小程序开发
```bash
pnpm dev:mp-weixin
```
然后使用微信开发者工具打开 `dist/dev/mp-weixin` 目录

### 3. 构建生产版本

#### H5构建
```bash
pnpm build:h5
```

#### 微信小程序构建
```bash
pnpm build:mp-weixin
```

## 功能特性

### 登录功能

项目内置了完整的登录系统，支持：

1. **账号密码登录**
   - 用户名/密码验证
   - 验证码支持
   - 表单验证

2. **微信一键登录**（仅小程序环境）
   - 微信授权登录
   - 自动获取用户信息

3. **登录状态管理**
   - 自动保存登录状态
   - Token管理
   - 登录拦截

### UI设计特点

- 现代化的渐变背景设计
- 流畅的动画效果
- 响应式布局
- 优雅的表单交互
- 统一的视觉风格

### 页面路由

- `/pages/index/index` - 首页
- `/pages/login/index` - 登录页
- `/pages/about/about` - 关于页
- `/pages/mine/index` - 我的页面

## 环境配置

项目支持多环境配置，配置文件位于 `env/` 目录：

- `.env` - 基础配置
- `.env.development` - 开发环境
- `.env.production` - 生产环境

### 主要配置项

```bash
# 应用标题
VITE_APP_TITLE = 'Words WeChat'

# 开发端口
VITE_APP_PORT = 9000

# API服务器地址
VITE_SERVER_BASEURL = 'https://ukw0y1.laf.run'

# 微信小程序AppID
VITE_WX_APPID = 'your-wx-appid'
```

## 开发建议

1. **代码规范**: 项目已配置 ESLint + Prettier，请保持代码风格一致
2. **组件开发**: 优先使用 wot-design-uni 组件库
3. **样式编写**: 使用 UnoCSS 原子类 + SCSS 模块化
4. **API调用**: 统一使用 `src/api/` 目录下的接口
5. **状态管理**: 使用 Pinia 进行状态管理

## 部署说明

### H5部署

1. 执行构建命令: `pnpm build:h5`
2. 将 `dist/build/h5` 目录部署到服务器

### 微信小程序发布

1. 执行构建命令: `pnpm build:mp-weixin`
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 上传代码并提交审核

## 常见问题

### 1. 登录接口调用失败

检查 `env/.env` 中的 `VITE_SERVER_BASEURL` 配置是否正确。

### 2. 微信登录无法使用

确保在微信小程序环境中使用，H5环境不支持微信登录。

### 3. 样式显示异常

检查是否正确引入了 wot-design-uni 的样式文件。

## 技术支持

如有问题，请查阅：
- [UniApp官方文档](https://uniapp.dcloud.net.cn/)
- [Vue3官方文档](https://cn.vuejs.org/)
- [wot-design-uni文档](https://wot-design-uni.cn/)
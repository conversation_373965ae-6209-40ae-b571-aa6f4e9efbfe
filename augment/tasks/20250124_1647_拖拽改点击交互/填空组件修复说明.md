# 填空组件问题修复说明

## 问题描述
用户反馈：
1. **句子填空组件**：点击单词时，默认填入的是第二个空白框，而不是第一个空白框
2. **单词填空组件**：点击字母时，也存在相同的问题

## 问题原因分析
两个组件都存在相同的逻辑问题。在自动选择空白位置时，使用了 `Array.find()` 方法直接在 `blankIndices` 数组中查找：

### 原有问题代码
```javascript
// 句子填空组件 - SentenceFillBlank.vue
const firstEmptyPosition = blankIndices.find((indexStr: string) => {
  const index = Number(indexStr)
  return !droppedWords.value[index]
})

// 单词填空组件 - WordFillBlank.vue  
const nextEmptyPosition = blankIndices.find((indexStr: string) => {
  const index = Number(indexStr)
  return !droppedLetters.value[index]
})
```

### 问题根因
`blankIndices` 数组中的索引可能不是按顺序排列的（比如 `["2", "0", "4"]`），`Array.find()` 返回的是数组中第一个匹配的元素，而不是按照在句子/单词中出现的位置顺序。

## 修复方案

### 1. 句子填空组件修复 (SentenceFillBlank.vue)
```javascript
// 修复后的代码
const blankIndices = currentStepInfo.value?.step?.options || []
// 将字符串索引转换为数字并排序，然后找到第一个空白位置
const sortedIndices = blankIndices
  .map(indexStr => Number(indexStr))
  .sort((a, b) => a - b)

const firstEmptyPosition = sortedIndices.find(index => !droppedWords.value[index])
```

### 2. 单词填空组件修复 (WordFillBlank.vue)
```javascript
// 修复后的代码
const blankIndices = currentStepInfo.value?.step?.options || []
// 将字符串索引转换为数字并排序，然后找到第一个空白位置
const sortedIndices = blankIndices
  .map(indexStr => Number(indexStr))
  .sort((a, b) => a - b)

const nextEmptyPosition = sortedIndices.find(index => !droppedLetters.value[index])
```

## 修复效果
- ✅ **句子填空**：现在点击单词时，会按照在句子中从左到右的顺序填入第一个空白位置
- ✅ **单词填空**：现在点击字母时，会按照在单词中从左到右的顺序填入第一个空白位置
- ✅ 保持了原有的其他功能不变
- ✅ 用户体验更加符合直觉

## 测试验证
请分别测试以下场景：

### 句子填空组件
1. 在没有选中任何位置的情况下，点击单词应该填入最左边的空白位置
2. 当第一个位置已填入时，点击单词应该填入下一个空白位置
3. 手动选中特定位置后，点击单词应该填入选中的位置

### 单词填空组件
1. 点击字母应该按照单词中从左到右的顺序填入空白位置
2. 当前面的位置已填入时，点击字母应该填入下一个空白位置
3. 撤销功能应该正常工作

## 相关文件
- `words-frontend/src/views/course/components/SentenceFillBlank.vue` (第168-183行)
- `words-frontend/src/views/course/components/WordFillBlank.vue` (第245-255行)

## 修复时间
2025年1月24日 16:47

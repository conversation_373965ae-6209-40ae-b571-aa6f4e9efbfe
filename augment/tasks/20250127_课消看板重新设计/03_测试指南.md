# 课消看板测试指南

## 测试前准备

### 1. 后端配置
确保以下配置已完成：
- [ ] 数据库中有课消数据 (`student_course_consumption` 表)
- [ ] 教学组和成员关系配置正确
- [ ] 用户角色权限配置正确

### 2. 前端配置
- [ ] 路由配置已添加到 `dynamicRoutes`
- [ ] 组件文件已正确放置
- [ ] API 接口路径正确

## 测试用例

### 1. 老师角色测试

#### 测试账号要求
- 角色：teacher
- 需要有学生数据和课消记录

#### 测试步骤
1. **登录系统**
   - 使用老师账号登录
   - 验证能看到课消看板菜单

2. **访问老师看板**
   - 访问 `/dashboard/course-consumption/teacher`
   - 验证页面正常加载

3. **功能测试**
   - [ ] 时间范围选择器工作正常
   - [ ] 统计卡片显示正确数据
   - [ ] 课消趋势图正常显示
   - [ ] 学生列表显示正确
   - [ ] 学生详情对话框正常打开
   - [ ] 学科分布图表正常显示

4. **权限测试**
   - [ ] 只能看到自己的学生数据
   - [ ] 无法访问其他角色的看板页面

### 2. 组长角色测试

#### 测试账号要求
- 角色：teaching_group_leader 或 teaching_group_admin
- 需要管理一个教学组
- 组内需要有老师和学生数据

#### 测试步骤
1. **登录系统**
   - 使用组长账号登录
   - 验证能看到课消看板菜单

2. **访问组长看板**
   - 访问 `/dashboard/course-consumption/group-leader`
   - 验证页面正常加载

3. **组视图测试**
   - [ ] 显示本组老师统计
   - [ ] 老师选择器显示本组老师
   - [ ] 统计数据正确

4. **老师视图测试**
   - [ ] 选择特定老师后显示学生数据
   - [ ] 返回组视图功能正常
   - [ ] 学生详情对话框正常

5. **权限测试**
   - [ ] 只能看到本组的数据
   - [ ] 老师选择器只显示本组老师

### 3. 管理角色测试

#### 测试账号要求
- 角色：admin 或 hr
- 系统中需要有多个教学组数据

#### 测试步骤
1. **登录系统**
   - 使用管理员账号登录
   - 验证能看到课消看板菜单

2. **访问管理看板**
   - 访问 `/dashboard/course-consumption/admin`
   - 验证页面正常加载

3. **全局视图测试**
   - [ ] 显示所有组统计
   - [ ] 组选择器显示所有组
   - [ ] 统计数据正确

4. **组视图测试**
   - [ ] 选择特定组后显示该组老师
   - [ ] 老师选择器显示该组老师
   - [ ] 面包屑导航正常

5. **老师视图测试**
   - [ ] 选择特定老师后显示学生数据
   - [ ] 面包屑导航正常
   - [ ] 返回功能正常

6. **权限测试**
   - [ ] 可以查看所有数据
   - [ ] 选择器显示所有选项

### 4. 自动跳转测试

#### 测试步骤
1. **访问入口页面**
   - 访问 `/dashboard/course-consumption/index`
   - 验证根据角色自动跳转

2. **角色跳转验证**
   - [ ] admin/hr → `/admin`
   - [ ] teaching_group_leader/admin → `/group-leader`
   - [ ] teacher → `/teacher`
   - [ ] 无权限用户 → 错误提示

## 性能测试

### 1. 数据加载性能
- [ ] 大量数据时页面加载时间 < 3秒
- [ ] 图表渲染流畅
- [ ] 表格滚动流畅

### 2. 交互响应性能
- [ ] 时间范围切换响应 < 1秒
- [ ] 选择器切换响应 < 1秒
- [ ] 对话框打开响应 < 0.5秒

## 兼容性测试

### 1. 浏览器兼容性
- [ ] Chrome (最新版)
- [ ] Firefox (最新版)
- [ ] Safari (最新版)
- [ ] Edge (最新版)

### 2. 响应式测试
- [ ] 桌面端 (1920x1080)
- [ ] 笔记本 (1366x768)
- [ ] 平板 (768x1024)

## 错误处理测试

### 1. 网络错误
- [ ] 断网情况下的错误提示
- [ ] 接口超时的处理
- [ ] 服务器错误的处理

### 2. 数据异常
- [ ] 空数据的显示
- [ ] 异常数据的处理
- [ ] 权限不足的提示

## 用户体验测试

### 1. 界面友好性
- [ ] 加载状态提示清晰
- [ ] 错误信息友好
- [ ] 操作反馈及时

### 2. 操作便利性
- [ ] 导航清晰易懂
- [ ] 功能入口明显
- [ ] 操作流程顺畅

## 问题记录模板

### 问题描述
- **问题类型**: 功能/性能/兼容性/用户体验
- **重现步骤**: 
- **预期结果**: 
- **实际结果**: 
- **影响程度**: 高/中/低
- **浏览器环境**: 
- **用户角色**: 

### 解决方案
- **修复方法**: 
- **验证结果**: 
- **相关文件**: 

## 测试完成标准

- [ ] 所有功能测试用例通过
- [ ] 性能指标达标
- [ ] 兼容性测试通过
- [ ] 错误处理正常
- [ ] 用户体验良好
- [ ] 无阻塞性问题

## 上线前检查清单

### 后端检查
- [ ] API 接口正常响应
- [ ] 权限控制正确
- [ ] 数据查询性能良好
- [ ] 错误日志正常

### 前端检查
- [ ] 路由配置正确
- [ ] 组件加载正常
- [ ] 样式显示正确
- [ ] 交互功能正常

### 数据检查
- [ ] 测试数据准备充分
- [ ] 权限数据配置正确
- [ ] 菜单配置完成

## 注意事项

1. **数据权限**: 确保不同角色只能看到授权的数据
2. **性能监控**: 关注大数据量情况下的性能表现
3. **错误处理**: 确保所有异常情况都有友好的提示
4. **用户反馈**: 收集用户使用反馈，持续优化

## 测试报告

测试完成后，请填写测试报告：
- 测试时间：
- 测试人员：
- 测试环境：
- 测试结果：
- 发现问题：
- 建议改进：

# 课消看板重新设计 - 需求分析和架构设计

## 需求分析

### 背景
需要老师、组长、管理关注课消情况，并对异常课消进行预警

### 用户角色和需求

#### 1. 老师角色
- **查看范围**: 自己的学生
- **关注数据**:
  - 指定时间区间内学生周均课消趋势
  - 平均周课消
  - 学生个人课消详情
- **时间周期**: 支持本周、上周、本月、上月等快捷选择

#### 2. 组长角色  
- **查看范围**: 本组老师和学生
- **关注数据**:
  - 本组老师的课消情况
  - 指定时间区间内老师周均课消趋势和平均周课消
  - 组维度和老师个人维度统计
  - 可以点击查看具体学生
- **切换功能**: 可以切换组内老师查看

#### 3. 管理角色
- **查看范围**: 所有组
- **关注数据**:
  - 组级别课消统计
  - 可以查看组长看板（切换组）
  - 可以查看老师看板（切换老师）
- **切换功能**: 支持切换小组或者老师

## 架构设计

### 后端架构

#### API设计
```
/api/course-consumption-dashboard/
├── teacher/data          # 老师看板数据
├── group-leader/data     # 组长看板数据  
├── admin/data           # 管理看板数据
├── teacher/students     # 老师的学生列表
├── group-leader/teachers # 组长的老师列表
├── admin/groups         # 管理的组列表
└── admin/teachers       # 管理的老师列表
```

#### DTO设计
```java
// 通用请求参数
class DashboardRequest {
    String startDate;
    String endDate; 
    String timeRangeType; // thisWeek, lastWeek, thisMonth, lastMonth
}

// 老师看板请求
class TeacherDashboardRequest extends DashboardRequest {
    String teacherId; // 可选，默认当前用户
}

// 组长看板请求  
class GroupLeaderDashboardRequest extends DashboardRequest {
    String groupId; // 可选，默认当前用户所在组
    String teacherId; // 可选，查看特定老师
}

// 管理看板请求
class AdminDashboardRequest extends DashboardRequest {
    String groupId; // 可选，查看特定组
    String teacherId; // 可选，查看特定老师
}
```

#### 响应数据结构
```java
// 老师看板响应
class TeacherDashboardResponse {
    TimeRangeInfo timeRangeInfo;
    TeacherOverallStats overallStats;
    List<StudentConsumptionStats> studentStats;
    List<WeeklyTrendData> weeklyTrends;
}

// 组长看板响应
class GroupLeaderDashboardResponse {
    TimeRangeInfo timeRangeInfo;
    GroupOverallStats groupStats;
    List<TeacherConsumptionStats> teacherStats;
    List<StudentConsumptionStats> studentStats; // 当选择特定老师时
    List<WeeklyTrendData> weeklyTrends;
}

// 管理看板响应
class AdminDashboardResponse {
    TimeRangeInfo timeRangeInfo;
    AdminOverallStats overallStats;
    List<GroupConsumptionStats> groupStats;
    List<TeacherConsumptionStats> teacherStats; // 当选择特定组时
    List<StudentConsumptionStats> studentStats; // 当选择特定老师时
    List<WeeklyTrendData> weeklyTrends;
}
```

### 前端架构

#### 页面结构
```
src/views/dashboard/course-consumption/
├── teacher/
│   ├── index.vue                    # 老师看板主页
│   └── components/
│       ├── StudentStatsCard.vue     # 学生统计卡片
│       ├── WeeklyTrendChart.vue     # 周课消趋势图
│       └── StudentDetailTable.vue   # 学生详情表格
├── group-leader/
│   ├── index.vue                    # 组长看板主页
│   └── components/
│       ├── GroupStatsCard.vue       # 组统计卡片
│       ├── TeacherStatsTable.vue    # 老师统计表格
│       ├── TeacherSelector.vue      # 老师选择器
│       └── StudentDetailTable.vue   # 学生详情表格
├── admin/
│   ├── index.vue                    # 管理看板主页
│   └── components/
│       ├── AdminStatsCard.vue       # 管理统计卡片
│       ├── GroupStatsTable.vue      # 组统计表格
│       ├── GroupTeacherSelector.vue # 组/老师选择器
│       └── DetailView.vue           # 详情视图
└── shared/
    ├── TimeRangeSelector.vue        # 时间范围选择器
    ├── ConsumptionTrendChart.vue    # 课消趋势图
    └── StatisticsCard.vue           # 统计卡片组件
```

#### 路由设计
```javascript
{
  path: '/dashboard/course-consumption',
  component: Layout,
  redirect: '/dashboard/course-consumption/index',
  name: 'CourseConsumptionDashboard',
  meta: { title: '课消看板', icon: 'chart' },
  children: [
    {
      path: 'teacher',
      component: () => import('@/views/dashboard/course-consumption/teacher/index'),
      name: 'TeacherConsumptionDashboard',
      meta: { title: '老师课消看板', roles: ['teacher'] }
    },
    {
      path: 'group-leader', 
      component: () => import('@/views/dashboard/course-consumption/group-leader/index'),
      name: 'GroupLeaderConsumptionDashboard',
      meta: { title: '组长课消看板', roles: ['teaching_group_leader', 'teaching_group_admin'] }
    },
    {
      path: 'admin',
      component: () => import('@/views/dashboard/course-consumption/admin/index'),
      name: 'AdminConsumptionDashboard', 
      meta: { title: '管理课消看板', roles: ['admin', 'hr'] }
    }
  ]
}
```

## 数据模型设计

### 统计指标定义
- **总课消**: 指定时间范围内的课消总和
- **周均课消**: 总课消 ÷ 时间范围周数
- **月均课消**: 总课消 ÷ 时间范围月数  
- **课消率**: 有课消学生数 ÷ 总学生数
- **活跃度**: 最近一周有课消的学生占比

### 时间范围处理
- **本周**: 周一到当前日期
- **上周**: 上周一到上周日
- **本月**: 本月1号到当前日期
- **上月**: 上月1号到上月最后一天
- **自定义**: 用户选择的日期范围

### 权限控制
- 基于现有的角色权限系统
- 老师只能查看自己的数据
- 组长只能查看本组数据
- 管理员可以查看所有数据

## 技术要点

### 性能优化
- 使用缓存减少数据库查询
- 分页加载大量数据
- 异步加载图表数据

### 用户体验
- 响应式设计适配不同屏幕
- 加载状态提示
- 数据为空时的友好提示
- 图表交互和钻取功能

### 数据安全
- 严格的权限控制
- 数据脱敏处理
- 操作日志记录

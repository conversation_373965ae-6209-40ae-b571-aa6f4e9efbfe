# 课消看板本地路由配置说明

## 配置概述

已将课消看板路由配置为本地路由，不依赖后端动态路由返回。路由直接添加到 `constantRoutes` 中，并实现了基于角色的权限控制。

## 路由配置

### 1. 路由结构
```javascript
{
  path: '/dashboard/course-consumption',
  component: Layout,
  redirect: '/dashboard/course-consumption/index',
  name: 'CourseConsumptionDashboard',
  meta: {
    title: '课消看板',
    icon: 'chart'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/dashboard/course-consumption-role/index'),
      name: 'CourseConsumptionDashboardIndex',
      meta: {
        title: '课消看板',
        icon: 'chart',
        noCache: true
      }
    },
    {
      path: 'teacher',
      component: () => import('@/views/dashboard/course-consumption-role/teacher/index'),
      name: 'TeacherConsumptionDashboard',
      meta: {
        title: '老师课消看板',
        icon: 'user',
        noCache: true,
        roles: ['teacher', 'admin', 'hr']
      }
    },
    {
      path: 'group-leader',
      component: () => import('@/views/dashboard/course-consumption-role/group-leader/index'),
      name: 'GroupLeaderConsumptionDashboard',
      meta: {
        title: '组长课消看板',
        icon: 'peoples',
        noCache: true,
        roles: ['teaching_group_leader', 'teaching_group_admin', 'admin', 'hr']
      }
    },
    {
      path: 'admin',
      component: () => import('@/views/dashboard/course-consumption-role/admin/index'),
      name: 'AdminConsumptionDashboard',
      meta: {
        title: '管理课消看板',
        icon: 'monitor',
        noCache: true,
        roles: ['admin', 'hr']
      }
    }
  ]
}
```

### 2. 权限控制

#### 角色权限配置
- **teacher**: 只能访问老师看板
- **teaching_group_leader, teaching_group_admin**: 可以访问组长看板
- **admin, hr**: 可以访问所有看板

#### 权限控制实现
在 `permission.js` 中添加了 `filterLocalRoutes` 函数：
```javascript
export function filterLocalRoutes(routes) {
  const res = []
  routes.forEach(route => {
    const tmp = { ...route }
    
    // 检查路由权限
    if (tmp.meta && tmp.meta.roles) {
      if (!auth.hasRoleOr(tmp.meta.roles)) {
        return // 没有权限，跳过这个路由
      }
    }
    
    // 递归处理子路由
    if (tmp.children) {
      tmp.children = filterLocalRoutes(tmp.children)
      if (tmp.children.length === 0 && tmp.redirect) {
        return
      }
    }
    
    res.push(tmp)
  })
  return res
}
```

## 访问方式

### 1. 直接访问
用户可以直接访问以下URL：
- `/dashboard/course-consumption` - 入口页面，自动跳转到对应角色的看板
- `/dashboard/course-consumption/teacher` - 老师看板
- `/dashboard/course-consumption/group-leader` - 组长看板
- `/dashboard/course-consumption/admin` - 管理看板

### 2. 菜单访问
路由会自动出现在侧边栏菜单中，根据用户角色显示相应的菜单项。

### 3. 自动跳转逻辑
入口页面 (`/dashboard/course-consumption/index`) 会根据用户角色自动跳转：
```javascript
if (this.hasRole(roles, ['admin', 'hr'])) {
  // 管理员和HR跳转到管理看板
  this.$router.replace('/dashboard/course-consumption/admin')
} else if (this.hasRole(roles, ['teaching_group_leader', 'teaching_group_admin'])) {
  // 教学组长跳转到组长看板
  this.$router.replace('/dashboard/course-consumption/group-leader')
} else if (this.hasRole(roles, ['teacher'])) {
  // 老师跳转到老师看板
  this.$router.replace('/dashboard/course-consumption/teacher')
} else {
  // 没有权限
  this.$modal.msgError('您没有权限访问课消看板')
  this.$router.replace('/')
}
```

## 测试验证

### 1. 权限测试
使用不同角色的账号登录，验证：
- [ ] 只能看到有权限的菜单项
- [ ] 直接访问无权限的URL会被拦截
- [ ] 自动跳转到正确的看板页面

### 2. 功能测试
- [ ] 各个看板页面正常加载
- [ ] API接口正常调用
- [ ] 数据正常显示

### 3. 菜单显示测试
- [ ] 侧边栏菜单正确显示
- [ ] 菜单图标和标题正确
- [ ] 点击菜单正常跳转

## 注意事项

### 1. 角色名称
确保后端返回的用户角色名称与路由配置中的角色名称一致：
- `teacher` - 老师
- `teaching_group_leader` - 教学组长
- `teaching_group_admin` - 教学组管理员
- `admin` - 管理员
- `hr` - 人力资源

### 2. 权限验证
系统使用 `auth.hasRoleOr()` 方法进行权限验证，只要用户拥有配置的角色中的任意一个即可访问。

### 3. 缓存控制
所有看板页面都设置了 `noCache: true`，确保每次访问都是最新数据。

### 4. 图标配置
路由使用了以下图标：
- `chart` - 课消看板主菜单
- `user` - 老师看板
- `peoples` - 组长看板
- `monitor` - 管理看板

## 故障排除

### 1. 菜单不显示
- 检查用户是否有对应的角色权限
- 检查路由配置是否正确
- 查看浏览器控制台是否有错误

### 2. 页面访问被拦截
- 检查用户角色是否正确
- 检查路由权限配置
- 确认权限验证逻辑是否正常

### 3. 自动跳转不正确
- 检查入口页面的跳转逻辑
- 确认用户角色数据是否正确获取
- 查看控制台日志

## 扩展说明

如果需要添加新的角色或修改权限，只需要：
1. 修改路由配置中的 `roles` 数组
2. 更新入口页面的跳转逻辑
3. 确保后端返回正确的角色信息

本地路由配置的优势：
- 不依赖后端菜单配置
- 响应速度快
- 易于维护和调试
- 支持灵活的权限控制

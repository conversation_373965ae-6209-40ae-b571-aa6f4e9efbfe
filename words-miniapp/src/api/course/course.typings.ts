// 课程信息DTO
export interface CourseInfoDto {
  id: string
  type: string
  status: string
  teacher: {
    id: string
    name: string
    avatar: string
  }
  student: {
    id: string
    name: string
    avatar: string
  }
  scheduledStartTime: string
  scheduledEndTime: string
  actualStartTime: string
  actualEndTime: string
  durationMinutes: number
  content: CourseContent
}

// 课程内容
export interface CourseContent {
  currentSectionIndex: number
  sections: Section[]
}

// 课程环节
export interface Section {
  id: string
  title: string
  type: string
  status: string
  currentWordIndex: number
  startTime: string
  endTime: string
  words: CourseSectionWordDto[]
}

// 课程单词DTO
export interface CourseSectionWordDto {
  id: string
  status: string
  result: string
  currentStepIndex: number
  wordInfo: CourseSectionWordInfo
  steps: any[]
}

// 课程单词信息
export interface CourseSectionWordInfo {
  id: string
  word: string
  syllables: string
  phoneticUk: string
  phoneticUs: string
  difficulty: string
  videoUrl: string
  audioUkUrl: string
  audioUsUrl: string
  meanings: WordMeanings
  sentences: WordSentences
}

// 单词释义
export interface WordMeanings {
  pos: Array<{pos: string, def: string}>
  practices: string[]
}

// 单词例句
export interface WordSentences {
  [stage: string]: Array<{
    sentenceEn: string
    sentenceCn: string
    audioUkUrl: string
    audioUsUrl: string
    structurePartsEn: string[]
    practices: string[]
    syllables: string
    stage: string
  }>
}

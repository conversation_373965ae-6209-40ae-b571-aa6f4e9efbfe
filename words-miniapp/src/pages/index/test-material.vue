<template>
  <view class="test-page">
    <view class="header">
      <text class="title">课堂资料测试页面</text>
      <text class="subtitle">点击下方按钮测试课堂资料查看功能</text>
    </view>

    <view class="test-section">
      <text class="section-title">📚 测试课程</text>
      
      <view class="course-list">
        <view 
          v-for="course in testCourses" 
          :key="course.id"
          class="course-item"
          @click="openMaterialView(course.id)"
        >
          <view class="course-info">
            <text class="course-name">{{ course.name }}</text>
            <text class="course-desc">{{ course.description }}</text>
          </view>
          <view class="course-action">
            <text class="action-text">查看资料</text>
            <text class="action-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">🔧 自定义测试</text>
      
      <view class="custom-test">
        <view class="input-group">
          <text class="input-label">课程ID：</text>
          <input 
            v-model="customCourseId" 
            class="input-field"
            placeholder="请输入课程ID"
            type="text"
          />
        </view>
        
        <button 
          @click="openMaterialView(customCourseId)"
          class="test-btn custom"
          :disabled="!customCourseId"
        >
          <text class="btn-text">测试自定义课程</text>
        </button>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">📱 功能说明</text>
      
      <view class="feature-list">
        <view class="feature-item">
          <text class="feature-icon">🎵</text>
          <text class="feature-text">点击音标播放发音</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">🎤</text>
          <text class="feature-text">跟读练习功能</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">👆</text>
          <text class="feature-text">左右滑动切换单词</text>
        </view>
        <view class="feature-item">
          <text class="feature-icon">📖</text>
          <text class="feature-text">查看释义和例句</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 响应式数据
const customCourseId = ref('')

// 测试课程数据
const testCourses = ref([
  {
    id: 'test-course-001',
    name: '小学英语基础词汇',
    description: '包含apple、book等基础单词'
  },
  {
    id: 'demo-course-123',
    name: '中学英语进阶词汇',
    description: '包含更多复杂单词和例句'
  },
  {
    id: 'sample-course-456',
    name: '高中英语核心词汇',
    description: '高频词汇和重点语法'
  }
])

// 方法
const openMaterialView = (courseId: string) => {
  if (!courseId || courseId.trim() === '') {
    uni.showToast({
      title: '请输入有效的课程ID',
      icon: 'none'
    })
    return
  }

  console.log('打开课堂资料页面，课程ID:', courseId)
  
  uni.navigateTo({
    url: `/pages/course/material-view?courseId=${courseId}`,
    success: () => {
      console.log('页面跳转成功')
    },
    fail: (err) => {
      console.error('页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: white;
    margin-bottom: 8px;
  }
  
  .subtitle {
    display: block;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
  }
}

.test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  
  .section-title {
    display: block;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 16px;
  }
}

.course-list {
  .course-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    
    &:active {
      background: #e9ecef;
      transform: scale(0.98);
    }
    
    .course-info {
      flex: 1;
      
      .course-name {
        display: block;
        font-size: 16px;
        font-weight: 500;
        color: #2c3e50;
        margin-bottom: 4px;
      }
      
      .course-desc {
        display: block;
        font-size: 14px;
        color: #7f8c8d;
      }
    }
    
    .course-action {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .action-text {
        font-size: 14px;
        color: #667eea;
        font-weight: 500;
      }
      
      .action-icon {
        font-size: 16px;
        color: #667eea;
      }
    }
  }
}

.custom-test {
  .input-group {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    
    .input-label {
      font-size: 14px;
      color: #2c3e50;
      margin-right: 12px;
      min-width: 60px;
    }
    
    .input-field {
      flex: 1;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 12px;
      font-size: 14px;
      color: #2c3e50;
    }
  }
  
  .test-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:disabled {
      background: #dee2e6;
      color: #adb5bd;
    }
    
    &:not(:disabled):active {
      transform: scale(0.98);
    }
    
    .btn-text {
      color: inherit;
    }
  }
}

.feature-list {
  .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    
    &:last-child {
      border-bottom: none;
    }
    
    .feature-icon {
      font-size: 20px;
      width: 32px;
      text-align: center;
    }
    
    .feature-text {
      flex: 1;
      font-size: 14px;
      color: #2c3e50;
    }
  }
}
</style>

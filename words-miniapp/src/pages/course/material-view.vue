<template>
  <view class="material-view">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content">
        <view class="navbar-left" @click="goBack">
          <text class="icon-back">←</text>
        </view>
        <view class="navbar-title">课堂资料</view>
        <view class="navbar-right"></view>
      </view>
    </view>

    <!-- 录音状态指示器 -->
    <view v-if="isRecording" class="recording-indicator">
      <view class="recording-dot"></view>
      <text>正在录音</text>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text>正在加载课程资料...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <text class="error-icon">⚠️</text>
      <text class="error-text">{{ error }}</text>
      <button @click="loadCourseData" class="retry-btn">重新加载</button>
    </view>

    <!-- 课程内容 -->
    <view v-else-if="courseInfo" class="content-container">
      <!-- 顶部信息栏 -->
      <view class="top-info">
        <view class="course-info">
          <text class="course-title">课堂资料</text>
          <view class="participants">
            <text class="teacher">👨‍🏫 {{ courseInfo.teacher?.name || '老师' }}</text>
            <text class="student">👨‍🎓 {{ courseInfo.student?.name || '同学' }}</text>
          </view>
        </view>
        
        <!-- 进度指示器 -->
        <view class="progress-indicator">
          <text class="progress-text">{{ currentWordIndex + 1 }} / {{ totalWords }}</text>
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progressPercentage + '%' }"></view>
          </view>
        </view>
      </view>

      <!-- 单词卡片 -->
      <view class="word-container" v-if="currentWord" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd">
        <view class="word-card">
          <!-- 单词标题 -->
          <view class="word-header">
            <text class="word-title">{{ currentWord.wordInfo.word }}</text>
            <view class="difficulty-badge" :class="currentWord.wordInfo.difficulty">
              <text>{{ getDifficultyText(currentWord.wordInfo.difficulty) }}</text>
            </view>
          </view>

          <!-- 音标区域 -->
          <view class="phonetics-section">
            <view 
              class="phonetic-item uk"
              @click="playWordAudio(currentWord.wordInfo, 'uk')"
              :class="{ 'playing': currentPlayingAudio === `word-${currentWord.wordInfo.id}-uk` }"
            >
              <text class="phonetic-label">英式</text>
              <text class="phonetic-text">{{ currentWord.wordInfo.phoneticUk }}</text>
              <text class="play-icon">🔊</text>
            </view>
            <view 
              class="phonetic-item us"
              @click="playWordAudio(currentWord.wordInfo, 'us')"
              :class="{ 'playing': currentPlayingAudio === `word-${currentWord.wordInfo.id}-us` }"
            >
              <text class="phonetic-label">美式</text>
              <text class="phonetic-text">{{ currentWord.wordInfo.phoneticUs }}</text>
              <text class="play-icon">🔊</text>
            </view>
          </view>

          <!-- 释义区域 -->
          <view class="meanings-section" v-if="currentWord.wordInfo.meanings?.pos">
            <text class="section-title">📖 释义</text>
            <view class="meanings-list">
              <view 
                v-for="(meaning, idx) in currentWord.wordInfo.meanings.pos" 
                :key="idx"
                class="meaning-item"
              >
                <text class="pos-tag">{{ meaning.pos }}</text>
                <text class="definition">{{ meaning.def }}</text>
              </view>
            </view>
          </view>

          <!-- 例句区域 -->
          <view class="sentences-section" v-if="getSentencesList(currentWord.wordInfo.sentences).length > 0">
            <text class="section-title">💬 例句</text>
            <view class="sentences-list">
              <view 
                v-for="(sentence, idx) in getSentencesList(currentWord.wordInfo.sentences)" 
                :key="idx"
                class="sentence-item"
                @click="playSentenceAudio(sentence, 'uk')"
                :class="{ 'playing': currentPlayingAudio === `sentence-${idx}` }"
              >
                <view class="sentence-en">
                  <text class="text">{{ sentence.sentenceEn }}</text>
                  <text class="play-icon">🔊</text>
                </view>
                <text class="sentence-cn">{{ sentence.sentenceCn }}</text>
              </view>
            </view>
          </view>

          <!-- 跟读练习区域 -->
          <view class="practice-section">
            <text class="section-title">🎤 跟读练习</text>
            <view class="practice-controls">
              <button 
                @click="startFollowReading(currentWord.wordInfo, 'uk')" 
                class="practice-btn"
                :disabled="isRecording"
                :class="{ 'recording': isRecording }"
              >
                <text class="icon">🎤</text>
                <text class="text">{{ isRecording ? '录音中...' : '开始跟读' }}</text>
              </button>
              
              <button 
                v-if="isRecording"
                @click="stopRecording" 
                class="stop-btn"
              >
                <text class="icon">⏹️</text>
                <text class="text">停止录音</text>
              </button>
              
              <button 
                v-if="recordedAudio"
                @click="playRecordedAudio" 
                class="playback-btn"
              >
                <text class="icon">▶️</text>
                <text class="text">播放录音</text>
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部导航 -->
      <view class="bottom-navigation">
        <button 
          @click="previousWord" 
          :disabled="currentWordIndex <= 0"
          class="nav-btn prev"
          :class="{ 'disabled': currentWordIndex <= 0 }"
        >
          <text class="icon">◀️</text>
          <text class="text">上一个</text>
        </button>
        
        <view class="word-dots">
          <view 
            v-for="(word, index) in wordsList" 
            :key="word.id"
            class="dot"
            :class="{ 'active': index === currentWordIndex }"
            @click="goToWord(index)"
          ></view>
        </view>
        
        <button 
          @click="nextWord" 
          :disabled="currentWordIndex >= totalWords - 1"
          class="nav-btn next"
          :class="{ 'disabled': currentWordIndex >= totalWords - 1 }"
        >
          <text class="text">下一个</text>
          <text class="icon">▶️</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getCourseInfo } from '@/api/course/course'
import type { CourseInfoDto, CourseSectionWordDto, CourseSectionWordInfo, WordSentences } from '@/api/course/course.typings'

// 响应式数据
const loading = ref(true)
const error = ref('')
const courseInfo = ref<CourseInfoDto | null>(null)
const currentWordIndex = ref(0)
const isRecording = ref(false)
const recordedAudio = ref<string>('')
const currentPlayingAudio = ref<string>('')

// 小程序专用状态
const statusBarHeight = ref(0)
const courseId = ref('')
const touchStartX = ref(0)
const touchStartY = ref(0)
const isSwiping = ref(false)

// 录音管理器
let recorderManager: UniApp.RecorderManager | null = null
let innerAudioContext: UniApp.InnerAudioContext | null = null

// 计算属性
const wordsList = computed(() => {
  if (!courseInfo.value?.content?.sections) return []
  
  const allWords: CourseSectionWordDto[] = []
  courseInfo.value.content.sections.forEach((section: any) => {
    if (section.words) {
      allWords.push(...section.words)
    }
  })
  return allWords
})

const totalWords = computed(() => wordsList.value.length)

const progressPercentage = computed(() => {
  if (totalWords.value === 0) return 0
  return Math.round(((currentWordIndex.value + 1) / totalWords.value) * 100)
})

const currentWord = computed(() => {
  return wordsList.value[currentWordIndex.value] || null
})

// 页面加载
onLoad((options) => {
  courseId.value = options?.courseId || ''
  
  // 获取状态栏高度
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  
  // 初始化录音管理器
  initRecorderManager()
  
  // 加载课程数据
  if (courseId.value) {
    loadCourseData()
  } else {
    error.value = '缺少课程ID参数'
    loading.value = false
  }
})

// 方法
const goBack = () => {
  uni.navigateBack()
}

const getDifficultyText = (difficulty: string) => {
  const difficultyMap: Record<string, string> = {
    'easy': '简单',
    'medium': '中等', 
    'hard': '困难',
    'expert': '专家'
  }
  return difficultyMap[difficulty] || '未知'
}

const loadCourseData = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const response = await getCourseInfo(courseId.value)
    if (response.code === 200) {
      courseInfo.value = response.data
      console.log('课程信息加载成功:', courseInfo.value)
    } else {
      throw new Error(response.msg || '获取课程信息失败')
    }
  } catch (err: any) {
    console.error('加载课程数据失败:', err)
    error.value = err.message || '加载课程数据失败'
    uni.showToast({
      title: error.value,
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 获取例句列表的辅助方法
const getSentencesList = (sentences: any) => {
  if (!sentences) return []
  
  // 如果是Map结构 {stage: [sentences]}
  if (typeof sentences === 'object' && !Array.isArray(sentences)) {
    const allSentences: any[] = []
    Object.keys(sentences).forEach(stage => {
      if (sentences[stage] && Array.isArray(sentences[stage])) {
        allSentences.push(...sentences[stage])
      }
    })
    return allSentences
  }
  
  // 如果直接是数组
  if (Array.isArray(sentences)) {
    return sentences
  }
  
  return []
}

const playWordAudio = (wordInfo: CourseSectionWordInfo, accent: 'uk' | 'us') => {
  const audioUrl = accent === 'uk' ? wordInfo.audioUkUrl : wordInfo.audioUsUrl
  if (audioUrl) {
    currentPlayingAudio.value = `word-${wordInfo.id}-${accent}`
    playAudio(audioUrl)
  } else {
    uni.showToast({
      title: '音频文件不可用',
      icon: 'none'
    })
  }
}

const playSentenceAudio = (sentence: any, accent: 'uk' | 'us') => {
  const audioUrl = accent === 'uk' ? sentence.audioUkUrl : sentence.audioUsUrl
  if (audioUrl) {
    currentPlayingAudio.value = `sentence-${sentence.sentenceEn}`
    playAudio(audioUrl)
  } else {
    uni.showToast({
      title: '音频文件不可用',
      icon: 'none'
    })
  }
}

const playAudio = (url: string) => {
  if (innerAudioContext) {
    innerAudioContext.destroy()
  }
  
  innerAudioContext = uni.createInnerAudioContext()
  innerAudioContext.src = url
  innerAudioContext.play()
  
  innerAudioContext.onEnded(() => {
    currentPlayingAudio.value = ''
  })
  
  innerAudioContext.onError((err) => {
    console.error('音频播放失败:', err)
    currentPlayingAudio.value = ''
    uni.showToast({
      title: '音频播放失败',
      icon: 'none'
    })
  })
}

// 录音相关方法
const initRecorderManager = () => {
  recorderManager = uni.getRecorderManager()
  
  recorderManager.onStart(() => {
    console.log('录音开始')
    isRecording.value = true
  })
  
  recorderManager.onStop((res) => {
    console.log('录音结束', res)
    isRecording.value = false
    recordedAudio.value = res.tempFilePath
    uni.showToast({
      title: '录音完成',
      icon: 'success'
    })
  })
  
  recorderManager.onError((err) => {
    console.error('录音失败:', err)
    isRecording.value = false
    uni.showToast({
      title: '录音失败',
      icon: 'none'
    })
  })
}

const startFollowReading = (wordInfo: CourseSectionWordInfo, accent: 'uk' | 'us') => {
  // 先播放原音
  playWordAudio(wordInfo, accent)
  
  // 延迟开始录音
  setTimeout(() => {
    startRecording()
  }, 2000)
}

const startRecording = () => {
  if (recorderManager) {
    recorderManager.start({
      duration: 10000, // 最长录音10秒
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: 'mp3'
    })
    
    uni.showToast({
      title: '开始录音，请跟读',
      icon: 'none'
    })
  }
}

const stopRecording = () => {
  if (recorderManager && isRecording.value) {
    recorderManager.stop()
  }
}

const playRecordedAudio = () => {
  if (recordedAudio.value) {
    playAudio(recordedAudio.value)
  } else {
    uni.showToast({
      title: '没有录音可播放',
      icon: 'none'
    })
  }
}

// 导航方法
const previousWord = () => {
  if (currentWordIndex.value > 0) {
    currentWordIndex.value--
  }
}

const nextWord = () => {
  if (currentWordIndex.value < totalWords.value - 1) {
    currentWordIndex.value++
  }
}

const goToWord = (index: number) => {
  if (index >= 0 && index < totalWords.value) {
    currentWordIndex.value = index
  }
}

// 触摸滑动支持
const handleTouchStart = (e: TouchEvent) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
  isSwiping.value = false
}

const handleTouchMove = (e: TouchEvent) => {
  if (!touchStartX.value || !touchStartY.value) return
  
  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY
  
  const diffX = touchStartX.value - currentX
  const diffY = touchStartY.value - currentY
  
  // 判断是否为水平滑动
  if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
    isSwiping.value = true
    e.preventDefault()
  }
}

const handleTouchEnd = (e: TouchEvent) => {
  if (!isSwiping.value || !touchStartX.value) return
  
  const endX = e.changedTouches[0].clientX
  const diffX = touchStartX.value - endX
  
  const threshold = 100
  
  if (Math.abs(diffX) > threshold) {
    if (diffX > 0) {
      nextWord()
    } else {
      previousWord()
    }
  }
  
  touchStartX.value = 0
  touchStartY.value = 0
  isSwiping.value = false
}
</script>

<style lang="scss" scoped>
/* 小程序课堂资料查看页面样式 */
.material-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);

  .navbar-content {
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;

    .navbar-left {
      width: 60px;
      display: flex;
      align-items: center;

      .icon-back {
        color: white;
        font-size: 20px;
        font-weight: bold;
      }
    }

    .navbar-title {
      flex: 1;
      text-align: center;
      color: white;
      font-size: 16px;
      font-weight: 500;
    }

    .navbar-right {
      width: 60px;
    }
  }
}

/* 录音状态指示器 */
.recording-indicator {
  position: fixed;
  top: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  background: rgba(255, 71, 87, 0.9);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;

  .recording-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 1s infinite;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 100px);
  padding: 20px;
  color: white;
  text-align: center;
  margin-top: 100px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-text {
  font-size: 16px;
  margin-bottom: 20px;
}

.retry-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid white;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 内容容器 */
.content-container {
  padding-top: 100px;
  padding-bottom: 80px;
  min-height: 100vh;
}

/* 顶部信息栏 */
.top-info {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px;
  margin: 0 12px 16px 12px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.course-info {
  .course-title {
    font-size: 20px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 12px;
    text-align: center;
    display: block;
  }

  .participants {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #7f8c8d;

    .teacher, .student {
      padding: 6px 12px;
      background: #ecf0f1;
      border-radius: 15px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
}

.progress-indicator {
  margin-top: 12px;

  .progress-text {
    font-size: 14px;
    font-weight: 500;
    color: #34495e;
    text-align: center;
    margin-bottom: 8px;
    display: block;
  }

  .progress-bar {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }
}

/* 单词容器 */
.word-container {
  padding: 0 12px;
}

.word-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

/* 单词标题区域 */
.word-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .word-title {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
  }

  .difficulty-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;

    &.easy {
      background: #d4edda;
      color: #155724;
    }

    &.medium {
      background: #fff3cd;
      color: #856404;
    }

    &.hard {
      background: #f8d7da;
      color: #721c24;
    }

    &.expert {
      background: #d1ecf1;
      color: #0c5460;
    }
  }
}

/* 音标区域 */
.phonetics-section {
  margin-bottom: 24px;

  .phonetic-item {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;

    &.playing {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      .play-icon {
        animation: bounce 0.6s ease-in-out infinite alternate;
      }
    }

    &.uk {
      border-left: 4px solid #e74c3c;
    }

    &.us {
      border-left: 4px solid #27ae60;
    }

    .phonetic-label {
      font-size: 14px;
      font-weight: 500;
      opacity: 0.8;
    }

    .phonetic-text {
      font-size: 18px;
      font-weight: bold;
      flex: 1;
      text-align: center;
    }

    .play-icon {
      font-size: 20px;
      opacity: 0.8;
    }
  }
}

@keyframes bounce {
  0% { transform: scale(1); }
  100% { transform: scale(1.2); }
}

/* 释义和例句区域 */
.meanings-section, .sentences-section, .practice-section {
  margin-bottom: 24px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    display: block;
  }
}

.meanings-list {
  .meaning-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 12px;

    .pos-tag {
      background: #667eea;
      color: white;
      padding: 4px 8px;
      border-radius: 8px;
      font-size: 12px;
      font-weight: 600;
      min-width: 32px;
      text-align: center;
      flex-shrink: 0;
    }

    .definition {
      flex: 1;
      color: #2c3e50;
      font-size: 15px;
      line-height: 1.4;
    }
  }
}

.sentences-list {
  .sentence-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;

    &.playing {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      .play-icon {
        animation: bounce 0.6s ease-in-out infinite alternate;
      }
    }

    .sentence-en {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .text {
        flex: 1;
        font-size: 15px;
        line-height: 1.4;
        font-weight: 500;
      }

      .play-icon {
        font-size: 18px;
        opacity: 0.7;
        margin-left: 8px;
      }
    }

    .sentence-cn {
      font-size: 14px;
      opacity: 0.8;
      line-height: 1.3;
    }
  }
}

/* 跟读练习区域 */
.practice-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;

  button {
    background: #f8f9fa;
    border: none;
    border-radius: 16px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;

    .icon {
      font-size: 20px;
    }

    .text {
      flex: 1;
      text-align: center;
    }
  }

  .practice-btn {
    background: linear-gradient(135deg, #2ed573, #17c0eb);
    color: white;

    &.recording {
      background: linear-gradient(135deg, #ff4757, #ff6b7a);
      animation: pulse 1s infinite;
    }

    &:disabled {
      opacity: 0.6;
    }
  }

  .stop-btn {
    background: linear-gradient(135deg, #ff4757, #ff6b7a);
    color: white;
  }

  .playback-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
  }
}

/* 底部导航 */
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .nav-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 80px;

    &.disabled {
      opacity: 0.4;
    }

    &.prev {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;

      &.disabled {
        background: #f8f9fa;
        color: #adb5bd;
      }
    }

    &.next {
      background: linear-gradient(135deg, #2ed573, #17c0eb);
      color: white;

      &.disabled {
        background: #f8f9fa;
        color: #adb5bd;
      }
    }

    .icon {
      font-size: 16px;
    }

    .text {
      font-size: 12px;
    }
  }

  .word-dots {
    display: flex;
    gap: 8px;
    align-items: center;

    .dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #dee2e6;
      transition: all 0.3s ease;

      &.active {
        background: #667eea;
        transform: scale(1.5);
      }
    }
  }
}

/* 安全区域适配 */
.bottom-navigation {
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}
</style>

package org.nonamespace.word.rest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.course.CourseConsumptionDashboardDto;
import org.nonamespace.word.server.facade.ICourseConsumptionDashboardFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * 课消看板Controller
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@RestController
@RequestMapping("/course-consumption/dashboard")
@RequiredArgsConstructor
public class CourseConsumptionDashboardController extends BaseController {

    private final ICourseConsumptionDashboardFacade courseConsumptionDashboardFacade;

    /**
     * 获取课消看板数据
     */
    @PreAuthorize("@ss.hasPermi('course:consumption:dashboard:view')")
    @Log(title = "课消看板", businessType = BusinessType.OTHER)
    @PostMapping("/data")
    public AjaxResult getDashboardData(@Validated @RequestBody CourseConsumptionDashboardDto.GetDashboardReq req) {
        try {
            log.info("获取课消看板数据，请求参数: {}", req);

            // 处理时间范围快捷选择
            processTimeRangeShortcuts(req);

            CourseConsumptionDashboardDto.DashboardResp response =
                    courseConsumptionDashboardFacade.getDashboardData(req);

            log.info("课消看板数据获取成功，学生数: {}, 总课消: {}",
                    response.getStudentStats().size(),
                    response.getOverallStats().getTotalConsumption());

            return success(response);
        } catch (Exception e) {
            log.error("获取课消看板数据失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取快捷时间范围选项
     */
    @PreAuthorize("@ss.hasPermi('course:consumption:dashboard:view')")
    @GetMapping("/time-ranges")
    public AjaxResult getTimeRanges() {
        try {
            log.info("获取快捷时间范围选项");

            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

            // 本周（周一到周日）
            LocalDate thisWeekStart = today.with(java.time.DayOfWeek.MONDAY);
            LocalDate thisWeekEnd = today.with(java.time.DayOfWeek.SUNDAY);

            // 上周
            LocalDate lastWeekStart = thisWeekStart.minus(1, ChronoUnit.WEEKS);
            LocalDate lastWeekEnd = thisWeekEnd.minus(1, ChronoUnit.WEEKS);

            // 本月
            LocalDate thisMonthStart = today.withDayOfMonth(1);
            LocalDate thisMonthEnd = today.withDayOfMonth(today.lengthOfMonth());

            // 上月
            LocalDate lastMonthStart = thisMonthStart.minus(1, ChronoUnit.MONTHS);
            LocalDate lastMonthEnd = lastMonthStart.withDayOfMonth(lastMonthStart.lengthOfMonth());

            return AjaxResult.success()
                    .put("thisWeek", new TimeRange("thisWeek", "本周",
                            thisWeekStart.format(formatter), thisWeekEnd.format(formatter)))
                    .put("lastWeek", new TimeRange("lastWeek", "上周",
                            lastWeekStart.format(formatter), lastWeekEnd.format(formatter)))
                    .put("thisMonth", new TimeRange("thisMonth", "本月",
                            thisMonthStart.format(formatter), thisMonthEnd.format(formatter)))
                    .put("lastMonth", new TimeRange("lastMonth", "上月",
                            lastMonthStart.format(formatter), lastMonthEnd.format(formatter)));
        } catch (Exception e) {
            log.error("获取时间范围选项失败", e);
            return error("获取时间范围失败: " + e.getMessage());
        }
    }

    /**
     * 处理时间范围快捷选择
     */
    private void processTimeRangeShortcuts(CourseConsumptionDashboardDto.GetDashboardReq req) {
        if (req.getTimeRangeType() == null || req.getTimeRangeType().isEmpty()) {
            return;
        }
        
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        switch (req.getTimeRangeType()) {
            case "thisWeek":
                LocalDate thisWeekStart = today.with(java.time.DayOfWeek.MONDAY);
                LocalDate thisWeekEnd = today.with(java.time.DayOfWeek.SUNDAY);
                req.setStartDate(thisWeekStart.format(formatter));
                req.setEndDate(thisWeekEnd.format(formatter));
                break;
                
            case "lastWeek":
                LocalDate lastWeekStart = today.with(java.time.DayOfWeek.MONDAY).minus(1, ChronoUnit.WEEKS);
                LocalDate lastWeekEnd = today.with(java.time.DayOfWeek.SUNDAY).minus(1, ChronoUnit.WEEKS);
                req.setStartDate(lastWeekStart.format(formatter));
                req.setEndDate(lastWeekEnd.format(formatter));
                break;
                
            case "thisMonth":
                LocalDate thisMonthStart = today.withDayOfMonth(1);
                LocalDate thisMonthEnd = today.withDayOfMonth(today.lengthOfMonth());
                req.setStartDate(thisMonthStart.format(formatter));
                req.setEndDate(thisMonthEnd.format(formatter));
                break;
                
            case "lastMonth":
                LocalDate lastMonthStart = today.withDayOfMonth(1).minus(1, ChronoUnit.MONTHS);
                LocalDate lastMonthEnd = lastMonthStart.withDayOfMonth(lastMonthStart.lengthOfMonth());
                req.setStartDate(lastMonthStart.format(formatter));
                req.setEndDate(lastMonthEnd.format(formatter));
                break;
                
            default:
                // 自定义时间范围，不做处理
                break;
        }
    }

    /**
     * 时间范围内部类
     */
    public static class TimeRange {
        private String type;
        private String label;
        private String startDate;
        private String endDate;
        
        public TimeRange(String type, String label, String startDate, String endDate) {
            this.type = type;
            this.label = label;
            this.startDate = startDate;
            this.endDate = endDate;
        }
        
        // Getters
        public String getType() { return type; }
        public String getLabel() { return label; }
        public String getStartDate() { return startDate; }
        public String getEndDate() { return endDate; }
    }
}

package org.nonamespace.word.rest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.dto.dashboard.CourseConsumptionRoleDashboardDto;
import org.nonamespace.word.server.facade.ICourseConsumptionRoleDashboardFacade;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 分角色课消看板Controller
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@RestController
@RequestMapping("/course-consumption-role-dashboard")
@RequiredArgsConstructor
public class CourseConsumptionRoleDashboardController extends BaseController {

    private final ICourseConsumptionRoleDashboardFacade courseConsumptionRoleDashboardFacade;

    /**
     * 获取老师看板数据
     */
    @PreAuthorize("@ss.hasRole('teacher') or @ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "老师课消看板", businessType = BusinessType.OTHER)
    @PostMapping("/teacher/data")
    public AjaxResult getTeacherDashboardData(@Validated @RequestBody CourseConsumptionRoleDashboardDto.TeacherDashboardRequest req) {
        try {
            log.info("获取老师看板数据，请求参数: {}", req);

            CourseConsumptionRoleDashboardDto.TeacherDashboardResponse response =
                    courseConsumptionRoleDashboardFacade.getTeacherDashboardData(req);

            log.info("老师看板数据获取成功，学生数: {}", 
                    response.getStudentStats() != null ? response.getStudentStats().size() : 0);

            return success(response);
        } catch (Exception e) {
            log.error("获取老师看板数据失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取组长看板数据
     */
    @PreAuthorize("@ss.hasRole('teaching_group_leader') or @ss.hasRole('teaching_group_admin') or @ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "组长课消看板", businessType = BusinessType.OTHER)
    @PostMapping("/group-leader/data")
    public AjaxResult getGroupLeaderDashboardData(@Validated @RequestBody CourseConsumptionRoleDashboardDto.GroupLeaderDashboardRequest req) {
        try {
            log.info("获取组长看板数据，请求参数: {}", req);

            CourseConsumptionRoleDashboardDto.GroupLeaderDashboardResponse response =
                    courseConsumptionRoleDashboardFacade.getGroupLeaderDashboardData(req);

            log.info("组长看板数据获取成功");

            return success(response);
        } catch (Exception e) {
            log.error("获取组长看板数据失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取管理看板数据
     */
    @PreAuthorize("@ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "管理课消看板", businessType = BusinessType.OTHER)
    @PostMapping("/admin/data")
    public AjaxResult getAdminDashboardData(@Validated @RequestBody CourseConsumptionRoleDashboardDto.AdminDashboardRequest req) {
        try {
            log.info("获取管理看板数据，请求参数: {}", req);

            CourseConsumptionRoleDashboardDto.AdminDashboardResponse response =
                    courseConsumptionRoleDashboardFacade.getAdminDashboardData(req);

            log.info("管理看板数据获取成功");

            return success(response);
        } catch (Exception e) {
            log.error("获取管理看板数据失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取老师选择器选项 (组长使用)
     */
    @PreAuthorize("@ss.hasRole('teaching_group_leader') or @ss.hasRole('teaching_group_admin') or @ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "获取老师选择器选项", businessType = BusinessType.OTHER)
    @GetMapping("/teacher-selector")
    public AjaxResult getTeacherSelectorOptions() {
        try {
            log.info("获取老师选择器选项");

            CourseConsumptionRoleDashboardDto.TeacherSelectorResponse response =
                    courseConsumptionRoleDashboardFacade.getTeacherSelectorOptions();

            log.info("老师选择器选项获取成功，老师数: {}", 
                    response.getTeachers() != null ? response.getTeachers().size() : 0);

            return success(response);
        } catch (Exception e) {
            log.error("获取老师选择器选项失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取组选择器选项 (管理使用)
     */
    @PreAuthorize("@ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "获取组选择器选项", businessType = BusinessType.OTHER)
    @GetMapping("/group-selector")
    public AjaxResult getGroupSelectorOptions() {
        try {
            log.info("获取组选择器选项");

            CourseConsumptionRoleDashboardDto.GroupSelectorResponse response =
                    courseConsumptionRoleDashboardFacade.getGroupSelectorOptions();

            log.info("组选择器选项获取成功，组数: {}", 
                    response.getGroups() != null ? response.getGroups().size() : 0);

            return success(response);
        } catch (Exception e) {
            log.error("获取组选择器选项失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定组的老师选择器选项 (管理使用)
     */
    @PreAuthorize("@ss.hasRole('admin') or @ss.hasRole('hr')")
    @Log(title = "获取指定组的老师选择器选项", businessType = BusinessType.OTHER)
    @GetMapping("/group/{groupId}/teachers")
    public AjaxResult getTeacherSelectorOptionsByGroup(@PathVariable String groupId) {
        try {
            log.info("获取指定组的老师选择器选项，组ID: {}", groupId);

            CourseConsumptionRoleDashboardDto.TeacherSelectorResponse response =
                    courseConsumptionRoleDashboardFacade.getTeacherSelectorOptionsByGroup(groupId);

            log.info("指定组的老师选择器选项获取成功，老师数: {}", 
                    response.getTeachers() != null ? response.getTeachers().size() : 0);

            return success(response);
        } catch (Exception e) {
            log.error("获取指定组的老师选择器选项失败", e);
            return error("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 处理时间范围快捷选择
     */
    private void processTimeRangeShortcuts(CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {
        // 这个方法在Facade中已经实现，这里保留作为备用
        // 如果需要在Controller层做额外处理，可以在这里实现
    }
}

package org.nonamespace.word.server.dto.dashboard;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 分角色课消看板DTO
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public class CourseConsumptionRoleDashboardDto {

    /**
     * 通用请求参数基类
     */
    @Data
    public static class BaseDashboardRequest {
        /**
         * 开始日期 (YYYY-MM-DD)
         */
        @NotBlank(message = "开始日期不能为空")
        @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "日期格式错误")
        private String startDate;

        /**
         * 结束日期 (YYYY-MM-DD)
         */
        @NotBlank(message = "结束日期不能为空")
        @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "日期格式错误")
        private String endDate;

        /**
         * 时间范围类型 (thisWeek, lastWeek, thisMonth, lastMonth, custom)
         */
        private String timeRangeType = "thisWeek";

        /**
         * 学科过滤
         */
        private String subject;
    }

    /**
     * 老师看板请求
     */
    @Data
    public static class TeacherDashboardRequest extends BaseDashboardRequest {
        /**
         * 老师ID (可选，默认当前用户)
         */
        private String teacherId;
    }

    /**
     * 组长看板请求
     */
    @Data
    public static class GroupLeaderDashboardRequest extends BaseDashboardRequest {
        /**
         * 教学组ID (可选，默认当前用户所在组)
         */
        private String groupId;

        /**
         * 老师ID (可选，查看特定老师)
         */
        private String teacherId;
    }

    /**
     * 管理看板请求
     */
    @Data
    public static class AdminDashboardRequest extends BaseDashboardRequest {
        /**
         * 教学组ID (可选，查看特定组)
         */
        private String groupId;

        /**
         * 老师ID (可选，查看特定老师)
         */
        private String teacherId;
    }

    /**
     * 时间范围信息
     */
    @Data
    public static class TimeRangeInfo {
        /**
         * 开始日期
         */
        private String startDate;

        /**
         * 结束日期
         */
        private String endDate;

        /**
         * 时间范围类型
         */
        private String timeRangeType;

        /**
         * 时间范围描述
         */
        private String description;

        /**
         * 总天数
         */
        private Integer totalDays;

        /**
         * 总周数
         */
        private Integer totalWeeks;

        /**
         * 总月数
         */
        private Integer totalMonths;
    }

    /**
     * 老师看板响应
     */
    @Data
    public static class TeacherDashboardResponse {
        /**
         * 时间范围信息
         */
        private TimeRangeInfo timeRangeInfo;

        /**
         * 老师总体统计
         */
        private TeacherOverallStats overallStats;

        /**
         * 学生课消统计列表
         */
        private List<StudentConsumptionStats> studentStats;

        /**
         * 周课消趋势数据
         */
        private List<WeeklyTrendData> weeklyTrends;
    }

    /**
     * 组长看板响应
     */
    @Data
    public static class GroupLeaderDashboardResponse {
        /**
         * 时间范围信息
         */
        private TimeRangeInfo timeRangeInfo;

        /**
         * 组总体统计
         */
        private GroupOverallStats groupStats;

        /**
         * 老师课消统计列表
         */
        private List<TeacherConsumptionStats> teacherStats;

        /**
         * 学生课消统计列表 (当选择特定老师时)
         */
        private List<StudentConsumptionStats> studentStats;

        /**
         * 周课消趋势数据
         */
        private List<WeeklyTrendData> weeklyTrends;

        /**
         * 当前查看的老师信息
         */
        private TeacherInfo currentTeacher;
    }

    /**
     * 管理看板响应
     */
    @Data
    public static class AdminDashboardResponse {
        /**
         * 时间范围信息
         */
        private TimeRangeInfo timeRangeInfo;

        /**
         * 管理总体统计
         */
        private AdminOverallStats overallStats;

        /**
         * 组课消统计列表
         */
        private List<GroupConsumptionStats> groupStats;

        /**
         * 老师课消统计列表 (当选择特定组时)
         */
        private List<TeacherConsumptionStats> teacherStats;

        /**
         * 学生课消统计列表 (当选择特定老师时)
         */
        private List<StudentConsumptionStats> studentStats;

        /**
         * 周课消趋势数据
         */
        private List<WeeklyTrendData> weeklyTrends;

        /**
         * 当前查看的组信息
         */
        private GroupInfo currentGroup;

        /**
         * 当前查看的老师信息
         */
        private TeacherInfo currentTeacher;
    }

    /**
     * 老师总体统计
     */
    @Data
    public static class TeacherOverallStats {
        /**
         * 总学生数
         */
        private Long totalStudents;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 平均周课消
         */
        private BigDecimal avgWeeklyConsumption;

        /**
         * 平均月课消
         */
        private BigDecimal avgMonthlyConsumption;

        /**
         * 课消率
         */
        private BigDecimal consumptionRate;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;
    }

    /**
     * 组总体统计
     */
    @Data
    public static class GroupOverallStats {
        /**
         * 组名称
         */
        private String groupName;

        /**
         * 总老师数
         */
        private Long totalTeachers;

        /**
         * 活跃老师数
         */
        private Long activeTeachers;

        /**
         * 总学生数
         */
        private Long totalStudents;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 平均周课消
         */
        private BigDecimal avgWeeklyConsumption;

        /**
         * 师均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerTeacher;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;
    }

    /**
     * 管理总体统计
     */
    @Data
    public static class AdminOverallStats {
        /**
         * 总组数
         */
        private Long totalGroups;

        /**
         * 活跃组数
         */
        private Long activeGroups;

        /**
         * 总老师数
         */
        private Long totalTeachers;

        /**
         * 活跃老师数
         */
        private Long activeTeachers;

        /**
         * 总学生数
         */
        private Long totalStudents;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 平均周课消
         */
        private BigDecimal avgWeeklyConsumption;

        /**
         * 组均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerGroup;

        /**
         * 师均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerTeacher;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;
    }

    /**
     * 学生课消统计
     */
    @Data
    public static class StudentConsumptionStats {
        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 周课消课时
         */
        private BigDecimal weeklyConsumption;

        /**
         * 月课消课时
         */
        private BigDecimal monthlyConsumption;

        /**
         * 课消次数
         */
        private Long consumptionCount;

        /**
         * 最后课消时间
         */
        private Date lastConsumptionTime;

        /**
         * 学科分布
         */
        private List<SubjectConsumption> subjectConsumptions;
    }

    /**
     * 老师课消统计
     */
    @Data
    public static class TeacherConsumptionStats {
        /**
         * 老师ID
         */
        private String teacherId;

        /**
         * 老师姓名
         */
        private String teacherName;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 周课消课时
         */
        private BigDecimal weeklyConsumption;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;

        /**
         * 课消率
         */
        private BigDecimal consumptionRate;

        /**
         * 最后课消时间
         */
        private Date lastConsumptionTime;
    }

    /**
     * 组课消统计
     */
    @Data
    public static class GroupConsumptionStats {
        /**
         * 组ID
         */
        private String groupId;

        /**
         * 组名称
         */
        private String groupName;

        /**
         * 组长姓名
         */
        private String leaderName;

        /**
         * 老师数量
         */
        private Long teacherCount;

        /**
         * 活跃老师数
         */
        private Long activeTeachers;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 周课消课时
         */
        private BigDecimal weeklyConsumption;

        /**
         * 师均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerTeacher;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;

        /**
         * 课消率
         */
        private BigDecimal consumptionRate;
    }

    /**
     * 周课消趋势数据
     */
    @Data
    public static class WeeklyTrendData {
        /**
         * 周开始日期
         */
        private String weekStart;

        /**
         * 周结束日期
         */
        private String weekEnd;

        /**
         * 周描述 (如: 第1周)
         */
        private String weekLabel;

        /**
         * 课消课时
         */
        private BigDecimal consumption;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 课消次数
         */
        private Long consumptionCount;
    }

    /**
     * 学科课消分布
     */
    @Data
    public static class SubjectConsumption {
        /**
         * 学科
         */
        private String subject;

        /**
         * 课消课时
         */
        private BigDecimal consumption;

        /**
         * 课消次数
         */
        private Long count;

        /**
         * 占比
         */
        private BigDecimal percentage;
    }

    /**
     * 老师信息
     */
    @Data
    public static class TeacherInfo {
        /**
         * 老师ID
         */
        private String teacherId;

        /**
         * 老师姓名
         */
        private String teacherName;

        /**
         * 所属组ID
         */
        private String groupId;

        /**
         * 所属组名称
         */
        private String groupName;
    }

    /**
     * 组信息
     */
    @Data
    public static class GroupInfo {
        /**
         * 组ID
         */
        private String groupId;

        /**
         * 组名称
         */
        private String groupName;

        /**
         * 组长ID
         */
        private String leaderId;

        /**
         * 组长姓名
         */
        private String leaderName;
    }

    /**
     * 选择器选项
     */
    @Data
    public static class SelectorOption {
        /**
         * 值
         */
        private String value;

        /**
         * 标签
         */
        private String label;

        /**
         * 是否禁用
         */
        private Boolean disabled = false;
    }

    /**
     * 老师选择器响应
     */
    @Data
    public static class TeacherSelectorResponse {
        /**
         * 老师选项列表
         */
        private List<SelectorOption> teachers;
    }

    /**
     * 组选择器响应
     */
    @Data
    public static class GroupSelectorResponse {
        /**
         * 组选项列表
         */
        private List<SelectorOption> groups;
    }
}

package org.nonamespace.word.server.facade.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.dashboard.CourseConsumptionRoleDashboardDto;
import org.nonamespace.word.server.facade.ICourseConsumptionRoleDashboardFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分角色课消看板Facade实现类
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseConsumptionRoleDashboardFacadeImpl implements ICourseConsumptionRoleDashboardFacade {

    private final IStudentCourseConsumptionService studentCourseConsumptionService;
    private final UserStudentExtService userStudentExtService;
    private final ITeacherProfileService teacherProfileService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final SystemDataQueryUtil systemDataQueryUtil;

    @Override
    public CourseConsumptionRoleDashboardDto.TeacherDashboardResponse getTeacherDashboardData(
            CourseConsumptionRoleDashboardDto.TeacherDashboardRequest req) {
        
        log.info("获取老师看板数据，请求参数: {}", req);
        
        // 应用权限控制
        applyTeacherPermissions(req);
        
        // 构建时间范围信息
        CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo = buildTimeRangeInfo(req);
        
        // 获取课消数据
        List<StudentCourseConsumption> consumptions = getConsumptionsByTeacher(req);
        
        // 构建响应
        CourseConsumptionRoleDashboardDto.TeacherDashboardResponse response = 
                new CourseConsumptionRoleDashboardDto.TeacherDashboardResponse();
        response.setTimeRangeInfo(timeRangeInfo);
        response.setOverallStats(calculateTeacherOverallStats(consumptions, timeRangeInfo));
        response.setStudentStats(calculateStudentStats(consumptions, timeRangeInfo));
        response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
        
        log.info("老师看板数据获取成功，学生数: {}", response.getStudentStats().size());
        return response;
    }

    @Override
    public CourseConsumptionRoleDashboardDto.GroupLeaderDashboardResponse getGroupLeaderDashboardData(
            CourseConsumptionRoleDashboardDto.GroupLeaderDashboardRequest req) {
        
        log.info("获取组长看板数据，请求参数: {}", req);
        
        // 应用权限控制
        applyGroupLeaderPermissions(req);
        
        // 构建时间范围信息
        CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo = buildTimeRangeInfo(req);
        
        // 构建响应
        CourseConsumptionRoleDashboardDto.GroupLeaderDashboardResponse response = 
                new CourseConsumptionRoleDashboardDto.GroupLeaderDashboardResponse();
        response.setTimeRangeInfo(timeRangeInfo);
        
        if (StrUtil.isNotBlank(req.getTeacherId())) {
            // 查看特定老师的学生数据
            List<StudentCourseConsumption> consumptions = getConsumptionsByTeacher(req.getTeacherId(), req);
            response.setStudentStats(calculateStudentStats(consumptions, timeRangeInfo));
            response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
            response.setCurrentTeacher(getTeacherInfo(req.getTeacherId()));
        } else {
            // 查看组内所有老师数据
            List<StudentCourseConsumption> consumptions = getConsumptionsByGroup(req);
            response.setGroupStats(calculateGroupOverallStats(consumptions, timeRangeInfo, req.getGroupId()));
            response.setTeacherStats(calculateTeacherStatsInGroup(consumptions, timeRangeInfo, req.getGroupId()));
            response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
        }
        
        log.info("组长看板数据获取成功");
        return response;
    }

    @Override
    public CourseConsumptionRoleDashboardDto.AdminDashboardResponse getAdminDashboardData(
            CourseConsumptionRoleDashboardDto.AdminDashboardRequest req) {
        
        log.info("获取管理看板数据，请求参数: {}", req);
        
        // 构建时间范围信息
        CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo = buildTimeRangeInfo(req);
        
        // 构建响应
        CourseConsumptionRoleDashboardDto.AdminDashboardResponse response = 
                new CourseConsumptionRoleDashboardDto.AdminDashboardResponse();
        response.setTimeRangeInfo(timeRangeInfo);
        
        if (StrUtil.isNotBlank(req.getTeacherId())) {
            // 查看特定老师的学生数据
            List<StudentCourseConsumption> consumptions = getConsumptionsByTeacher(req.getTeacherId(), req);
            response.setStudentStats(calculateStudentStats(consumptions, timeRangeInfo));
            response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
            response.setCurrentTeacher(getTeacherInfo(req.getTeacherId()));
        } else if (StrUtil.isNotBlank(req.getGroupId())) {
            // 查看特定组的老师数据
            List<StudentCourseConsumption> consumptions = getConsumptionsByGroup(req.getGroupId(), req);
            response.setTeacherStats(calculateTeacherStatsInGroup(consumptions, timeRangeInfo, req.getGroupId()));
            response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
            response.setCurrentGroup(getGroupInfo(req.getGroupId()));
        } else {
            // 查看所有组数据
            List<StudentCourseConsumption> consumptions = getAllConsumptions(req);
            response.setOverallStats(calculateAdminOverallStats(consumptions, timeRangeInfo));
            response.setGroupStats(calculateGroupStats(consumptions, timeRangeInfo));
            response.setWeeklyTrends(calculateWeeklyTrends(consumptions, timeRangeInfo));
        }
        
        log.info("管理看板数据获取成功");
        return response;
    }

    @Override
    public CourseConsumptionRoleDashboardDto.TeacherSelectorResponse getTeacherSelectorOptions() {
        String currentUserId = SecurityUtils.getUserId().toString();
        
        // 获取当前用户管理的教学组
        List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, currentUserId)
                        .or()
                        .eq(TeachingGroup::getAdminId, currentUserId))
                .eq(TeachingGroup::getDeleted, false)
                .eq(TeachingGroup::getStatus, "active")
                .list();
        
        if (managedGroups.isEmpty()) {
            return new CourseConsumptionRoleDashboardDto.TeacherSelectorResponse();
        }
        
        // 获取组内老师
        List<String> groupIds = managedGroups.stream()
                .map(TeachingGroup::getId)
                .collect(Collectors.toList());
        
        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .in(TeachingGroupMember::getGroupId, groupIds)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();
        
        List<String> teacherIds = members.stream()
                .map(TeachingGroupMember::getTeacherId)
                .collect(Collectors.toList());
        
        if (teacherIds.isEmpty()) {
            return new CourseConsumptionRoleDashboardDto.TeacherSelectorResponse();
        }
        
        // 获取老师信息
        List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                .in(TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .eq(TeacherProfile::getStatus, "active")
                .list();
        
        List<CourseConsumptionRoleDashboardDto.SelectorOption> options = teachers.stream()
                .map(teacher -> {
                    CourseConsumptionRoleDashboardDto.SelectorOption option = 
                            new CourseConsumptionRoleDashboardDto.SelectorOption();
                    option.setValue(teacher.getTeacherId());
                    option.setLabel(teacher.getNickName());
                    return option;
                })
                .collect(Collectors.toList());
        
        CourseConsumptionRoleDashboardDto.TeacherSelectorResponse response = 
                new CourseConsumptionRoleDashboardDto.TeacherSelectorResponse();
        response.setTeachers(options);
        return response;
    }

    @Override
    public CourseConsumptionRoleDashboardDto.GroupSelectorResponse getGroupSelectorOptions() {
        // 管理员可以查看所有组
        List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                .eq(TeachingGroup::getDeleted, false)
                .eq(TeachingGroup::getStatus, "active")
                .orderByAsc(TeachingGroup::getName)
                .list();
        
        List<CourseConsumptionRoleDashboardDto.SelectorOption> options = groups.stream()
                .map(group -> {
                    CourseConsumptionRoleDashboardDto.SelectorOption option = 
                            new CourseConsumptionRoleDashboardDto.SelectorOption();
                    option.setValue(group.getId());
                    option.setLabel(group.getName());
                    return option;
                })
                .collect(Collectors.toList());
        
        CourseConsumptionRoleDashboardDto.GroupSelectorResponse response = 
                new CourseConsumptionRoleDashboardDto.GroupSelectorResponse();
        response.setGroups(options);
        return response;
    }

    @Override
    public CourseConsumptionRoleDashboardDto.TeacherSelectorResponse getTeacherSelectorOptionsByGroup(String groupId) {
        // 获取指定组的老师
        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, groupId)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();
        
        List<String> teacherIds = members.stream()
                .map(TeachingGroupMember::getTeacherId)
                .collect(Collectors.toList());
        
        if (teacherIds.isEmpty()) {
            return new CourseConsumptionRoleDashboardDto.TeacherSelectorResponse();
        }
        
        // 获取老师信息
        List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                .in(TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .eq(TeacherProfile::getStatus, "active")
                .list();
        
        List<CourseConsumptionRoleDashboardDto.SelectorOption> options = teachers.stream()
                .map(teacher -> {
                    CourseConsumptionRoleDashboardDto.SelectorOption option = 
                            new CourseConsumptionRoleDashboardDto.SelectorOption();
                    option.setValue(teacher.getTeacherId());
                    option.setLabel(teacher.getNickName());
                    return option;
                })
                .collect(Collectors.toList());
        
        CourseConsumptionRoleDashboardDto.TeacherSelectorResponse response = 
                new CourseConsumptionRoleDashboardDto.TeacherSelectorResponse();
        response.setTeachers(options);
        return response;
    }

    /**
     * 应用老师权限控制
     */
    private void applyTeacherPermissions(CourseConsumptionRoleDashboardDto.TeacherDashboardRequest req) {
        String currentUserId = SecurityUtils.getUserId().toString();
        
        if (!systemDataQueryUtil.isAdminOrHr()) {
            // 非管理员只能查看自己的数据
            req.setTeacherId(currentUserId);
        } else if (StrUtil.isBlank(req.getTeacherId())) {
            // 管理员未指定老师时，默认查看自己的数据
            req.setTeacherId(currentUserId);
        }
    }

    /**
     * 应用组长权限控制
     */
    private void applyGroupLeaderPermissions(CourseConsumptionRoleDashboardDto.GroupLeaderDashboardRequest req) {
        String currentUserId = SecurityUtils.getUserId().toString();
        
        if (!systemDataQueryUtil.isAdminOrHr()) {
            // 获取当前用户管理的教学组
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getAdminId, currentUserId))
                    .eq(TeachingGroup::getDeleted, false)
                    .list();
            
            if (managedGroups.isEmpty()) {
                throw new RuntimeException("您不是任何教学组的管理员");
            }
            
            // 如果未指定组ID，使用第一个管理的组
            if (StrUtil.isBlank(req.getGroupId())) {
                req.setGroupId(managedGroups.get(0).getId());
            } else {
                // 验证是否有权限查看指定的组
                boolean hasPermission = managedGroups.stream()
                        .anyMatch(group -> group.getId().equals(req.getGroupId()));
                if (!hasPermission) {
                    throw new RuntimeException("您没有权限查看该教学组的数据");
                }
            }
        }
    }

    /**
     * 构建时间范围信息
     */
    private CourseConsumptionRoleDashboardDto.TimeRangeInfo buildTimeRangeInfo(
            CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {
        
        CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo = 
                new CourseConsumptionRoleDashboardDto.TimeRangeInfo();
        
        // 处理时间范围快捷选择
        processTimeRangeShortcuts(req);
        
        timeRangeInfo.setStartDate(req.getStartDate());
        timeRangeInfo.setEndDate(req.getEndDate());
        timeRangeInfo.setTimeRangeType(req.getTimeRangeType());
        
        // 计算时间范围描述和统计信息
        LocalDate startDate = LocalDate.parse(req.getStartDate());
        LocalDate endDate = LocalDate.parse(req.getEndDate());
        
        long totalDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        int totalWeeks = (int) Math.ceil(totalDays / 7.0);
        int totalMonths = (int) Math.ceil(totalDays / 30.0);
        
        timeRangeInfo.setTotalDays((int) totalDays);
        timeRangeInfo.setTotalWeeks(totalWeeks);
        timeRangeInfo.setTotalMonths(totalMonths);
        
        // 设置描述
        String description = getTimeRangeDescription(req.getTimeRangeType(), startDate, endDate);
        timeRangeInfo.setDescription(description);
        
        return timeRangeInfo;
    }

    /**
     * 处理时间范围快捷选择
     */
    private void processTimeRangeShortcuts(CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {
        LocalDate now = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;
        
        switch (req.getTimeRangeType()) {
            case "thisWeek":
                startDate = now.with(java.time.DayOfWeek.MONDAY);
                endDate = now;
                break;
            case "lastWeek":
                startDate = now.minusWeeks(1).with(java.time.DayOfWeek.MONDAY);
                endDate = now.minusWeeks(1).with(java.time.DayOfWeek.SUNDAY);
                break;
            case "thisMonth":
                startDate = now.withDayOfMonth(1);
                endDate = now;
                break;
            case "lastMonth":
                startDate = now.minusMonths(1).withDayOfMonth(1);
                endDate = now.minusMonths(1).with(java.time.temporal.TemporalAdjusters.lastDayOfMonth());
                break;
            default:
                // custom 或其他情况，使用传入的日期
                return;
        }
        
        req.setStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        req.setEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }

    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(String timeRangeType, LocalDate startDate, LocalDate endDate) {
        switch (timeRangeType) {
            case "thisWeek":
                return "本周";
            case "lastWeek":
                return "上周";
            case "thisMonth":
                return "本月";
            case "lastMonth":
                return "上月";
            default:
                return startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + 
                       " 至 " + 
                       endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
    }

    /**
     * 获取老师的课消数据
     */
    private List<StudentCourseConsumption> getConsumptionsByTeacher(
            CourseConsumptionRoleDashboardDto.TeacherDashboardRequest req) {
        return getConsumptionsByTeacher(req.getTeacherId(), req);
    }

    /**
     * 获取指定老师的课消数据
     */
    private List<StudentCourseConsumption> getConsumptionsByTeacher(String teacherId,
            CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {
        var queryWrapper = studentCourseConsumptionService.lambdaQuery()
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .eq(StudentCourseConsumption::getTeacherId, teacherId)
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        if (StrUtil.isNotBlank(req.getSubject())) {
            queryWrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }

        return queryWrapper.list();
    }

    /**
     * 获取组的课消数据
     */
    private List<StudentCourseConsumption> getConsumptionsByGroup(
            CourseConsumptionRoleDashboardDto.GroupLeaderDashboardRequest req) {
        return getConsumptionsByGroup(req.getGroupId(), req);
    }

    /**
     * 获取指定组的课消数据
     */
    private List<StudentCourseConsumption> getConsumptionsByGroup(String groupId,
            CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {

        // 获取组内老师ID列表
        List<String> teacherIds = getTeacherIdsByGroup(groupId);

        if (teacherIds.isEmpty()) {
            return new ArrayList<>();
        }

        var queryWrapper = studentCourseConsumptionService.lambdaQuery()
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .in(StudentCourseConsumption::getTeacherId, teacherIds)
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        if (StrUtil.isNotBlank(req.getSubject())) {
            queryWrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }

        return queryWrapper.list();
    }

    /**
     * 获取所有课消数据
     */
    private List<StudentCourseConsumption> getAllConsumptions(
            CourseConsumptionRoleDashboardDto.BaseDashboardRequest req) {

        var queryWrapper = studentCourseConsumptionService.lambdaQuery()
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        if (StrUtil.isNotBlank(req.getSubject())) {
            queryWrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }

        return queryWrapper.list();
    }

    /**
     * 获取组内老师ID列表
     */
    private List<String> getTeacherIdsByGroup(String groupId) {
        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, groupId)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        return members.stream()
                .map(TeachingGroupMember::getTeacherId)
                .collect(Collectors.toList());
    }

    /**
     * 计算老师总体统计
     */
    private CourseConsumptionRoleDashboardDto.TeacherOverallStats calculateTeacherOverallStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo) {

        CourseConsumptionRoleDashboardDto.TeacherOverallStats stats =
                new CourseConsumptionRoleDashboardDto.TeacherOverallStats();

        if (consumptions.isEmpty()) {
            stats.setTotalStudents(0L);
            stats.setActiveStudents(0L);
            stats.setTotalConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
            stats.setAvgMonthlyConsumption(BigDecimal.ZERO);
            stats.setConsumptionRate(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
            return stats;
        }

        // 计算基础统计
        Set<String> allStudents = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        BigDecimal totalConsumption = consumptions.stream()
                .map(StudentCourseConsumption::getConsumedHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.setTotalStudents((long) allStudents.size());
        stats.setActiveStudents((long) allStudents.size());
        stats.setTotalConsumption(totalConsumption);

        // 计算平均值
        if (timeRangeInfo.getTotalWeeks() > 0) {
            BigDecimal avgWeekly = totalConsumption.divide(
                    BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
            stats.setAvgWeeklyConsumption(avgWeekly);

            if (allStudents.size() > 0) {
                BigDecimal avgWeeklyPerStudent = avgWeekly.divide(
                        BigDecimal.valueOf(allStudents.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);
            }
        }

        if (timeRangeInfo.getTotalMonths() > 0) {
            BigDecimal avgMonthly = totalConsumption.divide(
                    BigDecimal.valueOf(timeRangeInfo.getTotalMonths()), 2, RoundingMode.HALF_UP);
            stats.setAvgMonthlyConsumption(avgMonthly);
        }

        stats.setConsumptionRate(BigDecimal.valueOf(100)); // 有课消的学生占比100%

        return stats;
    }

    /**
     * 计算学生统计
     */
    private List<CourseConsumptionRoleDashboardDto.StudentConsumptionStats> calculateStudentStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo) {

        if (consumptions.isEmpty()) {
            return new ArrayList<>();
        }

        // 按学生分组
        Map<String, List<StudentCourseConsumption>> studentConsumptionMap = consumptions.stream()
                .collect(Collectors.groupingBy(StudentCourseConsumption::getStudentId));

        // 获取学生信息
        List<String> studentIds = new ArrayList<>(studentConsumptionMap.keySet());
        Map<String, UserStudentExt> studentInfoMap = getStudentInfoMap(studentIds);

        return studentConsumptionMap.entrySet().stream()
                .map(entry -> {
                    String studentId = entry.getKey();
                    List<StudentCourseConsumption> studentConsumptions = entry.getValue();
                    UserStudentExt studentInfo = studentInfoMap.get(studentId);

                    CourseConsumptionRoleDashboardDto.StudentConsumptionStats stats =
                            new CourseConsumptionRoleDashboardDto.StudentConsumptionStats();

                    stats.setStudentId(studentId);
                    stats.setStudentName(studentInfo != null ? studentInfo.getName() : "未知");
                    stats.setStudentPhone(studentInfo != null ? studentInfo.getPhone() : "");

                    // 计算课消统计
                    BigDecimal totalConsumption = studentConsumptions.stream()
                            .map(StudentCourseConsumption::getConsumedHours)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    stats.setTotalConsumption(totalConsumption);
                    stats.setConsumptionCount((long) studentConsumptions.size());

                    // 计算周/月课消
                    if (timeRangeInfo.getTotalWeeks() > 0) {
                        BigDecimal weeklyConsumption = totalConsumption.divide(
                                BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
                        stats.setWeeklyConsumption(weeklyConsumption);
                    }

                    if (timeRangeInfo.getTotalMonths() > 0) {
                        BigDecimal monthlyConsumption = totalConsumption.divide(
                                BigDecimal.valueOf(timeRangeInfo.getTotalMonths()), 2, RoundingMode.HALF_UP);
                        stats.setMonthlyConsumption(monthlyConsumption);
                    }

                    // 最后课消时间
                    Date lastConsumptionTime = studentConsumptions.stream()
                            .map(StudentCourseConsumption::getConsumptionTime)
                            .max(Date::compareTo)
                            .orElse(null);
                    stats.setLastConsumptionTime(lastConsumptionTime);

                    // 学科分布
                    Map<String, List<StudentCourseConsumption>> subjectMap = studentConsumptions.stream()
                            .collect(Collectors.groupingBy(StudentCourseConsumption::getSubject));

                    List<CourseConsumptionRoleDashboardDto.SubjectConsumption> subjectConsumptions =
                            subjectMap.entrySet().stream()
                            .map(subjectEntry -> {
                                CourseConsumptionRoleDashboardDto.SubjectConsumption subjectConsumption =
                                        new CourseConsumptionRoleDashboardDto.SubjectConsumption();
                                subjectConsumption.setSubject(subjectEntry.getKey());

                                BigDecimal subjectTotal = subjectEntry.getValue().stream()
                                        .map(StudentCourseConsumption::getConsumedHours)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                subjectConsumption.setConsumption(subjectTotal);
                                subjectConsumption.setCount((long) subjectEntry.getValue().size());

                                // 计算占比
                                if (totalConsumption.compareTo(BigDecimal.ZERO) > 0) {
                                    BigDecimal percentage = subjectTotal.divide(totalConsumption, 4, RoundingMode.HALF_UP)
                                            .multiply(BigDecimal.valueOf(100));
                                    subjectConsumption.setPercentage(percentage);
                                } else {
                                    subjectConsumption.setPercentage(BigDecimal.ZERO);
                                }

                                return subjectConsumption;
                            })
                            .collect(Collectors.toList());

                    stats.setSubjectConsumptions(subjectConsumptions);

                    return stats;
                })
                .sorted((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()))
                .collect(Collectors.toList());
    }

    /**
     * 计算周课消趋势
     */
    private List<CourseConsumptionRoleDashboardDto.WeeklyTrendData> calculateWeeklyTrends(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo) {

        if (consumptions.isEmpty()) {
            return new ArrayList<>();
        }

        LocalDate startDate = LocalDate.parse(timeRangeInfo.getStartDate());
        LocalDate endDate = LocalDate.parse(timeRangeInfo.getEndDate());

        List<CourseConsumptionRoleDashboardDto.WeeklyTrendData> trends = new ArrayList<>();

        LocalDate weekStart = startDate.with(java.time.DayOfWeek.MONDAY);
        int weekNumber = 1;

        while (!weekStart.isAfter(endDate)) {
            LocalDate weekEnd = weekStart.plusDays(6);
            if (weekEnd.isAfter(endDate)) {
                weekEnd = endDate;
            }

            // 筛选该周的课消数据
            LocalDate finalWeekStart = weekStart;
            LocalDate finalWeekEnd = weekEnd;
            List<StudentCourseConsumption> weekConsumptions = consumptions.stream()
                    .filter(consumption -> {
                        LocalDate consumptionDate = DateUtil.toLocalDateTime(consumption.getConsumptionTime()).toLocalDate();
                        return !consumptionDate.isBefore(finalWeekStart) && !consumptionDate.isAfter(finalWeekEnd);
                    })
                    .collect(Collectors.toList());

            CourseConsumptionRoleDashboardDto.WeeklyTrendData trendData =
                    new CourseConsumptionRoleDashboardDto.WeeklyTrendData();
            trendData.setWeekStart(weekStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            trendData.setWeekEnd(weekEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            trendData.setWeekLabel("第" + weekNumber + "周");

            BigDecimal weekConsumption = weekConsumptions.stream()
                    .map(StudentCourseConsumption::getConsumedHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            trendData.setConsumption(weekConsumption);

            Set<String> weekStudents = weekConsumptions.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            trendData.setStudentCount((long) weekStudents.size());
            trendData.setConsumptionCount((long) weekConsumptions.size());

            trends.add(trendData);

            weekStart = weekStart.plusWeeks(1);
            weekNumber++;
        }

        return trends;
    }

    /**
     * 获取学生信息映射
     */
    private Map<String, UserStudentExt> getStudentInfoMap(List<String> studentIds) {
        if (studentIds.isEmpty()) {
            return new HashMap<>();
        }

        List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getDeleted, false)
                .list();

        return students.stream()
                .collect(Collectors.toMap(UserStudentExt::getStudentId, student -> student));
    }

    /**
     * 获取老师信息
     */
    private CourseConsumptionRoleDashboardDto.TeacherInfo getTeacherInfo(String teacherId) {
        TeacherProfile teacher = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getTeacherId, teacherId)
                .eq(TeacherProfile::getDeleted, false)
                .one();

        if (teacher == null) {
            return null;
        }

        CourseConsumptionRoleDashboardDto.TeacherInfo teacherInfo =
                new CourseConsumptionRoleDashboardDto.TeacherInfo();
        teacherInfo.setTeacherId(teacherId);
        teacherInfo.setTeacherName(teacher.getNickName());

        // 获取老师所属组信息
        TeachingGroupMember member = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getTeacherId, teacherId)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .one();

        if (member != null) {
            TeachingGroup group = teachingGroupService.getById(member.getGroupId());
            if (group != null) {
                teacherInfo.setGroupId(group.getId());
                teacherInfo.setGroupName(group.getName());
            }
        }

        return teacherInfo;
    }

    /**
     * 获取组信息
     */
    private CourseConsumptionRoleDashboardDto.GroupInfo getGroupInfo(String groupId) {
        TeachingGroup group = teachingGroupService.getById(groupId);
        if (group == null) {
            return null;
        }

        CourseConsumptionRoleDashboardDto.GroupInfo groupInfo =
                new CourseConsumptionRoleDashboardDto.GroupInfo();
        groupInfo.setGroupId(groupId);
        groupInfo.setGroupName(group.getName());
        groupInfo.setLeaderId(group.getLeaderId());

        // 获取组长姓名
        if (StrUtil.isNotBlank(group.getLeaderId())) {
            TeacherProfile leader = teacherProfileService.lambdaQuery()
                    .eq(TeacherProfile::getTeacherId, group.getLeaderId())
                    .eq(TeacherProfile::getDeleted, false)
                    .one();
            if (leader != null) {
                groupInfo.setLeaderName(leader.getNickName());
            }
        }

        return groupInfo;
    }

    /**
     * 计算组总体统计
     */
    private CourseConsumptionRoleDashboardDto.GroupOverallStats calculateGroupOverallStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo,
            String groupId) {

        CourseConsumptionRoleDashboardDto.GroupOverallStats stats =
                new CourseConsumptionRoleDashboardDto.GroupOverallStats();

        // 获取组信息
        TeachingGroup group = teachingGroupService.getById(groupId);
        if (group != null) {
            stats.setGroupName(group.getName());
        }

        if (consumptions.isEmpty()) {
            stats.setTotalTeachers(0L);
            stats.setActiveTeachers(0L);
            stats.setTotalStudents(0L);
            stats.setActiveStudents(0L);
            stats.setTotalConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerTeacher(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
            return stats;
        }

        // 计算基础统计
        Set<String> allTeachers = consumptions.stream()
                .map(StudentCourseConsumption::getTeacherId)
                .collect(Collectors.toSet());

        Set<String> allStudents = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        BigDecimal totalConsumption = consumptions.stream()
                .map(StudentCourseConsumption::getConsumedHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.setTotalTeachers((long) allTeachers.size());
        stats.setActiveTeachers((long) allTeachers.size());
        stats.setTotalStudents((long) allStudents.size());
        stats.setActiveStudents((long) allStudents.size());
        stats.setTotalConsumption(totalConsumption);

        // 计算平均值
        if (timeRangeInfo.getTotalWeeks() > 0) {
            BigDecimal avgWeekly = totalConsumption.divide(
                    BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
            stats.setAvgWeeklyConsumption(avgWeekly);

            if (allTeachers.size() > 0) {
                BigDecimal avgWeeklyPerTeacher = avgWeekly.divide(
                        BigDecimal.valueOf(allTeachers.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerTeacher(avgWeeklyPerTeacher);
            }

            if (allStudents.size() > 0) {
                BigDecimal avgWeeklyPerStudent = avgWeekly.divide(
                        BigDecimal.valueOf(allStudents.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);
            }
        }

        return stats;
    }

    /**
     * 计算组内老师统计
     */
    private List<CourseConsumptionRoleDashboardDto.TeacherConsumptionStats> calculateTeacherStatsInGroup(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo,
            String groupId) {

        if (consumptions.isEmpty()) {
            return new ArrayList<>();
        }

        // 按老师分组
        Map<String, List<StudentCourseConsumption>> teacherConsumptionMap = consumptions.stream()
                .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

        // 获取老师信息
        List<String> teacherIds = new ArrayList<>(teacherConsumptionMap.keySet());
        Map<String, TeacherProfile> teacherInfoMap = getTeacherInfoMap(teacherIds);

        return teacherConsumptionMap.entrySet().stream()
                .map(entry -> {
                    String teacherId = entry.getKey();
                    List<StudentCourseConsumption> teacherConsumptions = entry.getValue();
                    TeacherProfile teacherInfo = teacherInfoMap.get(teacherId);

                    CourseConsumptionRoleDashboardDto.TeacherConsumptionStats stats =
                            new CourseConsumptionRoleDashboardDto.TeacherConsumptionStats();

                    stats.setTeacherId(teacherId);
                    stats.setTeacherName(teacherInfo != null ? teacherInfo.getNickName() : "未知");

                    // 计算学生数量
                    Set<String> students = teacherConsumptions.stream()
                            .map(StudentCourseConsumption::getStudentId)
                            .collect(Collectors.toSet());
                    stats.setStudentCount((long) students.size());
                    stats.setActiveStudents((long) students.size());

                    // 计算课消统计
                    BigDecimal totalConsumption = teacherConsumptions.stream()
                            .map(StudentCourseConsumption::getConsumedHours)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    stats.setTotalConsumption(totalConsumption);

                    // 计算周课消
                    if (timeRangeInfo.getTotalWeeks() > 0) {
                        BigDecimal weeklyConsumption = totalConsumption.divide(
                                BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
                        stats.setWeeklyConsumption(weeklyConsumption);

                        // 生均周课消
                        if (students.size() > 0) {
                            BigDecimal avgWeeklyPerStudent = weeklyConsumption.divide(
                                    BigDecimal.valueOf(students.size()), 2, RoundingMode.HALF_UP);
                            stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);
                        }
                    }

                    // 课消率 (这里简化为100%，实际应该考虑老师的所有学生)
                    stats.setConsumptionRate(BigDecimal.valueOf(100));

                    // 最后课消时间
                    Date lastConsumptionTime = teacherConsumptions.stream()
                            .map(StudentCourseConsumption::getConsumptionTime)
                            .max(Date::compareTo)
                            .orElse(null);
                    stats.setLastConsumptionTime(lastConsumptionTime);

                    return stats;
                })
                .sorted((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()))
                .collect(Collectors.toList());
    }

    /**
     * 获取老师信息映射
     */
    private Map<String, TeacherProfile> getTeacherInfoMap(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        List<TeacherProfile> teachers = teacherProfileService.lambdaQuery()
                .in(TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .list();

        return teachers.stream()
                .collect(Collectors.toMap(TeacherProfile::getTeacherId, teacher -> teacher));
    }

    /**
     * 计算管理总体统计
     */
    private CourseConsumptionRoleDashboardDto.AdminOverallStats calculateAdminOverallStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo) {

        CourseConsumptionRoleDashboardDto.AdminOverallStats stats =
                new CourseConsumptionRoleDashboardDto.AdminOverallStats();

        if (consumptions.isEmpty()) {
            stats.setTotalGroups(0L);
            stats.setActiveGroups(0L);
            stats.setTotalTeachers(0L);
            stats.setActiveTeachers(0L);
            stats.setTotalStudents(0L);
            stats.setActiveStudents(0L);
            stats.setTotalConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerGroup(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerTeacher(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
            return stats;
        }

        // 计算基础统计
        Set<String> allTeachers = consumptions.stream()
                .map(StudentCourseConsumption::getTeacherId)
                .collect(Collectors.toSet());

        Set<String> allStudents = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        // 获取活跃组数量
        Set<String> activeGroups = getActiveGroupsByTeachers(new ArrayList<>(allTeachers));

        BigDecimal totalConsumption = consumptions.stream()
                .map(StudentCourseConsumption::getConsumedHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.setTotalGroups((long) activeGroups.size());
        stats.setActiveGroups((long) activeGroups.size());
        stats.setTotalTeachers((long) allTeachers.size());
        stats.setActiveTeachers((long) allTeachers.size());
        stats.setTotalStudents((long) allStudents.size());
        stats.setActiveStudents((long) allStudents.size());
        stats.setTotalConsumption(totalConsumption);

        // 计算平均值
        if (timeRangeInfo.getTotalWeeks() > 0) {
            BigDecimal avgWeekly = totalConsumption.divide(
                    BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
            stats.setAvgWeeklyConsumption(avgWeekly);

            if (activeGroups.size() > 0) {
                BigDecimal avgWeeklyPerGroup = avgWeekly.divide(
                        BigDecimal.valueOf(activeGroups.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerGroup(avgWeeklyPerGroup);
            }

            if (allTeachers.size() > 0) {
                BigDecimal avgWeeklyPerTeacher = avgWeekly.divide(
                        BigDecimal.valueOf(allTeachers.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerTeacher(avgWeeklyPerTeacher);
            }

            if (allStudents.size() > 0) {
                BigDecimal avgWeeklyPerStudent = avgWeekly.divide(
                        BigDecimal.valueOf(allStudents.size()), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);
            }
        }

        return stats;
    }

    /**
     * 计算组统计
     */
    private List<CourseConsumptionRoleDashboardDto.GroupConsumptionStats> calculateGroupStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionRoleDashboardDto.TimeRangeInfo timeRangeInfo) {

        if (consumptions.isEmpty()) {
            return new ArrayList<>();
        }

        // 按老师分组，然后按组聚合
        Map<String, List<StudentCourseConsumption>> teacherConsumptionMap = consumptions.stream()
                .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

        // 获取老师所属组信息
        Map<String, String> teacherGroupMap = getTeacherGroupMap(new ArrayList<>(teacherConsumptionMap.keySet()));

        // 按组聚合数据
        Map<String, List<StudentCourseConsumption>> groupConsumptionMap = new HashMap<>();
        teacherConsumptionMap.forEach((teacherId, teacherConsumptions) -> {
            String groupId = teacherGroupMap.get(teacherId);
            if (groupId != null) {
                groupConsumptionMap.computeIfAbsent(groupId, k -> new ArrayList<>())
                        .addAll(teacherConsumptions);
            }
        });

        // 获取组信息
        List<String> groupIds = new ArrayList<>(groupConsumptionMap.keySet());
        Map<String, TeachingGroup> groupInfoMap = getGroupInfoMap(groupIds);

        return groupConsumptionMap.entrySet().stream()
                .map(entry -> {
                    String groupId = entry.getKey();
                    List<StudentCourseConsumption> groupConsumptions = entry.getValue();
                    TeachingGroup groupInfo = groupInfoMap.get(groupId);

                    CourseConsumptionRoleDashboardDto.GroupConsumptionStats stats =
                            new CourseConsumptionRoleDashboardDto.GroupConsumptionStats();

                    stats.setGroupId(groupId);
                    stats.setGroupName(groupInfo != null ? groupInfo.getName() : "未知");

                    // 获取组长信息
                    if (groupInfo != null && StrUtil.isNotBlank(groupInfo.getLeaderId())) {
                        TeacherProfile leader = teacherProfileService.lambdaQuery()
                                .eq(TeacherProfile::getTeacherId, groupInfo.getLeaderId())
                                .eq(TeacherProfile::getDeleted, false)
                                .one();
                        if (leader != null) {
                            stats.setLeaderName(leader.getNickName());
                        }
                    }

                    // 计算统计数据
                    Set<String> teachers = groupConsumptions.stream()
                            .map(StudentCourseConsumption::getTeacherId)
                            .collect(Collectors.toSet());

                    Set<String> students = groupConsumptions.stream()
                            .map(StudentCourseConsumption::getStudentId)
                            .collect(Collectors.toSet());

                    stats.setTeacherCount((long) teachers.size());
                    stats.setActiveTeachers((long) teachers.size());
                    stats.setStudentCount((long) students.size());
                    stats.setActiveStudents((long) students.size());

                    BigDecimal totalConsumption = groupConsumptions.stream()
                            .map(StudentCourseConsumption::getConsumedHours)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    stats.setTotalConsumption(totalConsumption);

                    // 计算周课消
                    if (timeRangeInfo.getTotalWeeks() > 0) {
                        BigDecimal weeklyConsumption = totalConsumption.divide(
                                BigDecimal.valueOf(timeRangeInfo.getTotalWeeks()), 2, RoundingMode.HALF_UP);
                        stats.setWeeklyConsumption(weeklyConsumption);

                        // 师均周课消
                        if (teachers.size() > 0) {
                            BigDecimal avgWeeklyPerTeacher = weeklyConsumption.divide(
                                    BigDecimal.valueOf(teachers.size()), 2, RoundingMode.HALF_UP);
                            stats.setAvgWeeklyConsumptionPerTeacher(avgWeeklyPerTeacher);
                        }

                        // 生均周课消
                        if (students.size() > 0) {
                            BigDecimal avgWeeklyPerStudent = weeklyConsumption.divide(
                                    BigDecimal.valueOf(students.size()), 2, RoundingMode.HALF_UP);
                            stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);
                        }
                    }

                    // 课消率 (简化为100%)
                    stats.setConsumptionRate(BigDecimal.valueOf(100));

                    return stats;
                })
                .sorted((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()))
                .collect(Collectors.toList());
    }

    /**
     * 获取老师所属组映射
     */
    private Map<String, String> getTeacherGroupMap(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .in(TeachingGroupMember::getTeacherId, teacherIds)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        Map<String, String> teacherGroupMap = members.stream()
                .collect(Collectors.toMap(
                        TeachingGroupMember::getTeacherId,
                        TeachingGroupMember::getGroupId,
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));

        // 为没有分配组的老师分配默认组
        for (String teacherId : teacherIds) {
            if (!teacherGroupMap.containsKey(teacherId)) {
                teacherGroupMap.put(teacherId, "default");
            }
        }

        return teacherGroupMap;
    }

    /**
     * 获取组信息映射
     */
    private Map<String, TeachingGroup> getGroupInfoMap(List<String> groupIds) {
        if (groupIds.isEmpty()) {
            return new HashMap<>();
        }

        // 过滤出真实的组ID（排除默认组）
        List<String> realGroupIds = groupIds.stream()
                .filter(id -> !"default".equals(id))
                .collect(Collectors.toList());

        Map<String, TeachingGroup> groupMap = new HashMap<>();

        if (!realGroupIds.isEmpty()) {
            List<TeachingGroup> groups = teachingGroupService.lambdaQuery()
                    .in(TeachingGroup::getId, realGroupIds)
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            groupMap = groups.stream()
                    .collect(Collectors.toMap(TeachingGroup::getId, group -> group));
        }

        // 如果包含默认组，创建一个虚拟的默认组对象
        if (groupIds.contains("default")) {
            TeachingGroup defaultGroup = new TeachingGroup();
            defaultGroup.setId("default");
            defaultGroup.setName("未分组");
            defaultGroup.setLeaderId(null);
            groupMap.put("default", defaultGroup);
        }

        return groupMap;
    }

    /**
     * 根据老师获取活跃组
     */
    private Set<String> getActiveGroupsByTeachers(List<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new HashSet<>();
        }

        List<TeachingGroupMember> members = teachingGroupMemberService.lambdaQuery()
                .in(TeachingGroupMember::getTeacherId, teacherIds)
                .eq(TeachingGroupMember::getDeleted, false)
                .eq(TeachingGroupMember::getStatus, "active")
                .list();

        return members.stream()
                .map(TeachingGroupMember::getGroupId)
                .collect(Collectors.toSet());
    }
}

package org.nonamespace.word.server.facade;

import org.nonamespace.word.server.dto.dashboard.CourseConsumptionRoleDashboardDto;

/**
 * 分角色课消看板Facade接口
 * 
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface ICourseConsumptionRoleDashboardFacade {

    /**
     * 获取老师看板数据
     *
     * @param req 老师看板请求参数
     * @return 老师看板数据
     */
    CourseConsumptionRoleDashboardDto.TeacherDashboardResponse getTeacherDashboardData(
            CourseConsumptionRoleDashboardDto.TeacherDashboardRequest req);

    /**
     * 获取组长看板数据
     *
     * @param req 组长看板请求参数
     * @return 组长看板数据
     */
    CourseConsumptionRoleDashboardDto.GroupLeaderDashboardResponse getGroupLeaderDashboardData(
            CourseConsumptionRoleDashboardDto.GroupLeaderDashboardRequest req);

    /**
     * 获取管理看板数据
     *
     * @param req 管理看板请求参数
     * @return 管理看板数据
     */
    CourseConsumptionRoleDashboardDto.AdminDashboardResponse getAdminDashboardData(
            CourseConsumptionRoleDashboardDto.AdminDashboardRequest req);

    /**
     * 获取老师选择器选项 (组长使用)
     *
     * @return 老师选择器选项
     */
    CourseConsumptionRoleDashboardDto.TeacherSelectorResponse getTeacherSelectorOptions();

    /**
     * 获取组选择器选项 (管理使用)
     *
     * @return 组选择器选项
     */
    CourseConsumptionRoleDashboardDto.GroupSelectorResponse getGroupSelectorOptions();

    /**
     * 获取指定组的老师选择器选项 (管理使用)
     *
     * @param groupId 组ID
     * @return 老师选择器选项
     */
    CourseConsumptionRoleDashboardDto.TeacherSelectorResponse getTeacherSelectorOptionsByGroup(String groupId);
}

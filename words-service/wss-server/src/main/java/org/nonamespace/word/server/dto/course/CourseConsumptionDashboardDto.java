package org.nonamespace.word.server.dto.course;

import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课消看板相关DTO
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public class CourseConsumptionDashboardDto {

    /**
     * 获取课消看板数据请求参数
     */
    @Data
    public static class GetDashboardReq {
        /**
         * 开始日期 (YYYY-MM-DD)
         */
        @NotBlank(message = "开始日期不能为空")
        @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "开始日期格式错误，应为YYYY-MM-DD")
        private String startDate;

        /**
         * 结束日期 (YYYY-MM-DD)
         */
        @NotBlank(message = "结束日期不能为空")
        @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2}", message = "结束日期格式错误，应为YYYY-MM-DD")
        private String endDate;

        /**
         * 时间范围类型 (thisWeek, lastWeek, thisMonth, lastMonth, custom)
         */
        private String timeRangeType;

        /**
         * 学科过滤
         */
        private String subject;

        /**
         * 课型过滤
         */
        private String specification;

        /**
         * 学生ID过滤（用于查看特定学生）
         */
        private String studentId;

        /**
         * 教师ID过滤（用于查看特定教师的学生）
         */
        private String teacherId;

        /**
         * 销售ID过滤（用于查看特定销售的学生）
         */
        private String salesId;

        /**
         * 教学组ID过滤
         */
        private String teachingGroupId;

        /**
         * 销售组ID过滤
         */
        private String salesGroupId;
    }

    /**
     * 课消看板数据响应
     */
    @Data
    public static class DashboardResp {
        /**
         * 总体统计
         */
        private OverallStats overallStats;

        /**
         * 学生课消列表
         */
        private List<StudentConsumptionStats> studentStats;

        /**
         * 教师课消统计（教学组长可见）
         */
        private List<TeacherConsumptionStats> teacherStats;

        /**
         * 教学组课消统计（教学组长可见）
         */
        private List<TeachingGroupConsumptionStats> teachingGroupStats;

        /**
         * 销售课消统计（销售组长可见）
         */
        private List<SalesConsumptionStats> salesStats;

        /**
         * 时间范围信息
         */
        private TimeRangeInfo timeRangeInfo;
    }

    /**
     * 总体统计
     */
    @Data
    public static class OverallStats {
        /**
         * 总学生数
         */
        private Long totalStudents;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 平均周课消
         */
        private BigDecimal avgWeeklyConsumption;

        /**
         * 平均月课消
         */
        private BigDecimal avgMonthlyConsumption;

        /**
         * 活跃学生数（有课消的学生）
         */
        private Long activeStudents;

        /**
         * 课消率（活跃学生/总学生）
         */
        private BigDecimal consumptionRate;
    }

    /**
     * 学生课消统计
     */
    @Data
    public static class StudentConsumptionStats {
        /**
         * 学生ID
         */
        private String studentId;

        /**
         * 学生姓名
         */
        private String studentName;

        /**
         * 学生手机号
         */
        private String studentPhone;

        /**
         * 销售姓名
         */
        private String salesName;

        /**
         * 教师姓名列表
         */
        private List<String> teacherNames;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 周课消课时
         */
        private BigDecimal weeklyConsumption;

        /**
         * 月课消课时
         */
        private BigDecimal monthlyConsumption;

        /**
         * 课消次数
         */
        private Long consumptionCount;

        /**
         * 最后课消时间
         */
        private String lastConsumptionTime;

        /**
         * 学科分布
         */
        private List<SubjectConsumption> subjectConsumptions;
    }

    /**
     * 教师课消统计
     */
    @Data
    public static class TeacherConsumptionStats {
        /**
         * 教师ID
         */
        private String teacherId;

        /**
         * 教师姓名
         */
        private String teacherName;

        /**
         * 教学组名称
         */
        private String teachingGroupName;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;

        /**
         * 生均月课消
         */
        private BigDecimal avgMonthlyConsumptionPerStudent;

        /**
         * 课消次数
         */
        private Long consumptionCount;
    }

    /**
     * 教学组课消统计
     */
    @Data
    public static class TeachingGroupConsumptionStats {
        /**
         * 教学组ID
         */
        private String teachingGroupId;

        /**
         * 教学组名称
         */
        private String teachingGroupName;

        /**
         * 教师数量
         */
        private Long teacherCount;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;

        /**
         * 生均月课消
         */
        private BigDecimal avgMonthlyConsumptionPerStudent;

        /**
         * 师均课消
         */
        private BigDecimal avgConsumptionPerTeacher;
    }

    /**
     * 销售课消统计
     */
    @Data
    public static class SalesConsumptionStats {
        /**
         * 销售ID
         */
        private String salesId;

        /**
         * 销售姓名
         */
        private String salesName;

        /**
         * 销售组名称
         */
        private String salesGroupName;

        /**
         * 学生数量
         */
        private Long studentCount;

        /**
         * 总课消课时
         */
        private BigDecimal totalConsumption;

        /**
         * 生均周课消
         */
        private BigDecimal avgWeeklyConsumptionPerStudent;

        /**
         * 生均月课消
         */
        private BigDecimal avgMonthlyConsumptionPerStudent;

        /**
         * 活跃学生数
         */
        private Long activeStudents;

        /**
         * 课消率
         */
        private BigDecimal consumptionRate;
    }

    /**
     * 学科课消分布
     */
    @Data
    public static class SubjectConsumption {
        /**
         * 学科
         */
        private String subject;

        /**
         * 课消课时
         */
        private BigDecimal consumption;

        /**
         * 课消次数
         */
        private Long count;
    }

    /**
     * 时间范围信息
     */
    @Data
    public static class TimeRangeInfo {
        /**
         * 开始日期
         */
        private String startDate;

        /**
         * 结束日期
         */
        private String endDate;

        /**
         * 时间范围类型
         */
        private String timeRangeType;

        /**
         * 时间范围描述
         */
        private String description;

        /**
         * 总天数
         */
        private Long totalDays;

        /**
         * 总周数
         */
        private BigDecimal totalWeeks;

        /**
         * 总月数
         */
        private BigDecimal totalMonths;
    }
}

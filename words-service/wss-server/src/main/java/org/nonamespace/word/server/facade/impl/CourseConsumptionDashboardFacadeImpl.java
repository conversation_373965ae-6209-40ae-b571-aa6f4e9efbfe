package org.nonamespace.word.server.facade.impl;

import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nonamespace.word.server.domain.*;
import org.nonamespace.word.server.dto.course.CourseConsumptionDashboardDto;
import org.nonamespace.word.server.facade.ICourseConsumptionDashboardFacade;
import org.nonamespace.word.server.service.*;
import org.nonamespace.word.server.util.SystemDataQueryUtil;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课消看板Facade实现类
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CourseConsumptionDashboardFacadeImpl implements ICourseConsumptionDashboardFacade {

    private final IStudentCourseConsumptionService studentCourseConsumptionService;
    private final UserStudentExtService userStudentExtService;
    private final ITeacherProfileService teacherProfileService;
    private final ITeachingGroupService teachingGroupService;
    private final ITeachingGroupMemberService teachingGroupMemberService;
    private final ISalesGroupService salesGroupService;
    private final ISalesGroupMemberService salesGroupMemberService;
    private final ISaleProfileService saleProfileService;
    private final SystemDataQueryUtil systemDataQueryUtil;

    @Override
    public CourseConsumptionDashboardDto.DashboardResp getDashboardData(CourseConsumptionDashboardDto.GetDashboardReq req) {
        long startTime = System.currentTimeMillis();
        log.info("获取课消看板数据，请求参数: {}", req);

        try {
            // 应用数据权限过滤
            applyDataPermissions(req);
            log.info("权限过滤完成，耗时: {}ms", System.currentTimeMillis() - startTime);

            // 构建时间范围信息
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo = buildTimeRangeInfo(req);
            log.info("时间范围构建完成，耗时: {}ms", System.currentTimeMillis() - startTime);

            // 使用高性能查询方法
            CourseConsumptionDashboardDto.DashboardResp response = getDashboardDataOptimized(req, timeRangeInfo);

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("课消看板数据获取成功，总耗时: {}ms, 学生数: {}, 总课消: {}",
                    totalTime,
                    response.getStudentStats() != null ? response.getStudentStats().size() : 0,
                    response.getOverallStats() != null ? response.getOverallStats().getTotalConsumption() : 0);

            return response;

        } catch (Exception e) {
            long totalTime = System.currentTimeMillis() - startTime;
            log.error("获取课消看板数据失败，耗时: {}ms", totalTime, e);
            throw new RuntimeException("获取课消看板数据失败: " + e.getMessage());
        }
    }

    /**
     * 高性能优化版本的数据获取方法
     */
    private CourseConsumptionDashboardDto.DashboardResp getDashboardDataOptimized(
            CourseConsumptionDashboardDto.GetDashboardReq req,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        long stepStart = System.currentTimeMillis();

        // 1. 快速获取课消数据（只查询必要字段）
        List<StudentCourseConsumption> consumptions = getConsumptionDataFast(req);
        log.info("课消数据查询完成，数量: {}, 耗时: {}ms",
                consumptions.size(), System.currentTimeMillis() - stepStart);

        if (consumptions.isEmpty()) {
            return createEmptyResponse(timeRangeInfo);
        }

        stepStart = System.currentTimeMillis();

        // 2. 批量获取关联数据
        Map<String, String> studentNameMap = getStudentNamesMap(consumptions);
        Map<String, String> studentPhoneMap = getStudentPhonesMap(consumptions);
        Map<String, String> studentSalesMap = getStudentSalesMap(consumptions);
        Map<String, String> teacherNameMap = getTeacherNamesMap(consumptions);

        log.info("关联数据查询完成，耗时: {}ms", System.currentTimeMillis() - stepStart);

        stepStart = System.currentTimeMillis();

        // 3. 快速计算统计数据
        CourseConsumptionDashboardDto.DashboardResp response = new CourseConsumptionDashboardDto.DashboardResp();
        response.setTimeRangeInfo(timeRangeInfo);

        // 计算总体统计
        response.setOverallStats(calculateOverallStatsFast(consumptions, timeRangeInfo));

        // 计算学生统计
        response.setStudentStats(calculateStudentStatsFast(consumptions, timeRangeInfo,
                studentNameMap, studentPhoneMap, studentSalesMap, teacherNameMap));

        log.info("统计计算完成，耗时: {}ms", System.currentTimeMillis() - stepStart);

        // 根据用户角色计算相应的统计数据（简化版本）
        if (systemDataQueryUtil.isTeacherGroupManager()) {
            response.setTeacherStats(calculateTeacherStatsFast(consumptions, timeRangeInfo, teacherNameMap));
        }

        if (systemDataQueryUtil.isSalesManager() || systemDataQueryUtil.isAdminOrHr()) {
            response.setSalesStats(calculateSalesStatsFast(consumptions, timeRangeInfo, studentSalesMap));
        }

        return response;
    }

    /**
     * 应用数据权限过滤
     */
    private void applyDataPermissions(CourseConsumptionDashboardDto.GetDashboardReq req) {
        String currentUserId = SecurityUtils.getUserId().toString();

        if (systemDataQueryUtil.isAdminOrHr()) {
            // 管理员和HR可以查看所有数据
            return;
        }

        if (systemDataQueryUtil.isSalesDirector()) {
            // 销售总监可以查看所有销售数据
            return;
        }

        if (systemDataQueryUtil.isSalesGroupLeader()) {
            // 销售组长只能查看本组数据
            List<SalesGroup> managedGroups = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getLeaderId, currentUserId)
                    .eq(SalesGroup::getDeleted, false)
                    .list();

            if (!managedGroups.isEmpty()) {
                req.setSalesGroupId(managedGroups.get(0).getId());
            } else {
                // 如果不是任何组的组长，则没有权限查看
                req.setSalesId("NONE");
            }
            return;
        }

        if (systemDataQueryUtil.isSales()) {
            // 销售只能查看自己的学生
            req.setSalesId(currentUserId);
            return;
        }

        if (systemDataQueryUtil.isTeacherGroupManager()) {
            // 教学组长只能查看本组数据
            List<TeachingGroup> managedGroups = teachingGroupService.lambdaQuery()
                    .and(wrapper -> wrapper.eq(TeachingGroup::getLeaderId, currentUserId)
                            .or()
                            .eq(TeachingGroup::getAdminId, currentUserId))
                    .eq(TeachingGroup::getDeleted, false)
                    .list();

            if (!managedGroups.isEmpty()) {
                req.setTeachingGroupId(managedGroups.get(0).getId());
            } else {
                // 如果不是任何组的管理员，则没有权限查看
                req.setTeacherId("NONE");
            }
            return;
        }

        if (systemDataQueryUtil.isTeacher()) {
            // 教师只能查看自己的学生
            req.setTeacherId(currentUserId);
            return;
        }

        // 其他角色没有权限查看
        req.setStudentId("NONE");
    }

    /**
     * 构建时间范围信息
     */
    private CourseConsumptionDashboardDto.TimeRangeInfo buildTimeRangeInfo(CourseConsumptionDashboardDto.GetDashboardReq req) {
        CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo = new CourseConsumptionDashboardDto.TimeRangeInfo();
        
        LocalDate startDate = LocalDate.parse(req.getStartDate());
        LocalDate endDate = LocalDate.parse(req.getEndDate());
        
        timeRangeInfo.setStartDate(req.getStartDate());
        timeRangeInfo.setEndDate(req.getEndDate());
        timeRangeInfo.setTimeRangeType(req.getTimeRangeType());
        
        // 计算总天数
        long totalDays = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        timeRangeInfo.setTotalDays(totalDays);
        
        // 计算总周数
        BigDecimal totalWeeks = BigDecimal.valueOf(totalDays).divide(BigDecimal.valueOf(7), 2, RoundingMode.HALF_UP);
        timeRangeInfo.setTotalWeeks(totalWeeks);
        
        // 计算总月数
        BigDecimal totalMonths = BigDecimal.valueOf(totalDays).divide(BigDecimal.valueOf(30), 2, RoundingMode.HALF_UP);
        timeRangeInfo.setTotalMonths(totalMonths);
        
        // 设置描述
        String description = getTimeRangeDescription(req.getTimeRangeType(), startDate, endDate);
        timeRangeInfo.setDescription(description);
        
        return timeRangeInfo;
    }

    /**
     * 获取时间范围描述
     */
    private String getTimeRangeDescription(String timeRangeType, LocalDate startDate, LocalDate endDate) {
        if (timeRangeType == null) {
            return String.format("%s 至 %s", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), 
                                endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        
        return switch (timeRangeType) {
            case "thisWeek" -> "本周";
            case "lastWeek" -> "上周";
            case "thisMonth" -> "本月";
            case "lastMonth" -> "上月";
            default -> String.format("%s 至 %s", startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), 
                                    endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        };
    }

    /**
     * 获取课消数据
     */
    private List<StudentCourseConsumption> getConsumptionData(CourseConsumptionDashboardDto.GetDashboardReq req) {
        MPJLambdaWrapper<StudentCourseConsumption> wrapper = new MPJLambdaWrapper<StudentCourseConsumption>()
                .selectAll(StudentCourseConsumption.class)
                .select(UserStudentExt::getName, UserStudentExt::getPhone, UserStudentExt::getSalesId)
                .leftJoin(UserStudentExt.class, UserStudentExt::getStudentId, StudentCourseConsumption::getStudentId)
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        // 应用过滤条件
        if (req.getSubject() != null && !req.getSubject().isEmpty()) {
            wrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }
        
        if (req.getSpecification() != null && !req.getSpecification().isEmpty()) {
            wrapper.eq(StudentCourseConsumption::getSpecification, req.getSpecification());
        }
        
        if (req.getStudentId() != null && !req.getStudentId().isEmpty()) {
            if ("NONE".equals(req.getStudentId())) {
                wrapper.eq(StudentCourseConsumption::getStudentId, "");
            } else {
                wrapper.eq(StudentCourseConsumption::getStudentId, req.getStudentId());
            }
        }
        
        if (req.getTeacherId() != null && !req.getTeacherId().isEmpty()) {
            if ("NONE".equals(req.getTeacherId())) {
                wrapper.eq(StudentCourseConsumption::getTeacherId, "");
            } else {
                wrapper.eq(StudentCourseConsumption::getTeacherId, req.getTeacherId());
            }
        }

        // 应用销售过滤
        if (req.getSalesId() != null && !req.getSalesId().isEmpty()) {
            if ("NONE".equals(req.getSalesId())) {
                wrapper.eq(UserStudentExt::getSalesId, "");
            } else {
                wrapper.eq(UserStudentExt::getSalesId, req.getSalesId());
            }
        }

        // 应用销售组过滤
        if (req.getSalesGroupId() != null && !req.getSalesGroupId().isEmpty()) {
            List<String> salesIds = getSalesIdsByGroupId(req.getSalesGroupId());
            if (!salesIds.isEmpty()) {
                wrapper.in(UserStudentExt::getSalesId, salesIds);
            } else {
                wrapper.eq(UserStudentExt::getSalesId, "");
            }
        }

        // 应用教学组过滤
        if (req.getTeachingGroupId() != null && !req.getTeachingGroupId().isEmpty()) {
            List<String> teacherIds = getTeacherIdsByGroupId(req.getTeachingGroupId());
            if (!teacherIds.isEmpty()) {
                wrapper.in(StudentCourseConsumption::getTeacherId, teacherIds);
            } else {
                wrapper.eq(StudentCourseConsumption::getTeacherId, "");
            }
        }

        log.info("开始查询课消数据，时间范围: {} 到 {}", req.getStartDate(), req.getEndDate());

        // 优化查询：使用更高效的查询条件
        var queryWrapper = studentCourseConsumptionService.lambdaQuery()
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        // 应用学科过滤
        if (req.getSubject() != null && !req.getSubject().isEmpty()) {
            queryWrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }

        // 应用课型过滤
        if (req.getSpecification() != null && !req.getSpecification().isEmpty()) {
            queryWrapper.eq(StudentCourseConsumption::getSpecification, req.getSpecification());
        }

        // 应用学生ID过滤
        if (req.getStudentId() != null && !req.getStudentId().isEmpty()) {
            if ("NONE".equals(req.getStudentId())) {
                return new ArrayList<>(); // 无权限时直接返回空
            }
            queryWrapper.eq(StudentCourseConsumption::getStudentId, req.getStudentId());
        }

        // 应用教师ID过滤
        if (req.getTeacherId() != null && !req.getTeacherId().isEmpty()) {
            if ("NONE".equals(req.getTeacherId())) {
                return new ArrayList<>(); // 无权限时直接返回空
            }
            queryWrapper.eq(StudentCourseConsumption::getTeacherId, req.getTeacherId());
        }

        // 应用教学组过滤（在数据库层面）
        if (req.getTeachingGroupId() != null && !req.getTeachingGroupId().isEmpty()) {
            List<String> teacherIds = getTeacherIdsByGroupId(req.getTeachingGroupId());
            if (teacherIds.isEmpty()) {
                return new ArrayList<>();
            }
            queryWrapper.in(StudentCourseConsumption::getTeacherId, teacherIds);
        }

        // 执行查询，添加排序和限制
        List<StudentCourseConsumption> consumptions = queryWrapper
                .orderByDesc(StudentCourseConsumption::getConsumptionTime)
                .last("LIMIT 10000") // 限制最大查询数量，防止内存溢出
                .list();

        log.info("基础查询完成，获取到 {} 条课消记录", consumptions.size());

        // 应用销售过滤（需要关联学生表）
        if (req.getSalesId() != null && !req.getSalesId().isEmpty()) {
            if ("NONE".equals(req.getSalesId())) {
                return new ArrayList<>();
            }
            consumptions = filterBySalesIdOptimized(consumptions, req.getSalesId());
        }

        if (req.getSalesGroupId() != null && !req.getSalesGroupId().isEmpty()) {
            List<String> salesIds = getSalesIdsByGroupId(req.getSalesGroupId());
            if (salesIds.isEmpty()) {
                return new ArrayList<>();
            }
            consumptions = filterBySalesIdsOptimized(consumptions, salesIds);
        }

        log.info("最终返回 {} 条课消记录", consumptions.size());
        return consumptions;
    }

    /**
     * 根据销售ID过滤课消记录（优化版本）
     */
    private List<StudentCourseConsumption> filterBySalesIdOptimized(List<StudentCourseConsumption> consumptions, String salesId) {
        if (consumptions.isEmpty()) {
            return consumptions;
        }

        // 获取该销售的所有学生ID（使用Set提高查找效率）
        Set<String> studentIds = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getSalesId, salesId)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .map(UserStudentExt::getStudentId)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new ArrayList<>();
        }

        return consumptions.stream()
                .filter(c -> studentIds.contains(c.getStudentId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据销售ID过滤课消记录（原版本，保留兼容性）
     */
    private List<StudentCourseConsumption> filterBySalesId(List<StudentCourseConsumption> consumptions, String salesId) {
        return filterBySalesIdOptimized(consumptions, salesId);
    }

    /**
     * 根据销售ID列表过滤课消记录（优化版本）
     */
    private List<StudentCourseConsumption> filterBySalesIdsOptimized(List<StudentCourseConsumption> consumptions, List<String> salesIds) {
        if (consumptions.isEmpty() || salesIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取这些销售的所有学生ID（使用Set提高查找效率）
        Set<String> studentIds = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getSalesId, salesIds)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .map(UserStudentExt::getStudentId)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new ArrayList<>();
        }

        return consumptions.stream()
                .filter(c -> studentIds.contains(c.getStudentId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据销售ID列表过滤课消记录（原版本，保留兼容性）
     */
    private List<StudentCourseConsumption> filterBySalesIds(List<StudentCourseConsumption> consumptions, List<String> salesIds) {
        return filterBySalesIdsOptimized(consumptions, salesIds);
    }



    /**
     * 根据销售组ID获取销售ID列表
     */
    private List<String> getSalesIdsByGroupId(String groupId) {
        return salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getGroupId, groupId)
                .eq(SalesGroupMember::getStatus, "active")
                .eq(SalesGroupMember::getDeleted, false)
                .list()
                .stream()
                .map(SalesGroupMember::getSalesId)
                .collect(Collectors.toList());
    }

    /**
     * 根据教学组ID获取教师ID列表
     */
    private List<String> getTeacherIdsByGroupId(String groupId) {
        return teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getGroupId, groupId)
                .eq(TeachingGroupMember::getStatus, "active")
                .eq(TeachingGroupMember::getDeleted, false)
                .list()
                .stream()
                .map(TeachingGroupMember::getTeacherId)
                .collect(Collectors.toList());
    }

    /**
     * 计算总体统计
     */
    private CourseConsumptionDashboardDto.OverallStats calculateOverallStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        CourseConsumptionDashboardDto.OverallStats stats = new CourseConsumptionDashboardDto.OverallStats();

        // 获取所有学生ID
        Set<String> allStudentIds = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        // 总学生数
        stats.setTotalStudents((long) allStudentIds.size());

        // 活跃学生数（有课消的学生）
        Set<String> activeStudentIds = consumptions.stream()
                .filter(c -> c.getConsumedHours().compareTo(BigDecimal.ZERO) > 0)
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());
        stats.setActiveStudents((long) activeStudentIds.size());

        // 总课消课时
        BigDecimal totalConsumption = consumptions.stream()
                .map(StudentCourseConsumption::getConsumedHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalConsumption(totalConsumption);

        // 计算平均周课消和月课消
        if (stats.getActiveStudents() > 0) {
            // 平均周课消 = 总课消 / 活跃学生数 / 总周数
            BigDecimal avgWeeklyConsumption = totalConsumption
                    .divide(BigDecimal.valueOf(stats.getActiveStudents()), 4, RoundingMode.HALF_UP)
                    .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
            stats.setAvgWeeklyConsumption(avgWeeklyConsumption);

            // 平均月课消 = 总课消 / 活跃学生数 / 总月数
            BigDecimal avgMonthlyConsumption = totalConsumption
                    .divide(BigDecimal.valueOf(stats.getActiveStudents()), 4, RoundingMode.HALF_UP)
                    .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
            stats.setAvgMonthlyConsumption(avgMonthlyConsumption);
        } else {
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
            stats.setAvgMonthlyConsumption(BigDecimal.ZERO);
        }

        // 课消率
        if (stats.getTotalStudents() > 0) {
            BigDecimal consumptionRate = BigDecimal.valueOf(stats.getActiveStudents())
                    .divide(BigDecimal.valueOf(stats.getTotalStudents()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            stats.setConsumptionRate(consumptionRate);
        } else {
            stats.setConsumptionRate(BigDecimal.ZERO);
        }

        return stats;
    }

    /**
     * 计算学生统计
     */
    private List<CourseConsumptionDashboardDto.StudentConsumptionStats> calculateStudentStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        // 按学生分组
        Map<String, List<StudentCourseConsumption>> studentConsumptions = consumptions.stream()
                .collect(Collectors.groupingBy(StudentCourseConsumption::getStudentId));

        List<CourseConsumptionDashboardDto.StudentConsumptionStats> studentStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : studentConsumptions.entrySet()) {
            String studentId = entry.getKey();
            List<StudentCourseConsumption> studentConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.StudentConsumptionStats stats =
                    new CourseConsumptionDashboardDto.StudentConsumptionStats();

            // 基本信息
            stats.setStudentId(studentId);

            // 从课消记录中获取学生信息
            if (!studentConsumptionList.isEmpty()) {
                stats.setStudentName(getStudentNameById(studentId));
                stats.setStudentPhone(getStudentPhoneById(studentId));
                stats.setSalesName(getSalesNameByStudentId(studentId));
            }

            // 获取教师姓名列表
            Set<String> teacherIds = studentConsumptionList.stream()
                    .map(StudentCourseConsumption::getTeacherId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            List<String> teacherNames = getTeacherNamesByIds(teacherIds);
            stats.setTeacherNames(teacherNames);

            // 总课消课时
            BigDecimal totalConsumption = studentConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumedHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 课消次数
            stats.setConsumptionCount((long) studentConsumptionList.size());

            // 计算周课消和月课消
            BigDecimal weeklyConsumption = totalConsumption
                    .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
            stats.setWeeklyConsumption(weeklyConsumption);

            BigDecimal monthlyConsumption = totalConsumption
                    .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
            stats.setMonthlyConsumption(monthlyConsumption);

            // 最后课消时间
            Optional<Date> lastConsumptionTime = studentConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumptionTime)
                    .max(Date::compareTo);
            if (lastConsumptionTime.isPresent()) {
                stats.setLastConsumptionTime(lastConsumptionTime.get().toString());
            }

            // 学科分布
            Map<String, List<StudentCourseConsumption>> subjectGroups = studentConsumptionList.stream()
                    .collect(Collectors.groupingBy(StudentCourseConsumption::getSubject));

            List<CourseConsumptionDashboardDto.SubjectConsumption> subjectConsumptions = new ArrayList<>();
            for (Map.Entry<String, List<StudentCourseConsumption>> subjectEntry : subjectGroups.entrySet()) {
                CourseConsumptionDashboardDto.SubjectConsumption subjectConsumption =
                        new CourseConsumptionDashboardDto.SubjectConsumption();
                subjectConsumption.setSubject(subjectEntry.getKey());

                BigDecimal subjectTotal = subjectEntry.getValue().stream()
                        .map(StudentCourseConsumption::getConsumedHours)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                subjectConsumption.setConsumption(subjectTotal);
                subjectConsumption.setCount((long) subjectEntry.getValue().size());

                subjectConsumptions.add(subjectConsumption);
            }
            stats.setSubjectConsumptions(subjectConsumptions);

            studentStats.add(stats);
        }

        // 按总课消降序排序
        studentStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return studentStats;
    }

    /**
     * 根据学生ID获取学生姓名
     */
    private String getStudentNameById(String studentId) {
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();
        return student != null ? student.getName() : "";
    }

    /**
     * 根据学生ID获取学生手机号
     */
    private String getStudentPhoneById(String studentId) {
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();
        return student != null ? student.getPhone() : "";
    }

    /**
     * 根据学生ID获取销售姓名
     */
    private String getSalesNameByStudentId(String studentId) {
        UserStudentExt student = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getStudentId, studentId)
                .eq(UserStudentExt::getDeleted, false)
                .one();

        if (student != null && student.getSalesId() != null) {
            SaleProfile saleProfile = saleProfileService.lambdaQuery()
                    .eq(SaleProfile::getSalesId, student.getSalesId())
                    .eq(SaleProfile::getDeleted, false)
                    .one();
            return saleProfile != null ? saleProfile.getSalesName() : "";
        }
        return "";
    }

    /**
     * 根据教师ID列表获取教师姓名列表
     */
    private List<String> getTeacherNamesByIds(Set<String> teacherIds) {
        if (teacherIds.isEmpty()) {
            return new ArrayList<>();
        }

        return teacherProfileService.lambdaQuery()
                .in(TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .list()
                .stream()
                .map(TeacherProfile::getRealName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 计算教师统计（教学组长可见）
     */
    private List<CourseConsumptionDashboardDto.TeacherConsumptionStats> calculateTeacherStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        // 按教师分组
        Map<String, List<StudentCourseConsumption>> teacherConsumptions = consumptions.stream()
                .filter(c -> c.getTeacherId() != null)
                .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

        List<CourseConsumptionDashboardDto.TeacherConsumptionStats> teacherStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : teacherConsumptions.entrySet()) {
            String teacherId = entry.getKey();
            List<StudentCourseConsumption> teacherConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.TeacherConsumptionStats stats =
                    new CourseConsumptionDashboardDto.TeacherConsumptionStats();

            // 基本信息
            stats.setTeacherId(teacherId);
            stats.setTeacherName(getTeacherNameById(teacherId));
            stats.setTeachingGroupName(getTeachingGroupNameByTeacherId(teacherId));

            // 学生数量
            Set<String> studentIds = teacherConsumptionList.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setStudentCount((long) studentIds.size());

            // 总课消课时
            BigDecimal totalConsumption = teacherConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumedHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 课消次数
            stats.setConsumptionCount((long) teacherConsumptionList.size());

            // 生均周课消和月课消
            if (stats.getStudentCount() > 0) {
                BigDecimal avgWeeklyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);

                BigDecimal avgMonthlyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
                stats.setAvgMonthlyConsumptionPerStudent(avgMonthlyPerStudent);
            } else {
                stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
                stats.setAvgMonthlyConsumptionPerStudent(BigDecimal.ZERO);
            }

            teacherStats.add(stats);
        }

        // 按总课消降序排序
        teacherStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return teacherStats;
    }

    /**
     * 根据教师ID获取教师姓名
     */
    private String getTeacherNameById(String teacherId) {
        TeacherProfile teacher = teacherProfileService.lambdaQuery()
                .eq(TeacherProfile::getTeacherId, teacherId)
                .eq(TeacherProfile::getDeleted, false)
                .one();
        return teacher != null ? teacher.getRealName() : "";
    }

    /**
     * 根据教师ID获取教学组名称
     */
    private String getTeachingGroupNameByTeacherId(String teacherId) {
        TeachingGroupMember member = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getTeacherId, teacherId)
                .eq(TeachingGroupMember::getStatus, "active")
                .eq(TeachingGroupMember::getDeleted, false)
                .one();

        if (member != null) {
            TeachingGroup group = teachingGroupService.lambdaQuery()
                    .eq(TeachingGroup::getId, member.getGroupId())
                    .eq(TeachingGroup::getDeleted, false)
                    .one();
            return group != null ? group.getName() : "";
        }
        return "";
    }

    /**
     * 计算教学组统计（教学组长可见）
     */
    private List<CourseConsumptionDashboardDto.TeachingGroupConsumptionStats> calculateTeachingGroupStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        // 获取所有教师的教学组信息
        Map<String, String> teacherGroupMap = new HashMap<>();
        List<TeachingGroupMember> allMembers = teachingGroupMemberService.lambdaQuery()
                .eq(TeachingGroupMember::getStatus, "active")
                .eq(TeachingGroupMember::getDeleted, false)
                .list();

        for (TeachingGroupMember member : allMembers) {
            teacherGroupMap.put(member.getTeacherId(), member.getGroupId());
        }

        // 按教学组分组课消数据
        Map<String, List<StudentCourseConsumption>> groupConsumptions = new HashMap<>();
        for (StudentCourseConsumption consumption : consumptions) {
            if (consumption.getTeacherId() != null) {
                String groupId = teacherGroupMap.get(consumption.getTeacherId());
                if (groupId != null) {
                    groupConsumptions.computeIfAbsent(groupId, k -> new ArrayList<>()).add(consumption);
                }
            }
        }

        List<CourseConsumptionDashboardDto.TeachingGroupConsumptionStats> groupStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : groupConsumptions.entrySet()) {
            String groupId = entry.getKey();
            List<StudentCourseConsumption> groupConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.TeachingGroupConsumptionStats stats =
                    new CourseConsumptionDashboardDto.TeachingGroupConsumptionStats();

            // 基本信息
            stats.setTeachingGroupId(groupId);
            stats.setTeachingGroupName(getTeachingGroupNameById(groupId));

            // 教师数量
            Set<String> teacherIds = groupConsumptionList.stream()
                    .map(StudentCourseConsumption::getTeacherId)
                    .collect(Collectors.toSet());
            stats.setTeacherCount((long) teacherIds.size());

            // 学生数量
            Set<String> studentIds = groupConsumptionList.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setStudentCount((long) studentIds.size());

            // 总课消课时
            BigDecimal totalConsumption = groupConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumedHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 生均周课消和月课消
            if (stats.getStudentCount() > 0) {
                BigDecimal avgWeeklyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);

                BigDecimal avgMonthlyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
                stats.setAvgMonthlyConsumptionPerStudent(avgMonthlyPerStudent);
            } else {
                stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
                stats.setAvgMonthlyConsumptionPerStudent(BigDecimal.ZERO);
            }

            // 师均课消
            if (stats.getTeacherCount() > 0) {
                BigDecimal avgConsumptionPerTeacher = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getTeacherCount()), 2, RoundingMode.HALF_UP);
                stats.setAvgConsumptionPerTeacher(avgConsumptionPerTeacher);
            } else {
                stats.setAvgConsumptionPerTeacher(BigDecimal.ZERO);
            }

            groupStats.add(stats);
        }

        // 按总课消降序排序
        groupStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return groupStats;
    }

    /**
     * 根据教学组ID获取教学组名称
     */
    private String getTeachingGroupNameById(String groupId) {
        TeachingGroup group = teachingGroupService.lambdaQuery()
                .eq(TeachingGroup::getId, groupId)
                .eq(TeachingGroup::getDeleted, false)
                .one();
        return group != null ? group.getName() : "";
    }

    /**
     * 计算销售统计（销售组长和销售总监可见）
     */
    private List<CourseConsumptionDashboardDto.SalesConsumptionStats> calculateSalesStats(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        // 获取所有学生的销售信息
        Set<String> studentIds = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        Map<String, String> studentSalesMap = new HashMap<>();
        if (!studentIds.isEmpty()) {
            List<UserStudentExt> students = userStudentExtService.lambdaQuery()
                    .in(UserStudentExt::getStudentId, studentIds)
                    .eq(UserStudentExt::getDeleted, false)
                    .list();

            for (UserStudentExt student : students) {
                if (student.getSalesId() != null) {
                    studentSalesMap.put(student.getStudentId(), student.getSalesId());
                }
            }
        }

        // 按销售分组课消数据
        Map<String, List<StudentCourseConsumption>> salesConsumptions = new HashMap<>();
        for (StudentCourseConsumption consumption : consumptions) {
            String salesId = studentSalesMap.get(consumption.getStudentId());
            if (salesId != null) {
                salesConsumptions.computeIfAbsent(salesId, k -> new ArrayList<>()).add(consumption);
            }
        }

        List<CourseConsumptionDashboardDto.SalesConsumptionStats> salesStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : salesConsumptions.entrySet()) {
            String salesId = entry.getKey();
            List<StudentCourseConsumption> salesConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.SalesConsumptionStats stats =
                    new CourseConsumptionDashboardDto.SalesConsumptionStats();

            // 基本信息
            stats.setSalesId(salesId);
            stats.setSalesName(getSalesNameById(salesId));
            stats.setSalesGroupName(getSalesGroupNameBySalesId(salesId));

            // 学生数量
            Set<String> salesStudentIds = salesConsumptionList.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setStudentCount((long) salesStudentIds.size());

            // 活跃学生数（有课消的学生）
            Set<String> activeStudentIds = salesConsumptionList.stream()
                    .filter(c -> c.getConsumedHours().compareTo(BigDecimal.ZERO) > 0)
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setActiveStudents((long) activeStudentIds.size());

            // 总课消课时
            BigDecimal totalConsumption = salesConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumedHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 生均周课消和月课消
            if (stats.getActiveStudents() > 0) {
                BigDecimal avgWeeklyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getActiveStudents()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);

                BigDecimal avgMonthlyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getActiveStudents()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
                stats.setAvgMonthlyConsumptionPerStudent(avgMonthlyPerStudent);
            } else {
                stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
                stats.setAvgMonthlyConsumptionPerStudent(BigDecimal.ZERO);
            }

            // 课消率
            if (stats.getStudentCount() > 0) {
                BigDecimal consumptionRate = BigDecimal.valueOf(stats.getActiveStudents())
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                stats.setConsumptionRate(consumptionRate);
            } else {
                stats.setConsumptionRate(BigDecimal.ZERO);
            }

            salesStats.add(stats);
        }

        // 按总课消降序排序
        salesStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return salesStats;
    }

    /**
     * 根据销售ID获取销售姓名
     */
    private String getSalesNameById(String salesId) {
        SaleProfile saleProfile = saleProfileService.lambdaQuery()
                .eq(SaleProfile::getSalesId, salesId)
                .eq(SaleProfile::getDeleted, false)
                .one();
        return saleProfile != null ? saleProfile.getSalesName() : "";
    }

    /**
     * 根据销售ID获取销售组名称
     */
    private String getSalesGroupNameBySalesId(String salesId) {
        SalesGroupMember member = salesGroupMemberService.lambdaQuery()
                .eq(SalesGroupMember::getSalesId, salesId)
                .eq(SalesGroupMember::getStatus, "active")
                .eq(SalesGroupMember::getDeleted, false)
                .one();

        if (member != null) {
            SalesGroup group = salesGroupService.lambdaQuery()
                    .eq(SalesGroup::getId, member.getGroupId())
                    .eq(SalesGroup::getDeleted, false)
                    .one();
            return group != null ? group.getName() : "";
        }
        return "";
    }

    // ==================== 高性能优化方法 ====================

    /**
     * 快速获取课消数据（优化版本）
     */
    private List<StudentCourseConsumption> getConsumptionDataFast(CourseConsumptionDashboardDto.GetDashboardReq req) {
        log.info("开始快速查询课消数据，时间范围: {} 到 {}", req.getStartDate(), req.getEndDate());

        var queryWrapper = studentCourseConsumptionService.lambdaQuery()
                .eq(StudentCourseConsumption::getDeleted, false)
                .eq(StudentCourseConsumption::getStatus, "active")
                .ge(StudentCourseConsumption::getConsumptionTime, req.getStartDate() + " 00:00:00")
                .le(StudentCourseConsumption::getConsumptionTime, req.getEndDate() + " 23:59:59");

        // 应用过滤条件
        if (req.getSubject() != null && !req.getSubject().isEmpty()) {
            queryWrapper.eq(StudentCourseConsumption::getSubject, req.getSubject());
        }

        if (req.getSpecification() != null && !req.getSpecification().isEmpty()) {
            queryWrapper.eq(StudentCourseConsumption::getSpecification, req.getSpecification());
        }

        if (req.getStudentId() != null && !req.getStudentId().isEmpty()) {
            if ("NONE".equals(req.getStudentId())) {
                return new ArrayList<>();
            }
            queryWrapper.eq(StudentCourseConsumption::getStudentId, req.getStudentId());
        }

        if (req.getTeacherId() != null && !req.getTeacherId().isEmpty()) {
            if ("NONE".equals(req.getTeacherId())) {
                return new ArrayList<>();
            }
            queryWrapper.eq(StudentCourseConsumption::getTeacherId, req.getTeacherId());
        }

        // 应用教学组过滤
        if (req.getTeachingGroupId() != null && !req.getTeachingGroupId().isEmpty()) {
            List<String> teacherIds = getTeacherIdsByGroupId(req.getTeachingGroupId());
            if (teacherIds.isEmpty()) {
                return new ArrayList<>();
            }
            queryWrapper.in(StudentCourseConsumption::getTeacherId, teacherIds);
        }

        // 执行查询，限制数量
        List<StudentCourseConsumption> consumptions = queryWrapper
                .orderByDesc(StudentCourseConsumption::getConsumptionTime)
                .last("LIMIT 5000") // 减少限制数量
                .list();

        // 应用销售过滤（如果需要）
        if (req.getSalesId() != null && !req.getSalesId().isEmpty()) {
            if ("NONE".equals(req.getSalesId())) {
                return new ArrayList<>();
            }
            consumptions = filterBySalesIdFast(consumptions, req.getSalesId());
        }

        if (req.getSalesGroupId() != null && !req.getSalesGroupId().isEmpty()) {
            List<String> salesIds = getSalesIdsByGroupId(req.getSalesGroupId());
            if (salesIds.isEmpty()) {
                return new ArrayList<>();
            }
            consumptions = filterBySalesIdsFast(consumptions, salesIds);
        }

        return consumptions;
    }

    /**
     * 创建空响应
     */
    private CourseConsumptionDashboardDto.DashboardResp createEmptyResponse(CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {
        CourseConsumptionDashboardDto.DashboardResp response = new CourseConsumptionDashboardDto.DashboardResp();
        response.setTimeRangeInfo(timeRangeInfo);

        // 创建空的统计数据
        CourseConsumptionDashboardDto.OverallStats emptyStats = new CourseConsumptionDashboardDto.OverallStats();
        emptyStats.setTotalStudents(0L);
        emptyStats.setActiveStudents(0L);
        emptyStats.setTotalConsumption(BigDecimal.ZERO);
        emptyStats.setAvgWeeklyConsumption(BigDecimal.ZERO);
        emptyStats.setAvgMonthlyConsumption(BigDecimal.ZERO);
        emptyStats.setConsumptionRate(BigDecimal.ZERO);
        response.setOverallStats(emptyStats);

        response.setStudentStats(new ArrayList<>());
        response.setTeacherStats(new ArrayList<>());
        response.setSalesStats(new ArrayList<>());
        response.setTeachingGroupStats(new ArrayList<>());

        return response;
    }

    /**
     * 快速销售过滤
     */
    private List<StudentCourseConsumption> filterBySalesIdFast(List<StudentCourseConsumption> consumptions, String salesId) {
        if (consumptions.isEmpty()) {
            return consumptions;
        }

        Set<String> studentIds = userStudentExtService.lambdaQuery()
                .eq(UserStudentExt::getSalesId, salesId)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .map(UserStudentExt::getStudentId)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new ArrayList<>();
        }

        return consumptions.stream()
                .filter(c -> studentIds.contains(c.getStudentId()))
                .collect(Collectors.toList());
    }

    /**
     * 快速批量销售过滤
     */
    private List<StudentCourseConsumption> filterBySalesIdsFast(List<StudentCourseConsumption> consumptions, List<String> salesIds) {
        if (consumptions.isEmpty() || salesIds.isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> studentIds = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getSalesId, salesIds)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .map(UserStudentExt::getStudentId)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new ArrayList<>();
        }

        return consumptions.stream()
                .filter(c -> studentIds.contains(c.getStudentId()))
                .collect(Collectors.toList());
    }

    /**
     * 批量获取学生姓名映射
     */
    private Map<String, String> getStudentNamesMap(List<StudentCourseConsumption> consumptions) {
        Set<String> studentIds = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new HashMap<>();
        }

        return userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        UserStudentExt::getStudentId,
                        UserStudentExt::getName,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 批量获取学生手机号映射
     */
    private Map<String, String> getStudentPhonesMap(List<StudentCourseConsumption> consumptions) {
        Set<String> studentIds = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new HashMap<>();
        }

        return userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        UserStudentExt::getStudentId,
                        UserStudentExt::getPhone,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 批量获取学生销售映射
     */
    private Map<String, String> getStudentSalesMap(List<StudentCourseConsumption> consumptions) {
        Set<String> studentIds = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (studentIds.isEmpty()) {
            return new HashMap<>();
        }

        // 获取学生-销售关系
        Map<String, String> studentSalesIdMap = userStudentExtService.lambdaQuery()
                .in(UserStudentExt::getStudentId, studentIds)
                .eq(UserStudentExt::getDeleted, false)
                .list()
                .stream()
                .filter(s -> s.getSalesId() != null)
                .collect(Collectors.toMap(
                        UserStudentExt::getStudentId,
                        UserStudentExt::getSalesId,
                        (existing, replacement) -> existing
                ));

        if (studentSalesIdMap.isEmpty()) {
            return new HashMap<>();
        }

        // 获取销售姓名
        Set<String> salesIds = new HashSet<>(studentSalesIdMap.values());
        Map<String, String> salesNameMap = saleProfileService.lambdaQuery()
                .in(SaleProfile::getSalesId, salesIds)
                .eq(SaleProfile::getDeleted, false)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        SaleProfile::getSalesId,
                        SaleProfile::getSalesName,
                        (existing, replacement) -> existing
                ));

        // 组合结果
        Map<String, String> result = new HashMap<>();
        studentSalesIdMap.forEach((studentId, salesId) -> {
            String salesName = salesNameMap.get(salesId);
            if (salesName != null) {
                result.put(studentId, salesName);
            }
        });

        return result;
    }

    /**
     * 批量获取教师姓名映射
     */
    private Map<String, String> getTeacherNamesMap(List<StudentCourseConsumption> consumptions) {
        Set<String> teacherIds = consumptions.stream()
                .map(StudentCourseConsumption::getTeacherId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (teacherIds.isEmpty()) {
            return new HashMap<>();
        }

        return teacherProfileService.lambdaQuery()
                .in(TeacherProfile::getTeacherId, teacherIds)
                .eq(TeacherProfile::getDeleted, false)
                .list()
                .stream()
                .collect(Collectors.toMap(
                        TeacherProfile::getTeacherId,
                        TeacherProfile::getRealName,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 快速计算总体统计
     */
    private CourseConsumptionDashboardDto.OverallStats calculateOverallStatsFast(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo) {

        CourseConsumptionDashboardDto.OverallStats stats = new CourseConsumptionDashboardDto.OverallStats();

        if (consumptions.isEmpty()) {
            stats.setTotalStudents(0L);
            stats.setActiveStudents(0L);
            stats.setTotalConsumption(BigDecimal.ZERO);
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
            stats.setAvgMonthlyConsumption(BigDecimal.ZERO);
            stats.setConsumptionRate(BigDecimal.ZERO);
            return stats;
        }

        // 统计学生数量
        Set<String> allStudents = consumptions.stream()
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        Set<String> activeStudents = consumptions.stream()
                .filter(c -> c.getConsumedHours() != null && c.getConsumedHours().compareTo(BigDecimal.ZERO) > 0)
                .map(StudentCourseConsumption::getStudentId)
                .collect(Collectors.toSet());

        // 计算总课消
        BigDecimal totalConsumption = consumptions.stream()
                .map(c -> c.getConsumedHours() != null ? c.getConsumedHours() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        stats.setTotalStudents((long) allStudents.size());
        stats.setActiveStudents((long) activeStudents.size());
        stats.setTotalConsumption(totalConsumption);

        // 计算平均课消
        if (timeRangeInfo.getTotalWeeks().compareTo(BigDecimal.ZERO) > 0) {
            stats.setAvgWeeklyConsumption(totalConsumption.divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP));
        } else {
            stats.setAvgWeeklyConsumption(BigDecimal.ZERO);
        }

        if (timeRangeInfo.getTotalMonths().compareTo(BigDecimal.ZERO) > 0) {
            stats.setAvgMonthlyConsumption(totalConsumption.divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP));
        } else {
            stats.setAvgMonthlyConsumption(BigDecimal.ZERO);
        }

        // 计算课消率
        if (allStudents.size() > 0) {
            BigDecimal consumptionRate = BigDecimal.valueOf(activeStudents.size())
                    .divide(BigDecimal.valueOf(allStudents.size()), 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100));
            stats.setConsumptionRate(consumptionRate);
        } else {
            stats.setConsumptionRate(BigDecimal.ZERO);
        }

        return stats;
    }

    /**
     * 快速计算学生统计
     */
    private List<CourseConsumptionDashboardDto.StudentConsumptionStats> calculateStudentStatsFast(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo,
            Map<String, String> studentNameMap,
            Map<String, String> studentPhoneMap,
            Map<String, String> studentSalesMap,
            Map<String, String> teacherNameMap) {

        // 按学生分组
        Map<String, List<StudentCourseConsumption>> studentConsumptions = consumptions.stream()
                .collect(Collectors.groupingBy(StudentCourseConsumption::getStudentId));

        List<CourseConsumptionDashboardDto.StudentConsumptionStats> studentStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : studentConsumptions.entrySet()) {
            String studentId = entry.getKey();
            List<StudentCourseConsumption> studentConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.StudentConsumptionStats stats =
                    new CourseConsumptionDashboardDto.StudentConsumptionStats();

            // 基本信息（从Map中获取，避免重复查询）
            stats.setStudentId(studentId);
            stats.setStudentName(studentNameMap.getOrDefault(studentId, ""));
            stats.setStudentPhone(studentPhoneMap.getOrDefault(studentId, ""));
            stats.setSalesName(studentSalesMap.getOrDefault(studentId, ""));

            // 计算课消统计
            BigDecimal totalConsumption = studentConsumptionList.stream()
                    .map(c -> c.getConsumedHours() != null ? c.getConsumedHours() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 计算周课消和月课消
            if (timeRangeInfo.getTotalWeeks().compareTo(BigDecimal.ZERO) > 0) {
                stats.setWeeklyConsumption(totalConsumption.divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP));
            } else {
                stats.setWeeklyConsumption(BigDecimal.ZERO);
            }

            if (timeRangeInfo.getTotalMonths().compareTo(BigDecimal.ZERO) > 0) {
                stats.setMonthlyConsumption(totalConsumption.divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP));
            } else {
                stats.setMonthlyConsumption(BigDecimal.ZERO);
            }

            // 课消次数
            stats.setConsumptionCount((long) studentConsumptionList.size());

            // 最后课消时间
            Date lastTime = studentConsumptionList.stream()
                    .map(StudentCourseConsumption::getConsumptionTime)
                    .filter(Objects::nonNull)
                    .max(Date::compareTo)
                    .orElse(null);
            stats.setLastConsumptionTime(lastTime != null ? lastTime.toString() : null);

            // 授课教师（从Map中获取，避免重复查询）
            List<String> teacherNames = studentConsumptionList.stream()
                    .map(StudentCourseConsumption::getTeacherId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .map(teacherId -> teacherNameMap.getOrDefault(teacherId, ""))
                    .filter(name -> !name.isEmpty())
                    .collect(Collectors.toList());
            stats.setTeacherNames(teacherNames);

            // 学科课消分布（简化版本）
            Map<String, BigDecimal> subjectMap = studentConsumptionList.stream()
                    .filter(c -> c.getSubject() != null)
                    .collect(Collectors.groupingBy(
                            StudentCourseConsumption::getSubject,
                            Collectors.reducing(BigDecimal.ZERO,
                                    c -> c.getConsumedHours() != null ? c.getConsumedHours() : BigDecimal.ZERO,
                                    BigDecimal::add)
                    ));

            List<CourseConsumptionDashboardDto.SubjectConsumption> subjectConsumptions = subjectMap.entrySet().stream()
                    .map(e -> {
                        CourseConsumptionDashboardDto.SubjectConsumption sc = new CourseConsumptionDashboardDto.SubjectConsumption();
                        sc.setSubject(e.getKey());
                        sc.setConsumption(e.getValue());
                        return sc;
                    })
                    .collect(Collectors.toList());
            stats.setSubjectConsumptions(subjectConsumptions);

            studentStats.add(stats);
        }

        // 按总课消降序排序
        studentStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return studentStats;
    }

    /**
     * 快速计算教师统计（简化版本）
     */
    private List<CourseConsumptionDashboardDto.TeacherConsumptionStats> calculateTeacherStatsFast(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo,
            Map<String, String> teacherNameMap) {

        // 按教师分组
        Map<String, List<StudentCourseConsumption>> teacherConsumptions = consumptions.stream()
                .filter(c -> c.getTeacherId() != null)
                .collect(Collectors.groupingBy(StudentCourseConsumption::getTeacherId));

        List<CourseConsumptionDashboardDto.TeacherConsumptionStats> teacherStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : teacherConsumptions.entrySet()) {
            String teacherId = entry.getKey();
            List<StudentCourseConsumption> teacherConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.TeacherConsumptionStats stats =
                    new CourseConsumptionDashboardDto.TeacherConsumptionStats();

            stats.setTeacherId(teacherId);
            stats.setTeacherName(teacherNameMap.getOrDefault(teacherId, ""));

            // 学生数量
            Set<String> studentIds = teacherConsumptionList.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setStudentCount((long) studentIds.size());

            // 总课消课时
            BigDecimal totalConsumption = teacherConsumptionList.stream()
                    .map(c -> c.getConsumedHours() != null ? c.getConsumedHours() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 课消次数
            stats.setConsumptionCount((long) teacherConsumptionList.size());

            // 生均周课消和月课消
            if (stats.getStudentCount() > 0) {
                BigDecimal avgWeeklyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalWeeks(), 2, RoundingMode.HALF_UP);
                stats.setAvgWeeklyConsumptionPerStudent(avgWeeklyPerStudent);

                BigDecimal avgMonthlyPerStudent = totalConsumption
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .divide(timeRangeInfo.getTotalMonths(), 2, RoundingMode.HALF_UP);
                stats.setAvgMonthlyConsumptionPerStudent(avgMonthlyPerStudent);
            } else {
                stats.setAvgWeeklyConsumptionPerStudent(BigDecimal.ZERO);
                stats.setAvgMonthlyConsumptionPerStudent(BigDecimal.ZERO);
            }

            teacherStats.add(stats);
        }

        // 按总课消降序排序
        teacherStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return teacherStats;
    }

    /**
     * 快速计算销售统计（简化版本）
     */
    private List<CourseConsumptionDashboardDto.SalesConsumptionStats> calculateSalesStatsFast(
            List<StudentCourseConsumption> consumptions,
            CourseConsumptionDashboardDto.TimeRangeInfo timeRangeInfo,
            Map<String, String> studentSalesMap) {

        // 按销售分组课消数据
        Map<String, List<StudentCourseConsumption>> salesConsumptions = new HashMap<>();
        for (StudentCourseConsumption consumption : consumptions) {
            String salesName = studentSalesMap.get(consumption.getStudentId());
            if (salesName != null) {
                salesConsumptions.computeIfAbsent(salesName, k -> new ArrayList<>()).add(consumption);
            }
        }

        List<CourseConsumptionDashboardDto.SalesConsumptionStats> salesStats = new ArrayList<>();

        for (Map.Entry<String, List<StudentCourseConsumption>> entry : salesConsumptions.entrySet()) {
            String salesName = entry.getKey();
            List<StudentCourseConsumption> salesConsumptionList = entry.getValue();

            CourseConsumptionDashboardDto.SalesConsumptionStats stats =
                    new CourseConsumptionDashboardDto.SalesConsumptionStats();

            stats.setSalesName(salesName);

            // 学生数量
            Set<String> salesStudentIds = salesConsumptionList.stream()
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setStudentCount((long) salesStudentIds.size());

            // 活跃学生数（有课消的学生）
            Set<String> activeStudentIds = salesConsumptionList.stream()
                    .filter(c -> c.getConsumedHours() != null && c.getConsumedHours().compareTo(BigDecimal.ZERO) > 0)
                    .map(StudentCourseConsumption::getStudentId)
                    .collect(Collectors.toSet());
            stats.setActiveStudents((long) activeStudentIds.size());

            // 总课消课时
            BigDecimal totalConsumption = salesConsumptionList.stream()
                    .map(c -> c.getConsumedHours() != null ? c.getConsumedHours() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalConsumption(totalConsumption);

            // 课消率
            if (stats.getStudentCount() > 0) {
                BigDecimal consumptionRate = BigDecimal.valueOf(stats.getActiveStudents())
                        .divide(BigDecimal.valueOf(stats.getStudentCount()), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
                stats.setConsumptionRate(consumptionRate);
            } else {
                stats.setConsumptionRate(BigDecimal.ZERO);
            }

            salesStats.add(stats);
        }

        // 按总课消降序排序
        salesStats.sort((a, b) -> b.getTotalConsumption().compareTo(a.getTotalConsumption()));

        return salesStats;
    }
}

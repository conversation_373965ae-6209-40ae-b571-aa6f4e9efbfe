package org.nonamespace.word.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.nonamespace.word.server.domain.CourseBookingApplicationReview;

import java.util.List;

/**
 * 预约课申请审核记录Service接口
 * 
 * 注意：Service定位为数据层服务，不在这边做业务耦合
 * 业务逻辑在Facade层实现
 *
 * <AUTHOR> Assistant
 * @date 2025-01-12
 */
public interface ICourseBookingApplicationReviewService extends IService<CourseBookingApplicationReview> {

    /**
     * 根据申请ID获取所有审核记录
     *
     * @param applicationId 申请ID
     * @return 审核记录列表
     */
    List<CourseBookingApplicationReview> getReviewsByApplicationId(String applicationId);

    /**
     * 根据申请ID和教学组ID获取审核记录
     *
     * @param applicationId 申请ID
     * @param teachingGroupId 教学组ID
     * @return 审核记录
     */
    CourseBookingApplicationReview getReviewByApplicationAndGroup(String applicationId, String teachingGroupId);

    /**
     * 检查申请是否已被某个教学组审核过
     *
     * @param applicationId 申请ID
     * @param teachingGroupId 教学组ID
     * @return 是否已审核
     */
    boolean hasReviewed(String applicationId, String teachingGroupId);

    /**
     * 获取申请的所有通过审核记录
     *
     * @param applicationId 申请ID
     * @return 通过的审核记录列表
     */
    List<CourseBookingApplicationReview> getApprovedReviews(String applicationId);

    /**
     * 获取申请的所有拒绝审核记录
     *
     * @param applicationId 申请ID
     * @return 拒绝的审核记录列表
     */
    List<CourseBookingApplicationReview> getRejectedReviews(String applicationId);
}

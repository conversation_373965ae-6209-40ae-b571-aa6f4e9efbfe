package org.nonamespace.word.server.facade;

import org.nonamespace.word.server.dto.course.CourseConsumptionDashboardDto;

/**
 * 课消看板Facade接口
 * 
 * <AUTHOR>
 * @date 2025-01-09
 */
public interface ICourseConsumptionDashboardFacade {

    /**
     * 获取课消看板数据
     *
     * @param req 查询请求参数
     * @return 课消看板数据
     */
    CourseConsumptionDashboardDto.DashboardResp getDashboardData(CourseConsumptionDashboardDto.GetDashboardReq req);
}

package org.nonamespace.word.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.nonamespace.word.server.domain.CourseBookingApplicationReview;

/**
 * 预约课申请审核记录Mapper接口
 * 
 * 注意：遵循编码规范，尽量使用Service层方法，避免在Mapper中编写复杂SQL
 *
 * <AUTHOR> Assistant
 * @date 2025-01-12
 */
@Mapper
public interface CourseBookingApplicationReviewMapper extends BaseMapper<CourseBookingApplicationReview> {

    // 基础的CRUD操作通过BaseMapper提供
    // 复杂查询通过Service层的lambdaQuery()或MPJLambdaWrapper实现
}

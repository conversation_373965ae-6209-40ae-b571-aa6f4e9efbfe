-- 为course表添加消课相关字段
-- 作者: system
-- 日期: 2025-01-15

-- 添加消课方式字段
ALTER TABLE course ADD COLUMN IF NOT EXISTS consumption_method VARCHAR(20) DEFAULT '系统消课';

-- 添加消课信息字段（JSON格式）
ALTER TABLE course ADD COLUMN IF NOT EXISTS consumption_info JSONB;

-- 为现有记录设置默认值
UPDATE course SET consumption_method = '系统消课' WHERE consumption_method IS NULL;

-- 添加字段注释
COMMENT ON COLUMN course.consumption_method IS '消课方式 (系统消课、人工消课)';
COMMENT ON COLUMN course.consumption_info IS '消课信息 (JSON格式，存储人工消课的详细信息)';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_course_consumption_method ON course(consumption_method);

-- 课消看板菜单和权限配置
-- 作者: system
-- 日期: 2025-01-09

-- 1. 添加课消看板菜单
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (
    nextval('sys_menu_seq'),
    '课消看板',
    (SELECT menu_id FROM sys_menu WHERE menu_name = '课程管理' AND parent_id != 0 LIMIT 1),
    5,
    'consumption-dashboard',
    'course/consumption-dashboard/index',
    '',
    1,
    0,
    'C',
    '0',
    '0',
    'course:consumption:dashboard:view',
    'chart',
    'admin',
    NOW(),
    'admin',
    NOW(),
    '学生课消看板菜单'
);

-- 2. 添加课消看板相关权限按钮
-- 查看权限（已在菜单中包含）
-- 导出权限
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES (
    nextval('sys_menu_seq'),
    '课消看板导出',
    (SELECT menu_id FROM sys_menu WHERE perms = 'course:consumption:dashboard:view' LIMIT 1),
    1,
    '',
    '',
    '',
    1,
    0,
    'F',
    '0',
    '0',
    'course:consumption:dashboard:export',
    '#',
    'admin',
    NOW(),
    'admin',
    NOW(),
    '课消看板导出按钮'
);

-- 3. 为不同角色分配权限
-- 管理员角色 (admin)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'admin'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 人力角色 (hr)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'hr'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 销售总监角色 (sales_director)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales_director'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 销售组长角色 (sales_group_leader)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales_group_leader'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 销售角色 (sales)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales'
  AND m.perms IN ('course:consumption:dashboard:view')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 教学组长角色 (teaching_group_leader)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teaching_group_leader'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 教学组教务角色 (teaching_group_admin)
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teaching_group_admin'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 教师角色 (teacher) - 只能查看，不能导出
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teacher'
  AND m.perms IN ('course:consumption:dashboard:view')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 4. 添加数据权限配置说明注释
/*
数据权限说明：
1. admin/hr: 可以查看所有数据
2. sales_director: 可以查看所有销售数据
3. sales_group_leader: 只能查看本组销售数据
4. sales: 只能查看自己的学生数据
5. teaching_group_leader: 只能查看本组教师数据
6. teaching_group_admin: 只能查看本组教师数据
7. teacher: 只能查看自己的学生数据

权限控制在后端Facade层实现，通过SystemDataQueryUtil进行角色判断和数据过滤。
*/

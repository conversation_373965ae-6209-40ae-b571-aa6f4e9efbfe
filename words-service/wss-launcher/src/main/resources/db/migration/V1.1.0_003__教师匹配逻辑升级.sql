-- =====================================================
-- 教师匹配逻辑升级数据库变更
-- 创建时间：2025-01-12
-- 说明：新增试听课时间字段，支持试听课时间匹配功能
-- =====================================================

-- 1. 在course_booking_application表中新增试听课时间相关字段
ALTER TABLE course_booking_application 
ADD COLUMN trial_class_date DATE,
ADD COLUMN trial_class_start_time TIME,
ADD COLUMN trial_class_end_time TIME;

-- 2. 添加字段注释
COMMENT ON COLUMN course_booking_application.trial_class_date IS '试听课日期';
COMMENT ON COLUMN course_booking_application.trial_class_start_time IS '试听课开始时间';
COMMENT ON COLUMN course_booking_application.trial_class_end_time IS '试听课结束时间';

-- 3. 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_course_booking_trial_date 
    ON course_booking_application(trial_class_date);

CREATE INDEX IF NOT EXISTS idx_course_booking_trial_time 
    ON course_booking_application(trial_class_date, trial_class_start_time, trial_class_end_time);

-- 4. 添加数据约束
-- 确保试听课时间的逻辑一致性
ALTER TABLE course_booking_application 
ADD CONSTRAINT chk_trial_class_time_consistency 
CHECK (
    (trial_class_date IS NULL AND trial_class_start_time IS NULL AND trial_class_end_time IS NULL) OR
    (trial_class_date IS NOT NULL AND trial_class_start_time IS NOT NULL AND trial_class_end_time IS NOT NULL AND trial_class_start_time < trial_class_end_time)
);

-- 5. 添加业务约束注释
COMMENT ON CONSTRAINT chk_trial_class_time_consistency ON course_booking_application IS 
'试听课时间一致性约束：要么全部为空，要么全部不为空且开始时间小于结束时间';

-- 6. 为现有数据兼容性创建视图（可选）
-- 创建一个视图来兼容旧的查询逻辑
CREATE OR REPLACE VIEW course_booking_application_with_trial_info AS
SELECT 
    *,
    CASE 
        WHEN trial_class_date IS NOT NULL 
        THEN CONCAT(trial_class_date, ' ', trial_class_start_time, '-', trial_class_end_time)
        ELSE NULL 
    END AS trial_class_time_display
FROM course_booking_application;

COMMENT ON VIEW course_booking_application_with_trial_info IS 
'预约课申请视图，包含试听课时间显示字段，用于兼容性查询';

-- 7. 创建用于教师匹配的辅助函数（可选）
-- 检查时间段是否重叠的函数
CREATE OR REPLACE FUNCTION check_time_overlap(
    start1 TIME, end1 TIME,
    start2 TIME, end2 TIME
) RETURNS BOOLEAN AS $$
BEGIN
    RETURN NOT (end1 <= start2 OR end2 <= start1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION check_time_overlap(TIME, TIME, TIME, TIME) IS 
'检查两个时间段是否重叠，用于教师匹配逻辑';

-- 8. 创建试听课时间查询的辅助函数
CREATE OR REPLACE FUNCTION has_trial_class_conflict(
    teacher_id_param VARCHAR,
    trial_date_param DATE,
    trial_start_param TIME,
    trial_end_param TIME
) RETURNS BOOLEAN AS $$
DECLARE
    conflict_count INTEGER;
BEGIN
    -- 检查教师在指定日期时间段是否有冲突的课程
    -- 这里需要根据实际的课程表结构来实现
    -- 暂时返回false，具体实现在Service层
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION has_trial_class_conflict(VARCHAR, DATE, TIME, TIME) IS 
'检查教师在指定试听课时间是否有冲突，具体逻辑在Service层实现';

-- 9. 更新统计相关的视图或函数（如果存在）
-- 如果有统计试听课的需求，可以在这里添加相关的视图

-- 10. 创建数据迁移的辅助脚本（可选）
-- 为了帮助现有数据的迁移，可以创建一些辅助脚本

-- 执行完成提示
SELECT '教师匹配逻辑升级数据库变更执行完成！新增试听课时间字段和相关约束。' as result;

-- 11. 性能优化建议
-- 如果预约课申请表数据量很大，建议：
-- 1. 在业务低峰期执行此迁移
-- 2. 监控索引创建的性能影响
-- 3. 考虑分批更新现有数据（如果需要）

-- 12. 回滚脚本（紧急情况使用）
/*
-- 如果需要回滚，可以执行以下脚本：
DROP VIEW IF EXISTS course_booking_application_with_trial_info;
DROP FUNCTION IF EXISTS check_time_overlap(TIME, TIME, TIME, TIME);
DROP FUNCTION IF EXISTS has_trial_class_conflict(VARCHAR, DATE, TIME, TIME);
ALTER TABLE course_booking_application 
    DROP CONSTRAINT IF EXISTS chk_trial_class_time_consistency,
    DROP COLUMN IF EXISTS trial_class_date,
    DROP COLUMN IF EXISTS trial_class_start_time,
    DROP COLUMN IF EXISTS trial_class_end_time;
*/

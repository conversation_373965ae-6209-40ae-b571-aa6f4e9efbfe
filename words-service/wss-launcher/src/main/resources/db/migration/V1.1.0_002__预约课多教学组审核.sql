-- =====================================================
-- 预约课多教学组审核功能数据库变更
-- 创建时间：2025-01-12
-- 说明：支持多教学组分别审核预约课申请的功能
-- =====================================================

-- 1. 创建预约课申请审核记录表
CREATE TABLE IF NOT EXISTS course_booking_application_review (
    id VARCHAR(50) PRIMARY KEY,
    application_id VARCHAR(50) NOT NULL,
    teaching_group_id VARCHAR(50) NOT NULL,
    reviewer_id VARCHAR(50) NOT NULL,
    review_result VARCHAR(20) NOT NULL, -- '已通过', '已拒绝'
    review_comment TEXT,
    rejection_reason TEXT,
    assigned_teacher_id VARCHAR(50),
    assigned_time_slot TEXT, -- JSON格式的时间段信息
    review_time TIMESTAMP NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE
);

-- 2. 添加表注释
COMMENT ON TABLE course_booking_application_review IS '预约课申请审核记录表';
COMMENT ON COLUMN course_booking_application_review.id IS '主键ID';
COMMENT ON COLUMN course_booking_application_review.application_id IS '申请ID';
COMMENT ON COLUMN course_booking_application_review.teaching_group_id IS '教学组ID';
COMMENT ON COLUMN course_booking_application_review.reviewer_id IS '审核人ID';
COMMENT ON COLUMN course_booking_application_review.review_result IS '审核结果：已通过、已拒绝';
COMMENT ON COLUMN course_booking_application_review.review_comment IS '审核意见';
COMMENT ON COLUMN course_booking_application_review.rejection_reason IS '拒绝原因';
COMMENT ON COLUMN course_booking_application_review.assigned_teacher_id IS '分配的教师ID（通过时）';
COMMENT ON COLUMN course_booking_application_review.assigned_time_slot IS '分配的时间段（JSON格式）';
COMMENT ON COLUMN course_booking_application_review.review_time IS '审核时间';
COMMENT ON COLUMN course_booking_application_review.create_time IS '创建时间';
COMMENT ON COLUMN course_booking_application_review.update_time IS '更新时间';
COMMENT ON COLUMN course_booking_application_review.deleted IS '是否删除';

-- 3. 创建索引
CREATE INDEX IF NOT EXISTS idx_course_booking_review_application_id 
    ON course_booking_application_review(application_id);
CREATE INDEX IF NOT EXISTS idx_course_booking_review_teaching_group_id 
    ON course_booking_application_review(teaching_group_id);
CREATE INDEX IF NOT EXISTS idx_course_booking_review_reviewer_id 
    ON course_booking_application_review(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_course_booking_review_result 
    ON course_booking_application_review(review_result);
CREATE INDEX IF NOT EXISTS idx_course_booking_review_time 
    ON course_booking_application_review(review_time);

-- 4. 创建唯一约束（一个申请在一个教学组只能有一条审核记录）
CREATE UNIQUE INDEX IF NOT EXISTS uk_course_booking_review_app_group 
    ON course_booking_application_review(application_id, teaching_group_id) 
    WHERE deleted = FALSE;

-- 5. 添加外键约束（可选，根据实际需要）
-- ALTER TABLE course_booking_application_review 
--     ADD CONSTRAINT fk_review_application 
--     FOREIGN KEY (application_id) REFERENCES course_booking_application(id);

-- ALTER TABLE course_booking_application_review 
--     ADD CONSTRAINT fk_review_teaching_group 
--     FOREIGN KEY (teaching_group_id) REFERENCES teaching_group(id);

-- 执行完成提示
SELECT '预约课多教学组审核功能数据库变更执行完成！' as result;

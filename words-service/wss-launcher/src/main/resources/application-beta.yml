# 数据源配置
spring:
    # Flyway开发环境配置
    flyway:
        # 开发环境允许清理数据库（谨慎使用）
        clean-disabled: false
        # 开发环境详细日志
        sql-migration-suffixes: .sql
        # 开发环境占位符
        placeholders:
            environment: dev
        # 开发环境特定脚本位置
        locations:
            - classpath:db/migration
            - classpath:db/dev
        enabled: false

    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
#        driverClassName: org.postgresql.Driver
        driverClassName: com.p6spy.engine.spy.P6SpyDriver
        druid:
            # 主库数据源
            master:
                url: *******************************************************************************************************************************************************************************************************************
                username: main
                password: 'Tongbu121!'
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 100
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT version()
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

    data:
        # redis 配置
        redis:
            # 地址
            host: word.121tongbu.com
            # 端口，默认为6379
            port: 19736
            # 数据库索引
            database: 0
            # 密码
            password: 'Redis123!'
            # 连接超时时间
            timeout: 10s
            lettuce:
                pool:
                    # 连接池中的最小空闲连接
                    min-idle: 0
                    # 连接池中的最大空闲连接
                    max-idle: 8
                    # 连接池的最大数据库连接数
                    max-active: 8
                    # #连接池最大阻塞等待时间（使用负值表示没有限制）
                    max-wait: -1ms
    ai:
        openai:
            # 聊天模型
            chat:
                options:
                    model: deepseek-ai/DeepSeek-R1-0528-Qwen3-8B
            api-key: sk-tnesojkqpqozrtzfcxfkvucksdfgzmgysehqmrwbaoblwedi
            base-url: https://api.siliconflow.cn/
            temperature: ${OPENAI_TEMPERATURE:0.7}
            top-p: ${OPENAI_TOP_P:1.0}
            max-tokens: ${OPENAI_MAX_TOKENS:2000}

# 阿里云OSS配置
aliyun:
    oss:
        endpoint: oss-cn-shenzhen.aliyuncs.com
        accessKeyId: LTAI5tB4PDg4LaNBgbNVpW9g
        accessKeySecret: ******************************
        bucketName: tongbu-words-dev
        urlPrefix: https://tongbu-words-dev.oss-cn-shenzhen.aliyuncs.com/

nebius:
    base-url: https://api.studio.nebius.com
    api-key: eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMDI3NjM1MTUyODMwNDIwMDc1MyIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0MDg4MiwidXVpZCI6ImRjNzJlOGFmLTdkNDctNGUxNy04MjMyLTAwYmU1Yzc4Mjc4YyIsIm5hbWUiOiJ3b3JkcyIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEwOjQxOjIyKzAwMDAifQ.T3OWY8SA_6G2DNzHaD4E2iuioBi7s56pZX7GyGQwusk
    model: Qwen/Qwen3-32B
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890

xai:
    base-url: https://api.studio.nebius.com/v1/chat/completions
    api-key: eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMDI3NjM1MTUyODMwNDIwMDc1MyIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0MDg4MiwidXVpZCI6ImRjNzJlOGFmLTdkNDctNGUxNy04MjMyLTAwYmU1Yzc4Mjc4YyIsIm5hbWUiOiJ3b3JkcyIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEwOjQxOjIyKzAwMDAifQ.T3OWY8SA_6G2DNzHaD4E2iuioBi7s56pZX7GyGQwusk
    model: deepseek-ai/DeepSeek-V3-0324
    reasoning_effort: low
    temperature: 0
    proxy:
        host: 127.0.0.1
        port: 7890

word:
    audio:
        save-path: F:\\voice
        base-url: https://dict.youdao.com/dictvoice
    enrich:
        job:
            enabled: false
        words-model: nebuis   # grok / siliconflow / nebuis，单词文本补全，默认为grok

volcengine:
    base-url: https://openspeech.bytedance.com
    speech:
        appid: 7167433942
        cluster: volcano_tts
        secretkey: Rgom2zgcp1VeySqr7oAefF8x4Et-MQTe
        access-token: lMA59dpe1S4RfAS2hJrm3Xt_xZR7L00_
        voice-type:
            us: zh_female_shuangkuaisisi_moon_bigtts
            uk: en_male_smith_mars_bigtts

clawcloud:
    url: https://iqzphfbrhgsd.ap-northeast-1.clawcloudrun.com/tts
    voice:
        us: en-US-AvaMultilingualNeural
        uk: en-GB-RyanNeural

wx:
    mp:
        job:
            enabled: false
        configs:
            - appId: wx81bac75532ac7de9
              secret: f5d423eba860626b20517882c8672e6c
              learningReportTemplateId: DQncyQJQ-KSmzn31smmI-63VT3seZwXf2KqAqx3k_iY
              startCourseTemplateId: LwucLOiZV2KRUU4UwhVxtDWIHYGUyqhO0cDKAdxESZE
        redirectUri: http://wxmapp.shamee.top/#/pages/bind/index
        learningReportDetailUrl: http://wxmapp.shamee.top/#/pages/course/report?id=%s
        reviewReportDetailUrl: http://wxmapp.shamee.top/#/pages/course/review-report?id=%s

course:
    remind:
        job:
            enabled: false
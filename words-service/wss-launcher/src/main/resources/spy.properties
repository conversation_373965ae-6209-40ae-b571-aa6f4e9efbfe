#################################################################
# P6SPY 最优实践配置 (Best Practice Configuration)
#################################################################

# 1. 驱动与数据库
# --------------------------------------------------------------
# 指定真实的 JDBC 驱动类名。请根据你的数据库修改此项。
# 这是取代旧版 `realdriver` 的推荐方式。
# MySQL 8.x+
# driverlist=com.mysql.cj.jdbc.Driver
# Oracle
# driverlist=oracle.jdbc.driver.OracleDriver
# PostgreSQL
driverlist=org.postgresql.Driver
# SQL Server
# driverlist=com.microsoft.sqlserver.jdbc.SQLServerDriver


# 2. 日志追加器 (Appender) - 推荐方式
# --------------------------------------------------------------
# 使用 Slf4jLogger，将 P6Spy 的日志输出整合到你项目现有的日志框架中 (如 Logback, Log4j2)。
# 这是最推荐的做法，可以由项目的日志配置统一管理日志级别、文件滚动、格式等。
# appender=com.p6spy.engine.spy.appender.Slf4jLogger

# 使用 Slf4jLogger 时，P6Spy 会使用名为 "p6spy" 的 logger。
# 你可以在你的 logback.xml 或 log4j2.xml 中为 "p6spy" 这个 logger 单独配置日志级别和输出目标。
# 例如，在 logback.xml 中：
# <logger name="p6spy" level="INFO" additivity="false">
#   <appender-ref ref="CONSOLE" />
#   <appender-ref ref="FILE" />
# </logger>

# 2. 日志追加器 (Appender) - 独立文件方式
# --------------------------------------------------------------
# 使用 FileLogger 将日志输出到独立的 spy.log 文件中
appender=com.p6spy.engine.spy.appender.FileLogger
logfile=spy.log
append=true # true 表示追加到文件末尾, false 表示每次重启都覆盖


# 3. 日志消息格式 (Log Message Format) - 核心
# --------------------------------------------------------------
# 使用完全自定义的格式，以获取最详尽的信息。
logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat

# 自定义日志的输出格式，这是优化的精髓所在。
# - %(executionTime): SQL执行耗时(毫秒)
# - %(category): 日志类别 (statement, commit, rollback)
# - %(effectiveSqlSingleLine): 将参数替换后的完整SQL，并强制为单行，便于日志分析
# - %(stackTrace): 打印调用此SQL的代码堆栈，精准定位SQL来源 (最强大的功能之一)
customLogMessageFormat=【P6SPY-SQL】耗时: %(executionTime)ms | %(category) | 连接ID: %(connectionId)%n--- 完整SQL:%n%(effectiveSqlSingleLine)%n--- 调用来源:%n%(stackTrace)


# 4. 性能监控 (Performance Monitoring)
# --------------------------------------------------------------
# 开启慢查询检测。当一条SQL的执行时间超过阈值时，会以ERROR级别记录一条日志。
outagedetection=true

# 设置慢查询的阈值，单位是秒。以下设置为2秒。
outagedetectioninterval=2


# 5. 其他高级配置
# --------------------------------------------------------------
# 如果SQL语句本身过长，可以开启自动格式化。但这会与 customLogMessageFormat 冲突，请二选一。
# logMessageFormat=com.p6spy.engine.spy.appender.MultiLineFormat

# JNDI 数据源名称过滤，如果使用 JNDI 数据源，可以指定需要监控的名称
# filter=true
# jndicontextcustom=java:comp/env/jdbc/yourDataSource
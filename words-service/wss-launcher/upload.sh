#/bin/bash

# 创建当前时间目录
current_time=$(date +%Y%m%d_%H%M%S)
target = "deploy/$current_time"
mkdir -p "$target"

# 将target/lib 和target/*.jar 文件复制到当前时间目录
# 判断是否存在target/lib目录，存在着复制
if [ -d "target/lib" ]; then
    cp -r target/lib "$target"
else
    echo "lib directory does not exist, skipping..."
fi

cp target/*.jar "$target"

# 上传到服务器发布目录

ssh  <EMAIL> "mkdir /root/apps/edu/words/upgrade/$current_time"

scp -r "$current_time" <EMAIL>:/root/apps/edu/words/upgrade/

echo "DONE"
-- 课消看板权限配置检查和修复脚本
-- 使用说明：在数据库中执行此脚本来检查和修复课消看板相关权限配置
-- 作者: system
-- 日期: 2025-01-09

-- ================================
-- 1. 检查现有菜单配置
-- ================================
SELECT '=== 检查课消看板菜单配置 ===' as info;

SELECT 
    menu_id,
    menu_name,
    parent_id,
    path,
    component,
    perms,
    visible,
    status
FROM sys_menu 
WHERE menu_name LIKE '%课消看板%' OR perms LIKE '%course:consumption:dashboard%'
ORDER BY menu_id;

-- ================================
-- 2. 检查角色权限分配
-- ================================
SELECT '=== 检查角色权限分配 ===' as info;

SELECT 
    r.role_name,
    r.role_key,
    m.menu_name,
    m.perms
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.perms LIKE '%course:consumption:dashboard%'
ORDER BY r.role_key, m.perms;

-- ================================
-- 3. 修复缺失的菜单（如果不存在）
-- ================================
SELECT '=== 修复缺失的菜单 ===' as info;

-- 检查并创建课消看板主菜单
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'course:consumption:dashboard:view') THEN
        INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
        VALUES (
            nextval('sys_menu_seq'),
            '课消看板',
            (SELECT menu_id FROM sys_menu WHERE menu_name = '课程管理' AND parent_id != 0 LIMIT 1),
            5,
            'consumption-dashboard',
            'course/consumption-dashboard/index',
            '',
            1,
            0,
            'C',
            '0',
            '0',
            'course:consumption:dashboard:view',
            'chart',
            'admin',
            NOW(),
            'admin',
            NOW(),
            '学生课消看板菜单'
        );
        RAISE NOTICE '已创建课消看板主菜单';
    ELSE
        RAISE NOTICE '课消看板主菜单已存在';
    END IF;
END $$;

-- 检查并创建导出权限按钮
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM sys_menu WHERE perms = 'course:consumption:dashboard:export') THEN
        INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
        VALUES (
            nextval('sys_menu_seq'),
            '课消看板导出',
            (SELECT menu_id FROM sys_menu WHERE perms = 'course:consumption:dashboard:view' LIMIT 1),
            1,
            '',
            '',
            '',
            1,
            0,
            'F',
            '0',
            '0',
            'course:consumption:dashboard:export',
            '#',
            'admin',
            NOW(),
            'admin',
            NOW(),
            '课消看板导出按钮'
        );
        RAISE NOTICE '已创建课消看板导出权限';
    ELSE
        RAISE NOTICE '课消看板导出权限已存在';
    END IF;
END $$;

-- ================================
-- 4. 修复角色权限分配
-- ================================
SELECT '=== 修复角色权限分配 ===' as info;

-- 为管理员分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'admin'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为人力分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'hr'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为销售总监分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales_director'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为销售组长分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales_group_leader'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为销售分配权限（只有查看权限）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'sales'
  AND m.perms IN ('course:consumption:dashboard:view')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为教学组长分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teaching_group_leader'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为教学组教务分配权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teaching_group_admin'
  AND m.perms IN ('course:consumption:dashboard:view', 'course:consumption:dashboard:export')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- 为教师分配权限（只有查看权限）
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 
    r.role_id,
    m.menu_id
FROM sys_role r, sys_menu m
WHERE r.role_key = 'teacher'
  AND m.perms IN ('course:consumption:dashboard:view')
  AND NOT EXISTS (
      SELECT 1 FROM sys_role_menu rm 
      WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
  );

-- ================================
-- 5. 验证配置结果
-- ================================
SELECT '=== 验证配置结果 ===' as info;

SELECT 
    r.role_name as "角色名称",
    r.role_key as "角色标识",
    COUNT(CASE WHEN m.perms = 'course:consumption:dashboard:view' THEN 1 END) as "查看权限",
    COUNT(CASE WHEN m.perms = 'course:consumption:dashboard:export' THEN 1 END) as "导出权限"
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id AND m.perms LIKE '%course:consumption:dashboard%'
WHERE r.role_key IN ('admin', 'hr', 'sales_director', 'sales_group_leader', 'sales', 'teaching_group_leader', 'teaching_group_admin', 'teacher')
GROUP BY r.role_id, r.role_name, r.role_key
ORDER BY r.role_key;

SELECT '=== 权限配置完成 ===' as info;

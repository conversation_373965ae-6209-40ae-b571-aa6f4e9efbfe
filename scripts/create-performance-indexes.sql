-- 课消看板性能优化索引创建脚本
-- 执行前请先运行 performance-analysis.sql 分析当前状态
-- 使用 CONCURRENTLY 选项避免锁表，适合生产环境

-- ================================
-- 1. 课消表核心索引
-- ================================

-- 时间范围查询的复合索引（最重要）
-- 这个索引将大幅提升按时间范围查询的性能
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consumption_time_status_deleted
ON student_course_consumption(consumption_time, status, deleted);

-- 学生和教师关联查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consumption_student_teacher
ON student_course_consumption(student_id, teacher_id);

-- 学科和课型过滤索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consumption_subject_spec
ON student_course_consumption(subject, specification);

-- 课消时间单独索引（用于排序）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_consumption_time_desc
ON student_course_consumption(consumption_time DESC);

-- ================================
-- 2. 学生表索引
-- ================================

-- 销售关联索引（用于销售权限过滤）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_student_sales_deleted
ON user_student_ext(sales_id, deleted);

-- 学生ID索引（如果不存在主键索引）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_student_id_deleted
ON user_student_ext(student_id, deleted);

-- ================================
-- 3. 教师表索引
-- ================================

-- 教师ID和删除状态索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_teacher_id_deleted
ON teacher_profile(teacher_id, deleted);

-- ================================
-- 4. 销售表索引
-- ================================

-- 销售ID和删除状态索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sales_id_deleted
ON sale_profile(sales_id, deleted);

-- ================================
-- 5. 组织关系表索引
-- ================================

-- 教学组成员索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_teaching_group_member_group_status
ON teaching_group_member(group_id, status, deleted);

-- 教学组成员的教师索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_teaching_group_member_teacher
ON teaching_group_member(teacher_id, deleted);

-- 销售组成员索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sales_group_member_group_status
ON sales_group_member(group_id, status, deleted);

-- 销售组成员的销售索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_sales_group_member_sales
ON sales_group_member(sales_id, deleted);

-- ================================
-- 6. 验证索引创建结果
-- ================================

-- 查看课消表的所有索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'student_course_consumption'
ORDER BY indexname;

-- 查看学生表的所有索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'user_student_ext'
ORDER BY indexname;

-- ================================
-- 7. 更新表统计信息
-- ================================

-- 更新统计信息以确保查询优化器使用最新的数据分布信息
ANALYZE student_course_consumption;
ANALYZE user_student_ext;
ANALYZE teacher_profile;
ANALYZE sale_profile;
ANALYZE teaching_group_member;
ANALYZE sales_group_member;

-- ================================
-- 8. 测试查询性能
-- ================================

-- 测试时间范围查询性能
EXPLAIN (ANALYZE, BUFFERS)
SELECT COUNT(*)
FROM student_course_consumption
WHERE deleted = false
  AND status = 'active'
  AND consumption_time >= CURRENT_DATE - INTERVAL '7 days'
  AND consumption_time <= CURRENT_DATE + INTERVAL '1 day';

-- 测试学生关联查询性能
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
    scc.student_id,
    COUNT(*) as consumption_count,
    SUM(scc.consumed_hours) as total_consumption
FROM student_course_consumption scc
WHERE scc.deleted = false
  AND scc.status = 'active'
  AND scc.consumption_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY scc.student_id
ORDER BY total_consumption DESC
LIMIT 100;

-- ================================
-- 9. 索引使用情况监控
-- ================================

-- 查看索引使用统计（需要运行一段时间后查看）
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
WHERE tablename IN ('student_course_consumption', 'user_student_ext', 'teacher_profile', 'sale_profile')
ORDER BY tablename, idx_scan DESC;

-- ================================
-- 10. 清理未使用的索引（谨慎执行）
-- ================================

-- 查找可能未使用的索引（idx_scan = 0）
-- 注意：新创建的索引可能显示为0，需要运行一段时间后再判断
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
  AND tablename IN ('student_course_consumption', 'user_student_ext', 'teacher_profile', 'sale_profile')
ORDER BY pg_relation_size(indexrelid) DESC;

-- ================================
-- 执行完成提示
-- ================================

SELECT 
    '索引创建完成！' as message,
    '请运行应用程序测试性能改善效果' as next_step,
    '建议监控索引使用情况，定期优化' as recommendation;

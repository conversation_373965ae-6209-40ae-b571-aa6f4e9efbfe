-- 课消看板性能分析脚本
-- 用于分析数据库性能瓶颈和优化建议

-- ================================
-- 1. 数据量统计
-- ================================
SELECT '=== 数据量统计 ===' as info;

-- 课消记录总数
SELECT 
    'student_course_consumption' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = false THEN 1 END) as active_records,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as status_active_records
FROM student_course_consumption;

-- 学生记录总数
SELECT 
    'user_student_ext' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = false THEN 1 END) as active_records
FROM user_student_ext;

-- 教师记录总数
SELECT 
    'teacher_profile' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = false THEN 1 END) as active_records
FROM teacher_profile;

-- 销售记录总数
SELECT 
    'sale_profile' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN deleted = false THEN 1 END) as active_records
FROM sale_profile;

-- ================================
-- 2. 索引分析
-- ================================
SELECT '=== 索引分析 ===' as info;

-- 检查课消表的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'student_course_consumption'
ORDER BY indexname;

-- 检查学生表的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'user_student_ext'
ORDER BY indexname;

-- ================================
-- 3. 查询性能测试
-- ================================
SELECT '=== 查询性能测试 ===' as info;

-- 测试基础查询（最近一周）
EXPLAIN ANALYZE
SELECT *
FROM student_course_consumption
WHERE deleted = false
  AND status = 'active'
  AND consumption_time >= CURRENT_DATE - INTERVAL '7 days'
  AND consumption_time <= CURRENT_DATE + INTERVAL '1 day'
LIMIT 1000;

-- 测试关联查询
EXPLAIN ANALYZE
SELECT 
    scc.*,
    use.name as student_name,
    use.phone as student_phone,
    tp.real_name as teacher_name
FROM student_course_consumption scc
LEFT JOIN user_student_ext use ON scc.student_id = use.student_id AND use.deleted = false
LEFT JOIN teacher_profile tp ON scc.teacher_id = tp.teacher_id AND tp.deleted = false
WHERE scc.deleted = false
  AND scc.status = 'active'
  AND scc.consumption_time >= CURRENT_DATE - INTERVAL '7 days'
  AND scc.consumption_time <= CURRENT_DATE + INTERVAL '1 day'
LIMIT 100;

-- ================================
-- 4. 建议的索引优化
-- ================================
SELECT '=== 建议的索引优化 ===' as info;

-- 检查是否存在推荐的索引
SELECT 
    'idx_consumption_time_status' as index_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'student_course_consumption' 
            AND indexname = 'idx_consumption_time_status'
        ) THEN '已存在'
        ELSE '需要创建'
    END as status;

SELECT 
    'idx_consumption_student_teacher' as index_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'student_course_consumption' 
            AND indexname = 'idx_consumption_student_teacher'
        ) THEN '已存在'
        ELSE '需要创建'
    END as status;

SELECT 
    'idx_student_sales' as index_name,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM pg_indexes 
            WHERE tablename = 'user_student_ext' 
            AND indexname = 'idx_student_sales'
        ) THEN '已存在'
        ELSE '需要创建'
    END as status;

-- ================================
-- 5. 创建推荐索引的SQL
-- ================================
SELECT '=== 创建推荐索引的SQL ===' as info;

-- 如果索引不存在，可以执行以下SQL创建索引：

/*
-- 课消表的复合索引（时间 + 状态 + 删除标记）
CREATE INDEX CONCURRENTLY idx_consumption_time_status 
ON student_course_consumption(consumption_time, status, deleted);

-- 课消表的学生教师索引
CREATE INDEX CONCURRENTLY idx_consumption_student_teacher 
ON student_course_consumption(student_id, teacher_id);

-- 课消表的学科课型索引
CREATE INDEX CONCURRENTLY idx_consumption_subject_spec 
ON student_course_consumption(subject, specification);

-- 学生表的销售索引
CREATE INDEX CONCURRENTLY idx_student_sales 
ON user_student_ext(sales_id, deleted);

-- 教学组成员表索引
CREATE INDEX CONCURRENTLY idx_teaching_group_member 
ON teaching_group_member(group_id, status, deleted);

-- 销售组成员表索引
CREATE INDEX CONCURRENTLY idx_sales_group_member 
ON sales_group_member(group_id, status, deleted);
*/

-- ================================
-- 6. 表统计信息更新
-- ================================
SELECT '=== 表统计信息更新 ===' as info;

-- 更新表统计信息（提高查询计划准确性）
-- ANALYZE student_course_consumption;
-- ANALYZE user_student_ext;
-- ANALYZE teacher_profile;
-- ANALYZE sale_profile;

-- ================================
-- 7. 慢查询分析
-- ================================
SELECT '=== 慢查询分析 ===' as info;

-- 检查当前数据库配置
SELECT name, setting, unit, context 
FROM pg_settings 
WHERE name IN (
    'log_min_duration_statement',
    'log_statement',
    'shared_preload_libraries',
    'track_activities',
    'track_counts'
);

-- 如果启用了pg_stat_statements，可以查看慢查询
-- SELECT query, calls, total_time, mean_time, rows
-- FROM pg_stat_statements
-- WHERE query LIKE '%student_course_consumption%'
-- ORDER BY total_time DESC
-- LIMIT 10;

-- ================================
-- 8. 连接池和并发分析
-- ================================
SELECT '=== 连接池和并发分析 ===' as info;

-- 当前活跃连接数
SELECT 
    state,
    COUNT(*) as connection_count
FROM pg_stat_activity
WHERE datname = current_database()
GROUP BY state;

-- 最大连接数配置
SELECT name, setting, unit 
FROM pg_settings 
WHERE name = 'max_connections';

-- ================================
-- 9. 内存使用分析
-- ================================
SELECT '=== 内存使用分析 ===' as info;

-- 内存相关配置
SELECT name, setting, unit 
FROM pg_settings 
WHERE name IN (
    'shared_buffers',
    'work_mem',
    'maintenance_work_mem',
    'effective_cache_size'
);

-- ================================
-- 10. 优化建议总结
-- ================================
SELECT '=== 优化建议总结 ===' as info;

SELECT 
    '1. 创建复合索引' as recommendation,
    'CREATE INDEX idx_consumption_time_status ON student_course_consumption(consumption_time, status, deleted)' as sql_command;

SELECT 
    '2. 创建关联索引' as recommendation,
    'CREATE INDEX idx_consumption_student_teacher ON student_course_consumption(student_id, teacher_id)' as sql_command;

SELECT 
    '3. 创建销售索引' as recommendation,
    'CREATE INDEX idx_student_sales ON user_student_ext(sales_id, deleted)' as sql_command;

SELECT 
    '4. 更新表统计信息' as recommendation,
    'ANALYZE student_course_consumption; ANALYZE user_student_ext;' as sql_command;

SELECT 
    '5. 启用慢查询日志' as recommendation,
    'ALTER SYSTEM SET log_min_duration_statement = 1000; -- 记录超过1秒的查询' as sql_command;

SELECT 
    '6. 优化查询逻辑' as recommendation,
    '使用批量查询代替N+1查询，减少数据库往返次数' as sql_command;

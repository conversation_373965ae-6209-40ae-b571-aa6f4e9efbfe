#!/bin/bash

# 课消看板功能测试脚本
# 使用说明：在项目根目录执行此脚本来测试课消看板功能
# 作者: system
# 日期: 2025-01-09

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
BASE_URL="http://localhost:8080"
API_PREFIX="/course-consumption/dashboard"

# 测试用户token（需要根据实际情况修改）
ADMIN_TOKEN=""
SALES_TOKEN=""
TEACHER_TOKEN=""

echo -e "${BLUE}=== 课消看板功能测试 ===${NC}"
echo "测试服务器: $BASE_URL"
echo "API前缀: $API_PREFIX"
echo ""

# 检查服务器是否运行
check_server() {
    echo -e "${YELLOW}检查服务器状态...${NC}"
    if curl -s --connect-timeout 5 "$BASE_URL/actuator/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 服务器运行正常${NC}"
        return 0
    else
        echo -e "${RED}✗ 服务器未运行或无法连接${NC}"
        echo "请确保后端服务已启动"
        return 1
    fi
}

# 测试获取时间范围选项
test_time_ranges() {
    echo -e "${YELLOW}测试获取时间范围选项...${NC}"
    
    response=$(curl -s -w "%{http_code}" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        "$BASE_URL$API_PREFIX/time-ranges")
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 时间范围接口测试通过${NC}"
        echo "响应数据: $body" | head -c 200
        echo "..."
    else
        echo -e "${RED}✗ 时间范围接口测试失败 (HTTP $http_code)${NC}"
        echo "响应: $body"
    fi
    echo ""
}

# 测试获取看板数据
test_dashboard_data() {
    local token=$1
    local role=$2
    
    echo -e "${YELLOW}测试获取看板数据 ($role)...${NC}"
    
    # 构建请求数据
    local today=$(date +%Y-%m-%d)
    local week_ago=$(date -d '7 days ago' +%Y-%m-%d)
    
    local request_data='{
        "startDate": "'$week_ago'",
        "endDate": "'$today'",
        "timeRangeType": "custom"
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $token" \
        -d "$request_data" \
        "$BASE_URL$API_PREFIX/data")
    
    http_code="${response: -3}"
    body="${response%???}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 看板数据接口测试通过 ($role)${NC}"
        
        # 解析响应数据
        total_students=$(echo "$body" | grep -o '"totalStudents":[0-9]*' | cut -d':' -f2)
        total_consumption=$(echo "$body" | grep -o '"totalConsumption":[0-9.]*' | cut -d':' -f2)
        
        echo "  - 总学生数: ${total_students:-0}"
        echo "  - 总课消: ${total_consumption:-0}"
    else
        echo -e "${RED}✗ 看板数据接口测试失败 ($role) (HTTP $http_code)${NC}"
        echo "响应: $body" | head -c 200
    fi
    echo ""
}

# 测试权限控制
test_permissions() {
    echo -e "${YELLOW}测试权限控制...${NC}"
    
    # 测试不同角色的数据访问权限
    if [ -n "$ADMIN_TOKEN" ]; then
        test_dashboard_data "$ADMIN_TOKEN" "管理员"
    else
        echo -e "${YELLOW}跳过管理员权限测试 (未提供token)${NC}"
    fi
    
    if [ -n "$SALES_TOKEN" ]; then
        test_dashboard_data "$SALES_TOKEN" "销售"
    else
        echo -e "${YELLOW}跳过销售权限测试 (未提供token)${NC}"
    fi
    
    if [ -n "$TEACHER_TOKEN" ]; then
        test_dashboard_data "$TEACHER_TOKEN" "教师"
    else
        echo -e "${YELLOW}跳过教师权限测试 (未提供token)${NC}"
    fi
}

# 测试数据过滤
test_data_filtering() {
    echo -e "${YELLOW}测试数据过滤功能...${NC}"
    
    # 测试学科过滤
    local request_data='{
        "startDate": "'$(date -d '30 days ago' +%Y-%m-%d)'",
        "endDate": "'$(date +%Y-%m-%d)'",
        "timeRangeType": "custom",
        "subject": "英语"
    }'
    
    response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d "$request_data" \
        "$BASE_URL$API_PREFIX/data")
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 学科过滤测试通过${NC}"
    else
        echo -e "${RED}✗ 学科过滤测试失败 (HTTP $http_code)${NC}"
    fi
    echo ""
}

# 性能测试
test_performance() {
    echo -e "${YELLOW}测试接口性能...${NC}"
    
    local request_data='{
        "startDate": "'$(date -d '90 days ago' +%Y-%m-%d)'",
        "endDate": "'$(date +%Y-%m-%d)'",
        "timeRangeType": "custom"
    }'
    
    start_time=$(date +%s%N)
    
    response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -d "$request_data" \
        "$BASE_URL$API_PREFIX/data")
    
    end_time=$(date +%s%N)
    duration=$(( (end_time - start_time) / 1000000 )) # 转换为毫秒
    
    http_code="${response: -3}"
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✓ 性能测试通过${NC}"
        echo "  - 响应时间: ${duration}ms"
        
        if [ $duration -lt 5000 ]; then
            echo -e "${GREEN}  - 性能良好 (<5秒)${NC}"
        elif [ $duration -lt 10000 ]; then
            echo -e "${YELLOW}  - 性能一般 (5-10秒)${NC}"
        else
            echo -e "${RED}  - 性能较差 (>10秒)${NC}"
        fi
    else
        echo -e "${RED}✗ 性能测试失败 (HTTP $http_code)${NC}"
    fi
    echo ""
}

# 主测试流程
main() {
    echo -e "${BLUE}开始测试...${NC}"
    echo ""
    
    # 检查服务器
    if ! check_server; then
        exit 1
    fi
    echo ""
    
    # 检查token配置
    if [ -z "$ADMIN_TOKEN" ] && [ -z "$SALES_TOKEN" ] && [ -z "$TEACHER_TOKEN" ]; then
        echo -e "${YELLOW}警告: 未配置测试token，部分测试将被跳过${NC}"
        echo "请在脚本中设置 ADMIN_TOKEN, SALES_TOKEN, TEACHER_TOKEN 变量"
        echo ""
    fi
    
    # 执行测试
    test_time_ranges
    test_permissions
    test_data_filtering
    test_performance
    
    echo -e "${BLUE}=== 测试完成 ===${NC}"
    echo ""
    echo -e "${YELLOW}注意事项:${NC}"
    echo "1. 请确保数据库中有课消测试数据"
    echo "2. 请根据实际情况配置测试用户token"
    echo "3. 如有测试失败，请检查后端日志"
    echo "4. 建议在测试环境中运行此脚本"
}

# 执行主函数
main "$@"

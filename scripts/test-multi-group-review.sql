-- 多教学组审核功能测试脚本
-- 创建时间：2025-01-12
-- 说明：用于测试多教学组审核功能的SQL脚本

-- =====================================================
-- 1. 检查新表是否创建成功
-- =====================================================

-- 检查审核记录表是否存在
SELECT 
    table_name,
    table_comment
FROM information_schema.tables 
WHERE table_schema = 'words' 
  AND table_name = 'course_booking_application_review';

-- 检查表结构
DESCRIBE course_booking_application_review;

-- =====================================================
-- 2. 准备测试数据
-- =====================================================

-- 查看现有的教学组
SELECT 
    id,
    name,
    leader_id,
    status
FROM teaching_group 
WHERE deleted = false 
  AND status = 'active'
ORDER BY name;

-- 查看教学组成员
SELECT 
    tg.name as group_name,
    tgm.teacher_id,
    tp.real_name as teacher_name,
    tgm.role_type,
    tgm.status
FROM teaching_group_member tgm
JOIN teaching_group tg ON tgm.group_id = tg.id
LEFT JOIN teacher_profile tp ON tgm.teacher_id = tp.teacher_id
WHERE tgm.deleted = false 
  AND tgm.status = 'active'
  AND tg.deleted = false
ORDER BY tg.name, tp.real_name;

-- 查看待审核的申请
SELECT 
    id,
    student_id,
    subject,
    specification,
    preferred_teachers,
    status,
    create_time
FROM course_booking_application 
WHERE deleted = false 
  AND status = '待审核'
ORDER BY create_time DESC
LIMIT 10;

-- =====================================================
-- 3. 测试场景数据查询
-- =====================================================

-- 场景1：查看某个申请的候选老师所属教学组
WITH application_teachers AS (
    SELECT 
        id as application_id,
        unnest(preferred_teachers) as teacher_id
    FROM course_booking_application 
    WHERE id = 'YOUR_APPLICATION_ID' -- 替换为实际的申请ID
)
SELECT 
    at.application_id,
    at.teacher_id,
    tp.real_name as teacher_name,
    tg.id as group_id,
    tg.name as group_name,
    tg.leader_id
FROM application_teachers at
LEFT JOIN teacher_profile tp ON at.teacher_id = tp.teacher_id
LEFT JOIN teaching_group_member tgm ON at.teacher_id = tgm.teacher_id 
    AND tgm.deleted = false 
    AND tgm.status = 'active'
LEFT JOIN teaching_group tg ON tgm.group_id = tg.id 
    AND tg.deleted = false
ORDER BY tg.name;

-- =====================================================
-- 4. 审核记录查询
-- =====================================================

-- 查看某个申请的所有审核记录
SELECT 
    r.id,
    r.application_id,
    tg.name as teaching_group_name,
    tp.real_name as reviewer_name,
    r.review_result,
    r.review_comment,
    r.rejection_reason,
    r.assigned_teacher_id,
    atp.real_name as assigned_teacher_name,
    r.review_time
FROM course_booking_application_review r
LEFT JOIN teaching_group tg ON r.teaching_group_id = tg.id
LEFT JOIN teacher_profile tp ON r.reviewer_id = tp.teacher_id
LEFT JOIN teacher_profile atp ON r.assigned_teacher_id = atp.teacher_id
WHERE r.application_id = 'YOUR_APPLICATION_ID' -- 替换为实际的申请ID
  AND r.deleted = false
ORDER BY r.review_time;

-- 查看所有审核记录统计
SELECT 
    DATE(r.review_time) as review_date,
    tg.name as teaching_group_name,
    r.review_result,
    COUNT(*) as count
FROM course_booking_application_review r
LEFT JOIN teaching_group tg ON r.teaching_group_id = tg.id
WHERE r.deleted = false
  AND r.review_time >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY DATE(r.review_time), tg.name, r.review_result
ORDER BY review_date DESC, tg.name, r.review_result;

-- =====================================================
-- 5. 测试数据验证查询
-- =====================================================

-- 验证审核逻辑：检查申请状态与审核记录的一致性
SELECT 
    a.id as application_id,
    a.status as application_status,
    COUNT(r.id) as review_count,
    COUNT(CASE WHEN r.review_result = '已通过' THEN 1 END) as approved_count,
    COUNT(CASE WHEN r.review_result = '已拒绝' THEN 1 END) as rejected_count,
    -- 计算相关教学组数量
    (SELECT COUNT(DISTINCT tgm.group_id)
     FROM teaching_group_member tgm
     WHERE tgm.teacher_id = ANY(a.preferred_teachers)
       AND tgm.deleted = false 
       AND tgm.status = 'active') as related_group_count
FROM course_booking_application a
LEFT JOIN course_booking_application_review r ON a.id = r.application_id 
    AND r.deleted = false
WHERE a.deleted = false
  AND a.create_time >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY a.id, a.status, a.preferred_teachers
HAVING COUNT(r.id) > 0
ORDER BY a.create_time DESC;

-- =====================================================
-- 6. 清理测试数据（谨慎使用）
-- =====================================================

-- 清理测试审核记录（仅在测试环境使用）
-- DELETE FROM course_booking_application_review 
-- WHERE application_id IN ('TEST_APPLICATION_ID_1', 'TEST_APPLICATION_ID_2');

-- 重置测试申请状态（仅在测试环境使用）
-- UPDATE course_booking_application 
-- SET status = '待审核', 
--     approval_by = NULL, 
--     approval_time = NULL, 
--     rejection_reason = NULL,
--     update_time = CURRENT_TIMESTAMP
-- WHERE id IN ('TEST_APPLICATION_ID_1', 'TEST_APPLICATION_ID_2');

-- =====================================================
-- 7. 性能测试查询
-- =====================================================

-- 检查索引使用情况
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM course_booking_application_review 
WHERE application_id = 'YOUR_APPLICATION_ID' 
  AND deleted = false;

EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM course_booking_application_review 
WHERE teaching_group_id = 'YOUR_GROUP_ID' 
  AND deleted = false;

-- 检查查询性能
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename = 'course_booking_application_review'
ORDER BY idx_scan DESC;
